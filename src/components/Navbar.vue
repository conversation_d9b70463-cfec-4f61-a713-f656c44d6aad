<template>
  <nav>
    <!-- :clipped="$vuetify.breakpoint.lgAndUp" -->
    <v-navigation-drawer v-model="drawer" app>
      <v-list dense>
        <template v-for="(item, i) in items">
          <v-row v-if="item.heading" :key="i" align="center"> </v-row>

          <v-divider
            v-else-if="item.divider"
            :key="i"
            dark
            class="my-4"
          ></v-divider>

          <!-- :prepend-icon="item.model ? item.icon : item['icon-alt']"
            append-icon="" -->
          <v-list-group
            v-else-if="item.children"
            :key="i"
            v-model="item.model"
            prepend-icon="view_list"
            no-action
          >
            <template v-slot:activator>
              <v-list-item-content>
                <v-list-item-title>
                  {{ item.text }}
                </v-list-item-title>
              </v-list-item-content>
            </template>
            <v-list-item
              v-for="(child, i) in item.children"
              :key="i"
              :to="child.route"
            >
              <v-list-item-action>
                <v-icon>{{ child.icon }}</v-icon>
              </v-list-item-action>
              <v-list-item-content>
                <v-list-item-title>
                  {{ child.text }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list-group>

          <v-list-item v-else :key="item.text" :to="item.route">
            <v-list-item-action>
              <v-icon>{{ item.icon }}</v-icon>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>
                {{ item.text }}
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </template>

        <template v-for="item of items">
          <template v-if="item.items != null">
            <v-list-group :key="item.title" no-action v-model="item.active">
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title>
                    {{ item.title }}
                  </v-list-item-title>
                </v-list-item-content>
              </template>
              <div>
                <div v-for="child in item.items" :key="child.title">
                  <template v-if="child.subitems !== null">
                    <v-list-group
                      sub-group
                      no-action
                      :key="child.id"
                      prepend-icon="m"
                      v-model="child.subactive"
                    >
                      <template v-slot:activator>
                        <v-list-item-title v-text="child.title">
                          <i class="mr-2" :class="child.action"></i>
                        </v-list-item-title>
                      </template>
                      <template v-for="subchild in child.subitems">
                        <v-list-item :key="subchild.id" :to="subchild.route">
                          <v-list-item-title
                            v-text="subchild.text"
                          ></v-list-item-title>
                        </v-list-item>
                      </template>
                    </v-list-group>
                  </template>
                </div>
              </div>
            </v-list-group>
          </template>
        </template>
      </v-list>

      <v-divider></v-divider>
      
    </v-navigation-drawer>
    <!-- :clipped-left="$vuetify.breakpoint.lgAndUp" -->
    <v-app-bar app color="blue darken-3" dark>
      <v-app-bar-nav-icon @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
      <v-toolbar-title style="width: 300px" class="ml-0 pl-4">
        <span class="hidden-sm-and-down">Dashboard</span>
      </v-toolbar-title>

<!-- <v-checkbox
        v-model="collapseOnScroll"
        color="white"
        hide-details
      ></v-checkbox> -->

      <!-- <v-app-bar-nav-icon>
 <v-btn href="/" icon small v-bind="attrs" v-on="on">
								<v-icon>mdi-logout-variant</v-icon>
							</v-btn>
               <span>Logout</span>
      </v-app-bar-nav-icon> -->
       
      <!-- <v-text-field
        flat
        solo-inverted
        hide-details
        prepend-inner-icon="mdi-magnify"
        label="Search"
        class="hidden-sm-and-down"
      ></v-text-field>
      <v-spacer></v-spacer>
      <v-btn icon>
        <v-icon>mdi-apps</v-icon>
      </v-btn>
      <v-btn icon>
        <v-icon>mdi-bell</v-icon>
      </v-btn>
      <v-btn icon large>
        <v-avatar size="32px" item>
          <v-img
            src="https://cdn.vuetifyjs.com/images/logos/logo.svg"
            alt="Vuetify"
          ></v-img
        ></v-avatar>
      </v-btn> -->
      <v-spacer></v-spacer>
     <v-list-item-action key="open-history">
                <v-tooltip left>
                  <template v-slot:activator="{on, attrs}">
							<v-btn icon small v-bind="attrs" v-on="on" @click="Logout" >
								<v-icon>mdi-logout-variant</v-icon>
							</v-btn>
						</template>
                  <span>Logout</span>
                </v-tooltip>
              </v-list-item-action>
    </v-app-bar>
  
  </nav>
</template>

<script>
export default {
  props: {
    source: String,
  },
  data: () => ({
    drawer: null,
    items: [
      { icon: "home", text: "Home", route: "home" },
      { divider: true },
      { icon: "link", text: "Oils", route: "oil" },
      { icon: "link", text: "Petrol", route: "Petrol" },
      { icon: "link", text: "O change", route: "o_change" },
      { icon: "link", text: "ProductDetails", route: "productDetails" },
      { icon: "link", text: "Check stock", route: "checkstock" },
      { icon: "link", text: "OA", route: "oa" },
      { divider: true },
      { icon: "mdi-table", text: "Form Table", route: "form_table" },
      { divider: true },
      // D_to_Gu
      {
        text: "Logistics",
        model: false,
        children: [
          { icon: "link", text: "Logistic", route: "logistic" },
          { icon: "link", text: "Logistic2", route: "logistic2" },
          { icon: "link", text: "Logistic3", route: "Logistic3" },
        ],
      },
      // D_to_Gu
      {
        text: "D_to_Gu",
        model: false,
        children: [
          { icon: "", text: "D_to_Gu", route: "d_to_gu" },
          { icon: "", text: "New Product", route: "newproduct" },
        ],
      },
      // Order
      {
        text: "Order",
        model: false,
        children: [
          { icon: "", text: "Follow Items", route: "followitems" },
          { icon: "", text: "Order items", route: "orderitems" },
          { icon: "", text: "Order items G", route: "Orderitemsg" },
          { icon: "", text: "Order items D", route: "Orderitemsd" },
          { icon: "", text: "Compare", route: "compare" },
        ],
      },
      // Order
      {
        text: "In order",
        model: false,
        children: [
          { icon: "", text: "in_order", route: "in_order" },
          { icon: "", text: "in_order_list", route: "in_order_list" },
          { icon: "", text: "in_order_judy", route: "in_order_judy" },
        ],
      },
      //upload app
      { icon: "mdi-folder-upload", text: "Upload App", route: "Upload_app" },
      // dropdown2
      {
        title: "NewFile",
        items: [
          {
            title: "List",
            subitems: [
              { icon: "", text: "NewFile", route: "NewFile" },
              { icon: "", text: "NewFile2", route: "NewFile2" },
            ],
          },
        ],
      },
    ],
  }),
  methods: {
    Logout() {
      console.log("A")
      localStorage.removeItem("access_token");
      this.$router.go("/");
    },
  }
};
</script>
