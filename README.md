# vuetify_web

## Project setup
```
yarn install
```

### Compiles and hot-reloads for development
```
yarn serve
npm run serve
```

### Compiles and minifies for production
```
yarn build
```

### Lints and fixes files
```
yarn lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

<!-- -------------------------------------------->

sudo docker-compose up --build


docker system prune -a


sudo docker run -t shwethe_web .
echo "nameserver 8.8.8.8" | sudo tee /etc/resolv.conf > /dev/null

sudo docker build -t shwethe_web .
sudo docker run  -it -p 80:80  shwethe_web

sudo chmod -R 777 ./


<!-- -------------------------------------------->

"serve": "vue-cli-service serve",
"serve": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve",