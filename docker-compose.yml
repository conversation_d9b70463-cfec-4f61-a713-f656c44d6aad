# for local development
version: '2.4'
services:
  frontend:
    build:
      context: .
      target: 'develop-stage'
    restart: always
    ports:
      - '8200:8080'
      # volumes:
      # - '.:/app'
    volumes:
      - .:/var/www/docker-vue
      - /var/www/docker-vue/node_modules
    command: /bin/sh -c "yarn serve"

# sudo docker-compose up --build


# version: '3'
# services:
#   frontend:
#     build:
#       context: .
#       dockerfile: Dockerfile
#     restart: always
#     ports:
#       - "8200:8080"
#     container_name: frontend
#     volumes:
#        - .:/usr/src/app/my-app
#        - /usr/src/app/my-app/node_modules