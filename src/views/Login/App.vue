<template>
  <v-app>
    <v-dialog v-model="dialog" persistent max-width="600px" min-width="360px">
      <div>
        <v-tabs
          v-model="tab"
          show-arrows
          background-color="deep-purple accent-4"
          icons-and-text
          dark
          grow
        >
          <v-tabs-slider color="purple darken-4"></v-tabs-slider>
          <v-tab v-for="i in tabs" :key="i">
            <v-icon large>{{ i.icon }}</v-icon>
            <div class="caption py-1">{{ i.name }}</div>
          </v-tab>
          <v-tab-item>
            <v-card class="px-4">
              <v-card-text>
                <v-form ref="loginForm" v-model="valid" lazy-validation>
                  <v-row>
                    <v-col cols="12">
                      <v-text-field
                        v-model="loginEmail"
                        :rules="loginEmailRules"
                        label="E-mail"
                        required
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="loginPassword"
                        :append-icon="show1 ? 'eye' : 'eye-off'"
                        :rules="[rules.required, rules.min]"
                        :type="show1 ? 'text' : 'password'"
                        name="input-10-1"
                        label="Password"
                        hint="At least 8 characters"
                        counter
                        @click:append="show1 = !show1"
                      ></v-text-field>
                    </v-col>
                    <v-col class="d-flex" cols="12" sm="6" xsm="12"> </v-col>
                    <v-spacer></v-spacer>
                    <v-col class="d-flex" cols="12" sm="3" xsm="12" align-end>
                      <v-btn
                        x-large
                        block
                        :disabled="!valid"
                        color="success"
                        @click="validate"
                      >
                        Login
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-form>
              </v-card-text>
            </v-card>
          </v-tab-item>
          <!-- <v-tab-item>
                        <v-card class="px-4">
                            <v-card-text>
                                <v-form ref="registerForm" v-model="valid" lazy-validation>
                                    <v-row>
                                        <v-col cols="12" sm="6" md="6">
                                            <v-text-field v-model="firstName" :rules="[rules.required]" label="First Name" maxlength="20" required></v-text-field>
                                        </v-col>
                                        <v-col cols="12" sm="6" md="6">
                                            <v-text-field v-model="lastName" :rules="[rules.required]" label="Last Name" maxlength="20" required></v-text-field>
                                        </v-col>
                                        <v-col cols="12">
                                            <v-text-field v-model="email" :rules="emailRules" label="E-mail" required></v-text-field>
                                        </v-col>
                                        <v-col cols="12">
                                            <v-text-field v-model="password" :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'" :rules="[rules.required, rules.min]" :type="show1 ? 'text' : 'password'" name="input-10-1" label="Password" hint="At least 8 characters" counter @click:append="show1 = !show1"></v-text-field>
                                        </v-col>
                                        <v-col cols="12">
                                            <v-text-field block v-model="verify" :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'" :rules="[rules.required, passwordMatch]" :type="show1 ? 'text' : 'password'" name="input-10-1" label="Confirm Password" counter @click:append="show1 = !show1"></v-text-field>
                                        </v-col>
                                        <v-spacer></v-spacer>
                                        <v-col class="d-flex ml-auto" cols="12" sm="3" xsm="12">
                                            <v-btn x-large block :disabled="!valid" color="success" @click="validate">Register</v-btn>
                                        </v-col>
                                    </v-row>
                                </v-form>
                            </v-card-text>
                        </v-card>
                    </v-tab-item> -->
        </v-tabs>
      </div>
    </v-dialog>
  </v-app>
</template>

<script>
import { HTTP } from "../../plugins/http";
export default {
  data() {
    return {
      dialog: true,
      tab: 0,
      // tabs: [
      //     {name:"Login", icon:"mdi-account"},
      //     {name:"Register", icon:"mdi-account-outline"}
      // ],
      tabs: [{ name: "Login", icon: "mdi-account" }],
      valid: true,

      firstName: "",
      lastName: "",
      email: "",
      password: "",
      verify: "",
      loginPassword: "",
      loginEmail: "",
      loginEmailRules: [
        v => !!v || "Required"
        // v => /.+@.+\..+/.test(v) || "E-mail must be valid"
      ],
      emailRules: [
        v => !!v || "Required"
        // v => /.+@.+\..+/.test(v) || "E-mail must be valid"
      ],

      show1: false,
      rules: {
        required: value => !!value || "Required.",
        min: v => (v && v.length >= 2) || "Min 8 characters"
      }
    };
  },
  computed: {
    passwordMatch() {
      return () => this.password === this.verify || "Password must match";
    }
  },
  methods: {
    //     getUser() {
    //         try {
    //           HTTP.post("login",{ 'name':this.email , 'pass': this.password }).then(
    //                 function (response){
    //                 console.log(response)
    //                 });
    //           console.log(response);
    //         } catch (error) {
    //           console.error(error);
    //         }
    // },
    validate() {
      if (this.$refs.loginForm.validate()) {
        // submit form to server/API here...
        // console.log(this.loginPassword)
        // console.log(this.loginEmail)
        HTTP.post("sigin_sigout_api/login", {
          name: this.loginEmail,
          pass: this.loginPassword
        }).then(response => {
          console.log(response.data.access_token);
          if (response.data.access_token) {
            localStorage.setItem("access_token", response.data.access_token);
          } else {
            console.log("AAAA");
          }
          this.$router.go("/dashboard");
        });
        // this.$router.push("/dashboard");
      }
    },
    reset() {
      this.$refs.form.reset();
    },
    resetValidation() {
      this.$refs.form.resetValidation();
    },
    LoginForm() {
      this.$refs.form.validate();
      console.log("LoginForm");

      // HTTP.post("sigin_sigout_api/login",{ 'name':this.loginEmail , 'pass': this.loginPassword }).then(response => {
      //     console.log(response.data.access_token)
      //     localStorage.setItem('access_token', response.data.access_token)
      //     this.$router.push("/dashboard");

      // });

      // this.$refs.form.reset();
      // this.$router.push("/dashboard");
    }
  }
};
</script>

<style lang="scss" scoped>
.center {
  display: grid;
  height: 100vh;
  place-items: center;
}
.width {
  width: 30%;
}
/* bg-image */
.myDiv {
  position: relative;
  z-index: 1;
  background-color: white;
}
.myDiv .bg {
  position: absolute;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  // background-image: url("~@/assets/Flying_Whale_by_Shu_Le.jpg");
  opacity: 0.1;
  width: 100%;
  height: 100%;
}
@media only screen and (max-width: 600px) {
  .width {
    width: 90%;
  }
}
@media only screen and (min-width: 600px) and (max-width: 1264px) {
  .width {
    width: 70%;
  }
}
</style>
