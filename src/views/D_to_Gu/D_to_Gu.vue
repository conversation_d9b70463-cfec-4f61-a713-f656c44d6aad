<template>
  <v-container>
    <br />
    <br />
    <a-input-search
      placeholder="Search"
      style="width: 250px"
      @search="onSearch"
      enterButton
    />
    <br />
    <br />
    <a-table :columns="columns" :dataSource="data" bordered>
      <template slot-scope="text, record" slot="operation">
        <div class="editable-row-operations">
          <span>
            <a-button
              :disabled="editingKey !== ''"
              @click="showModal(record)"
              type="primary"
              >Edit</a-button
            >
          </span>
        </div>
      </template>
    </a-table>
    <a-modal
      title="Title"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <p hidden>{{ ModalID }}</p>
      <p>
        <b>{{ ModelIdname }} | {{ ModelMmname }}</b>
      </p>
      <br />
      <a-select
        show-search
        :value="value"
        placeholder="Search"
        style="width: 200px"
        :default-active-first-option="false"
        :show-arrow="false"
        :filter-option="false"
        :not-found-content="null"
        @search="handleSearch"
        @change="handleChange"
      >
        <a-select-option v-for="d in data_1" :key="d.value">{{
          d.text
        }}</a-select-option>
      </a-select>
    </a-modal>
  </v-container>
</template>

<script>
import { HTTP } from "../../plugins/http";

function fetch(value, callback) {
  const data = [{ value: "ကြွေပြား", text: "ကြွေပြား" }];
  callback(data);
}
const columns = [
  {
    title: "fen",
    dataIndex: "fen",
    // width: "15%",
    scopedSlots: { customRender: "fen" },
  },
  {
    title: "id",
    dataIndex: "id",
    // width: "15%",
    scopedSlots: { customRender: "id" },
  },
  {
    title: "idname",
    dataIndex: "idname",
    // width: "15%",
    scopedSlots: { customRender: "idname" },
  },
  {
    title: "mm_name",
    dataIndex: "mm_name",
    // width: "40%",
    scopedSlots: { customRender: "mm_name" },
  },
  {
    title: "d_name",
    dataIndex: "d_name",
    // width: "40%",
    scopedSlots: { customRender: "d_name" },
  },
  {
    title: "edit",
    dataIndex: "operation",
    scopedSlots: { customRender: "operation" },
  },
];
const data = [];
export default {
  data() {
    this.cacheData = data.map((item) => ({ ...item }));
    return {
      data,
      data_1: [],
      columns,
      editingKey: "",
      value: undefined,
      ModalText: null,
      visible: false,
      confirmLoading: false,
      search_data: "",
    };
  },
  created: function() {
    HTTP.get("d_to_gu").then(
      (response) => (
        console.log(response.data),
        (this.data = response.data)
        //  this.columns = response.data.columns
      )
    );
  },
  methods: {
    // Search input
    onSearch(value) {
      console.log(value);
      this.search_data = value;
      let data = { search: value };
      HTTP.post("d_to_gu_search", data).then(
        (response) => (
          console.log(response.data),
          (this.data = response.data)
          //  this.columns = response.data.columns
        )
      );
    },
    // Model Edit
    showModal(record) {
      console.log(record);
      this.visible = true;
      this.ModalID = record.id;
      this.ModalText = record.id;

      this.ModelIdname = record.idname;
      this.ModelMmname = record.mm_name;
    },
    handleOk() {
      // this.ModalText = "The modal will be closed after two seconds";
      this.confirmLoading = true;
      console.log(this.value);
      let data = [{ detail: this.value, id: this.ModalText }];
      HTTP.post("d_to_gu_insert", data).then();
      if (this.search_data != "") {
        console.log("print 1");
        let search_data_1 = { search: this.search_data };
        HTTP.post("d_to_gu_search", search_data_1).then(
          (response) => (
            console.log(response.data),
            (this.data = response.data)
            //  this.columns = response.data.columns
          )
        );
      } else {
        console.log("print 2");
        HTTP.get("d_to_gu").then(
          (response) => (
            console.log(response.data),
            (this.data = response.data)
            //  this.columns = response.data.columns
          )
        );
      }
      setTimeout(() => {
        this.visible = false;
        this.confirmLoading = false;
      }, 5000);
    },
    handleCancel() {
      console.log("Clicked cancel button");
      this.visible = false;
    },
    // search input in model
    handleSearch(value) {
      fetch(value, (data_1) => (this.data_1 = data_1));
    },
    handleChange(value) {
      console.log(value);
      this.value = value;
      fetch(value, (data_1) => (this.data_1 = data_1));
    },
  },
};
</script>
