<template>
  <v-app>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Oil
            <v-spacer></v-spacer>
            <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
            ></v-text-field>
          </v-card-title>
          <v-form ref="form" v-model="valid_2" autocomplete="off">
            <v-container>
              <v-row>
                <v-col cols="5">
                  <v-text-field
                    label="idname"
                    v-model="che_liang"
                    @keyup="_uppercase_5"
                    @keyup.enter="uppercase_5"
                    outlined
                    :rules="rules.lock"
                    ref="che_liang"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="5">
                  <v-text-field
                    label="name"
                    v-model="che_liang_mmname"
                    outlined
                    :rules="rules.lock"
                    disabled
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="2">
                  <v-btn
                    @click="submitCar"
                    :disabled="!valid_2"
                    color="success"
                    class="mr-4"
                  >
                    confirm
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50]
            }"
            :search="search"
            :headers="headers_3"
            :items="desserts_3"
            :sort-by="['', '']"
            multi-sort
          >
            <template v-slot:top>
                <v-dialog v-model="dialog" hide-overlay max-width="30%">
                  <v-card>
                    <v-card-title class="headline"></v-card-title>

                    <v-card-text>
                      <p>
                        <b> Phone Number</b>
                      </p>

                      <v-text-field
                        v-model="phone_number"
                        label="Phone Number"
                        type="input"
                        
                      ></v-text-field>
                      <v-btn small color="primary" @click="phone_data()"
                        >Summit</v-btn
                      >
                      <!-- <v-text-field
                        v-model="editedItem.product_price"
                        label="product_price"
                        disabled
                      ></v-text-field>
                      <v-text-field
                        v-model="editedItem.product_idname"
                        label="product_idname"
                        disabled
                      ></v-text-field>
                      <v-text-field
                        v-model="editedItem.product_mm_name"
                        label="product_mm_name"
                        disabled
                      ></v-text-field>
                      <v-text-field
                        v-model="editedItem.product_d_name"
                        label="product_d_name"
                        disabled
                      ></v-text-field> -->

                      <!-- <v-btn @click="searchOrder" color="success" class="mr-4">
                        Search
                      </v-btn> -->

                      <!-- <pre>{{ answer }}</pre> -->
                    </v-card-text>
                  </v-card>
                </v-dialog>
              </template>
            <template v-slot:item.edit="{ item }">
              <v-btn small color="primary" @click="editItem(item)"
                >Add Phone</v-btn
              >
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
    <template>
      <v-snackbar :color="snackbar.color" v-model="snackbar.show">
        {{ snackbar.message }}
      </v-snackbar>
    </template>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </v-app>
</template>

<script>
import { HTTP_shwethe_n } from "../../../../plugins/http";

export default {
  data() {
    return {

      data_record:[],
      dialog: false,
      phone_number: "",
      checK_idname: false,
      overlay: false,
      output: null,
      search: "",
      che_liang: null,
      che_liang_id: null,
      che_liang_mmname: null,
      valid: true,
      valid_2: true,
      snackbar: {
        show: false,
        message: null,
        color: null
      },

      headers: [
        // { text: "id", value: "id" },

        { text: "bill_id", value: "bill_id" },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "bill_id", value: "edit" },
        { text: "che_ci", value: "che_ci" }
      ],
      headers_2: [
        // { text: "id", value: "id" },

        { text: "bill_id", value: "bill_id" },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "product_idname", value: "product_idname" },
        { text: "product_mm_name", value: "product_mm_name" },
        { text: "product_d_name", value: "product_d_name" },
        { text: "product_qty_x", value: "product_qty_x" },
        { text: "product_qty", value: "product_qty" },
        { text: "product_price", value: "product_price" }
      ],
      headers_3: [
        // { text: "id", value: "id" },

        { text: "che_ci", value: "che_ci" },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "datetime", value: "datetime" },
        { text: "car_phone_number", value: "car_phone_number" },
        { text: "View", value: "edit" }
      ],
      desserts_2: [],
      desserts: [],
      desserts_3: [],
      id_: null,
      //edit
      delete: {},
      rules: {
        lock: [v => !!v || "required"],
        price: [
          v => !!v || "required",
          v => v >= 0.1 || "required",
          v => !!v == Number.isInteger(v) || "Must be integer"
        ],
        qty: [
          v => !!v || "required",
          v => v >= 0.1 || "required",
          v => !!v == Number.isInteger(v) || "Must be integer"
        ],
        distant: [v => !!v || "required"]
      },
      // answer
      answer: {
        che_ci: ""
      }
    };
  },
  methods: {
    itemText(item) {
      return `${item.che_ci} || ${item.idname} || ${item.mm_name}`;
    },
    value(item) {
      return `${item.che_ci}`;
    },

    getColor(che_ci) {
      if (che_ci == null) return "red";
      else if (che_ci != null) return "orange";
      else return "green";
    },

    async submitCar() {
      this.overlay = true;
      await HTTP_shwethe_n.post(`/api/v1/order_list/add_checi`, {
        che_liang: this.che_liang_id
      }).then(response => {
        console.log(response);
      });
      location.reload();
    },
    async uppercase_5() {
      this.checK_idname = true;
      this.che_liang = this.che_liang.toUpperCase();
      console.log("AAAAAAAAAAAAA");
      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.che_liang
        }
      }).then(response => (this.che_liang_mmname = JSON.parse(response.data)));
      if (this.che_liang_mmname == false) {
        console.log(1);
        this.che_liang_id = null;
        this.che_liang_mmname = null;
      } else {
        console.log(2);
        this.che_liang_id = this.che_liang_mmname[0].id;
        this.che_liang_mmname = this.che_liang_mmname[0].mm_name;
      }
    },
    _uppercase_5() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },
    async phone_data(){
      console.log(this.phone_number);
      this.dialog = false;
      await HTTP_shwethe_n.put("/api/v2/order_list/add_checi_info", {
        auto_id: this.data_record.auto_id,
        che_ci: this.data_record.che_ci,
        data_jsonb: {
          car_phone_number: this.phone_number
        }
        })
        .then(function() {
          // location.reload();
        })
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
          this.check_api();
        });
    },
    async check_api_() {
      // console.log(this.answer.che_ci)
      await HTTP_shwethe_n.put("/api/v1/order_list/order_list_compls", {
        bill_id: this.id_,
        che_ci: this.answer.che_ci,
        data_: {
          status: "sucess",
        },
      })
        .then(function() {
          location.reload();
        })
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
        });
    },
    async submitOil() {
      if (this.desserts_2 == false) {
        console.log("log");
        alert("no data");
      } else {
        this.overlay = true;
        await this.check_api_();
        console.log("go");
      }
    },

    async editItem(item) {
      this.data_record = []
       this.data_record = item
      // this.overlay = true;
      // this.id_ = item.bill_id;
      // item;
      // await HTTP_2.get("shwethe_n/api/v1/order_list/order_list_by", {
      //   params: {
      //     ID: item.bill_id
      //   }
      // }).then(
      //   response => (
      //     console.log(response.data),
      //     (this.desserts_2 = JSON.parse(response.data))
      //     //  this.columns = response.data.columns
      //   )
      // );
      // this.overlay = false;
      this.dialog = true;
      //console.log("phone_number" + this.phone_number);
      console.log(item);
    },
   
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },
    async check_api(){
          await HTTP_shwethe_n.get("/api/v2/order_list/add_checi").then(
      response => (
        //console.log(response.data),
        (this.desserts_3 = JSON.parse(response.data)),
        console.log(this.desserts_3)
        //  this.columns = response.data.columns
      )
    );
    }
  },
  async created() {
    
    await HTTP_shwethe_n.get("/api/v2/order_list/add_checi").then(
      response => (
        //console.log(response.data),
        (this.desserts_3 = JSON.parse(response.data)),
        console.log(this.desserts_3)
        //  this.columns = response.data.columns
      )
    );
  }
};
</script>
