# develop stage
# FROM node:12.16-alpine as develop-stage
# FROM node:14.0.0-alpine as develop-stage
# FROM node:16.0.0-alpine as develop-stage
FROM node:20-alpine as develop-stage
WORKDIR /app
COPY package*.json ./
RUN rm -f yarn.lock && yarn install
# COPY . .


COPY ./public ./public
COPY ./src ./src
COPY *.js ./

RUN yarn build

# build stage
# FROM develop-stage as build-stage
# CMD /bin/sh -c "yarn serve"
# production stage

FROM nginx:1.15.7-alpine as production-stage
# COPY --from=build-stage /app/dist /usr/share/nginx/html

COPY nginx/prod.conf /temp/prod.conf
RUN envsubst /app < /temp/prod.conf > /etc/nginx/conf.d/default.conf
COPY --from=0 /app/dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
