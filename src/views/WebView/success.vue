<template>
  <v-app class="myDiv">
    <div class="bg"></div>
    <div class="center">
      <v-container>
        <v-form @submit.prevent="success" autocomplete="off">
          <div class="p_title_1">
            <b-img
              fluid
              class="img"
              :src="require('@/assets/unnamed.png')"
              alt="Image 1"
            ></b-img>

            <!-- <img class="img" :src="require('@/assets/unnamed.png')" /> -->
          </div>
          <div class="p_title_2">
            <p>Registration completed</p>
          </div>
          <hr />
          <v-btn large block color="success" class="mr-4" type="submit">
            Ok
          </v-btn>
        </v-form>
      </v-container>
    </div>
  </v-app>
</template>

<script>
export default {
  data: () => ({}),
  methods: {
    success() {
      window.location.href = "https://www.google.com";
    },
  },
};
</script>

<style lang="scss" scoped>
.center {
  justify-content: center;
  display: grid;
  height: 100vh;
  place-items: center;
}

.p_title_1 {
  display: flex;
  justify-content: center;
}

.p_title_2 {
  margin: 7%;
  display: flex;
  justify-content: center;
  font-size: 25px;
}

.img {
  width: 50%;
  height: 100%;
  cursor: pointer;
}

/* bg-image */
.myDiv {
  position: relative;
  z-index: 1;
  background-color: white;
}
.myDiv .bg {
  position: absolute;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-image: url("~@/assets/vectorstock_7808629.jpg");
  opacity: 0.02;
  width: 100%;
  height: 100%;
}
</style>
