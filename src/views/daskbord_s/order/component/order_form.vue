<template>
  <div>
    <b-row>
      <b-col md="4" sm="12">
        <v-card> </v-card>
      </b-col>
      <b-col md="8" sm="12">
        <v-card>
          <v-form>
            <v-container>
              <v-row>
                <v-col cols="6">
                  <v-text-field
                    label="customer"
                    v-model="answer_3.test_1"
                    @keyup="_uppercase_3"
                    @keyup.enter="uppercase_3"
                    ref="answer_3_test_1"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    label="product test_2"
                    v-model="answer_3.test_2"
                    ref="test_2"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card>
      </b-col>
      <b-col md="4" sm="12">
        <v-card>
          <v-card-title>Order report</v-card-title>
          <v-card-title> {{ data_customer_mmname }} </v-card-title>
          <v-form
            ref="form"
            autocomplete="off"
            v-model="valid"
            @submit.prevent="submitForm"
          >
            <v-container>
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    label="product idname"
                    v-model="answer.sname"
                    @keyup="_uppercase"
                    @keyup.enter="uppercase"
                    ref="product_idname"
                    :rules="rules.lock"
                    clearable
                    autofocus
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    label="mm_name"
                    v-model="data_product[0].mm_name"
                    :rules="rules.lock"
                    filled
                    dense
                    disabled
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    label="dname"
                    v-model="data_product[0].d_name"
                    :rules="rules.lock"
                    filled
                    dense
                    disabled
                  >
                  </v-text-field>
                </v-col>
                <v-col>
                  <v-text-field
                    label="Price"
                    type="number"
                    v-model.number="data_product[0].price"
                    :rules="rules.lock"
                    ref="Price"
                    @keyup.enter="goQty"
                  ></v-text-field>
                </v-col>
                <v-col>
                  <v-text-field
                    label="Qty"
                    type="number"
                    v-model.number="data_product[0].qty"
                    :rules="rules.lock"
                    ref="Qty"
                  ></v-text-field>
                </v-col>
                <div class="w-100"></div>
                <v-col>
                  <v-btn
                    type="submit"
                    color="success"
                    class="mr-4"
                    :disabled="!valid"
                  >
                    add to list
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card>
      </b-col>
      <b-col md="8" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Lists
            <v-spacer></v-spacer>
            <v-dialog v-model="dialog" max-width="290">
              <template v-slot:activator="{ on, attrs }">
                <v-btn color="primary" dark v-bind="attrs" v-on="on">
                  customer select
                </v-btn>
              </template>

              <v-card>
                <v-card-title class="headline">
                  please information
                </v-card-title>
                <v-container>
                  <v-form ref="form" v-model="valid">
                    <v-row>
                      <v-col cols="12">
                        <v-text-field
                          label="Customer"
                          v-model="answer_2.sname"
                          @keyup="_uppercase_2"
                          @keyup.enter="uppercase_2"
                          clearable
                          :rules="rules.lock"
                          autofocus
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          label="Customer_mm_name"
                          v-model="data_customer_mmname"
                          :rules="rules.lock"
                          filled
                          dense
                          disabled
                        >
                        </v-text-field>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-container>
                <div class="_center">
                  <v-btn
                    class="_center_btn"
                    color="primary"
                    @click="submitForm_L"
                  >
                    L
                  </v-btn>
                  <!-- </v-col> -->
                  <!-- <v-col cols="6"> -->
                  <!-- <v-spacer></v-spacer> -->
                  <v-btn
                    class="_center_btn"
                    color="primary"
                    @click="submitForm_2"
                  >
                    N
                  </v-btn>
                </div>
                <!-- <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn color="green darken-1" text @click="dialog = false">
                    Disagree
                  </v-btn>
                  <v-btn
                    color="green darken-1"
                    text
                    :disabled="!valid_2"
                    @click="submitForm_2"
                  >
                    Agree
                  </v-btn>
                </v-card-actions> -->
              </v-card>
            </v-dialog>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers"
            :items="table_list"
            :sort-by="['', '']"
            multi-sort
          >
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>

    <v-row>
      <b-col md="12" sm="12">
        <template>
          <v-data-table
            :headers="headers_2"
            :items="desserts_2"
            :items-per-page="5"
            class="elevation-1"
          ></v-data-table>
        </template>
      </b-col>
    </v-row>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import { HTTP_shwethe_n } from "../../../../plugins/http";

export default {
  data: () => ({
    checK_idname: false,
    overlay: false,
    data_customer_valid: false,
    valid: false,
    valid_2: true,
    valid_3: true,
    dialog: false,
    prduct_mm_name_L: null,
    prduct_d_name_L: null,
    // Get api
    items_partner: [],
    items_car: [],
    items_oil_type: [],
    headers: [
      // { text: "id", value: "id" },

      { text: "product_idname", value: "product_idname" },

      { text: "product_mm_name", value: "product_mm_name" },
      { text: "product_d_name", value: "product_d_name" },
      { text: "product_qty", value: "product_qty" },
      { text: "product_price", value: "product_price" },
    ],
    headers_2: [
      { text: "日期", value: "日期" },
      { text: "idname", value: "idname" },
      { text: "mm_name", value: "mm_name" },
      { text: "d_name", value: "d_name" },
      { text: "數量", value: "數量" },
      { text: "售價", value: "售價" },
    ],
    desserts_2: [],

    // require
    rules: {
      lock: [(v) => !!v || "required"],
      price: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        // (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      qty: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        // (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      distant: [(v) => !!v || "required"],
    },
    _id: null,
    table_list: [],
    // answer
    data_product: [{ mm_name: null }],
    data_customer: null,
    data_customer_mmname: null,
    answer_3: {
      test_1: "",
      test_2: "",
    },
    answer_2: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    answer: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    alert: false,
    clear_text: false,
    data_product_5: null,
    data_product_6: null,
  }),

  methods: {
    _uppercase_3() {
      this.answer_3.test_1 = this.answer_3.test_1.toUpperCase();
      console.log(this.answer_3.test_1);
      console.log(this.answer_3.test_1.length);
      if (this.clear_text != false) {
        this.$refs["answer_3_test_1"].reset();
        // this.desserts_2 = []
        this.$refs["test_2"].reset();
        this.clear_text = false;
      } else {
        console.log("1");
      }
    },
    async uppercase_3() {
      this.clear_text = true;

      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.answer_3.test_1,
        },
      }).then(
        (response) => (
          (this.data_product_5 = JSON.parse(response.data)),
          console.log(this.data_product_5)
        )
      );
      if (this.data_product_5 == false) {
        // this.data_product = [{ mm_name: null }];
        this.answer_3.test_2 = null;

        console.log("");
      } else {
        this.answer_3.test_2 = this.data_product_5[0].mm_name;
      }
    },
    async uppercase() {
      console.log(this.answer_3.test_1);
      console.log(this.answer_3.test_2);
      var aaa = this.answer_3.test_1 && this.answer_3.test_2;
      if (aaa) {
        this.checK_idname = true;
        this.answer.sname = this.answer.sname.toUpperCase();
        console.log(this.answer.sname);
        await HTTP_shwethe_n.get("/api/v1/product_price", {
          params: {
            ID: this.answer.sname,
          },
        }).then(
          (response) => (
            (this.data_product = JSON.parse(response.data)),
            console.log(this.data_product)
          )
        );

        if (this.data_product == false) {
          this.data_product = [{ mm_name: null }];

          console.log("");
        } else {
          this.$refs.Price.focus();
          console.log(this.data_product);
          console.log(this.prduct_mm_name_L);
        }
        await HTTP_shwethe_n.get("/api/v1/order_n/order_n_product_by_price", {
          params: {
            ID: this.data_product[0].id,
            ID_2: this.data_product_5[0].id,
          },
        }).then(
          (response) => (
            (this.data_product_6 = JSON.parse(response.data)),
            (this.desserts_2 = JSON.parse(response.data)),
            console.log(this.data_product_6)
          )
        );
      } else {
        alert("Please complete all information.");
      }
    },
    _uppercase() {
      console.log("1");
      if (this.answer.sname.length != 1) {
        if (this.checK_idname != false) {
          this.$refs.form.reset();
          this.checK_idname = false;
        } else {
          console.log("1");
        }
      } else {
        this.checK_idname = false;
        console.log(1);
      }
    },
    async api_process() {
      console.log(this.data_customer);
      console.log("test");
      await HTTP_shwethe_n.put("/api/v1/order_n/order_n_compls", {
        bill_id: this._id,
        yi_fang: this.data_customer[0].id,
        data_: {
          status: "sucess",
        },
      })
        .then(function () {
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {});
    },
    submitForm_2() {
      this.alert = true;
      this.dialog = false;

      console.log("BBBBBB");
      console.log(this.data_customer[0].yi_fang);
      if (this.data_customer) {
        this.alert = false;
        this.overlay = true;
        this.api_process();
      } else {
        this.alert = true;
      }
    },
    submitForm_L() {
      this.alert = true;
      this.dialog = false;

      console.log("BBBBBB");
      console.log(this.data_customer[0].yi_fang);
      if (this.data_customer) {
        this.alert = false;
        this.overlay = true;
        this.api_process_L();
      } else {
        this.alert = true;
      }
    },
    async api_process_L() {
      console.log(this.data_customer);
      console.log("test");
      await HTTP_shwethe_n.put("/api/v2/order_n/order_n_compls", {
        bill_id: this._id,
        yi_fang: this.data_customer[0].id,
        lei_b: 24,
        data_: {
          status: "sucess",
        },
      })
        .then(function () {
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {});
    },
    async submitForm() {
      // var aaa =
      //   this.answer_3.test_1 &&
      //   this.answer_3.test_2 &&
      //   this.data_product[0].mm_name &&
      //   this.data_product[0].d_name &&
      //   this.data_product[0].price &&
      //   this.data_product[0].qty;

      // if (aaa) {
      // var _id_ = Date.now().toString();
      var  _id_ = 0;
      console.log("a");
      this.checK_idname = false;
      const alasql = require("alasql");
      console.log(this.data_product[0]);
      var res4 = alasql(
        "SELECT id as product_id,qty as product_qty,price as product_price ,mm_name as product_mm_name,d_name as product_d_name ,idname as product_idname FROM ?",
        [this.data_product]
      );
      // bill_id: this._id,
      res4[0].product_bill_id= _id_;

      console.log(res4[0]);
      console.log("test");
      console.log("test");
      await HTTP_shwethe_n.post("/api/v1/order_n/order_n_list", {
        uid: 0,
        bill_id: this._id,
        yi_fang: 0,
        lei_b: 0,
        data_jsonb: [res4[0]],
      })
        .then(function () {
          // alert("SUCCESS!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.product_idname.focus();
        });
      this.$refs.form.reset();

      await HTTP_shwethe_n.get("/api/v1/order_n/order_n", {
        params: {
          ID: this._id,
        },
      }).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          console.log(this.table_list)
        )
      );
      // } else {
      // console.log("b");
      // alert("Please complete all information.");
      // }
    },

    goQty() {
      this.$refs.Qty.focus();
    },

    async uppercase_2() {
      this.checK_idname = true;
      this.answer_2.sname = this.answer_2.sname.toUpperCase();
      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.answer_2.sname,
        },
      }).then((response) => (this.data_customer = JSON.parse(response.data)));
      this.data_customer_mmname = null;
      if (this.data_customer == false) {
        console.log(1);
        this.data_customer_mmname = null;
      } else {
        console.log(2);
        this.data_customer_mmname = this.data_customer[0].mm_name;
      }
    },
    _uppercase_2() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },

    itemText(item) {
      return `${item.id} | ${item.idname} | ${item.mm_name}`;
    },
    sname_value(item) {
      console.log(this.item);
      return `${item.id}`;
    },
  },
  async created() {
    console.log(localStorage.getItem("uid"));
    var _id_ = Date.now().toString();
    this._id = _id_;
    console.log(this._id);
    await HTTP_shwethe_n.post(`/api/v1/order_n/order_n`, {
      bill_id: this._id,
      uid: localStorage.getItem("uid"),
    }).then((response) => {
      console.log(response);
    });
  },
};
</script>

<style scoped>
._center {
  text-align: center;
}

._center_btn {
  margin-block: 4%;
  margin-left: 6%;
  margin-right: 6%;
}
</style>
