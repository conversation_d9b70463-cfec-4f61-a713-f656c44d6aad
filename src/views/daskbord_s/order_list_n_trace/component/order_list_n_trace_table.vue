<template>
  <v-app>
    <!-- <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn> -->

    <v-tabs v-model="tab" fixed-tabs background-color="indigo" dark>
      <v-tabs-slider></v-tabs-slider>
      <v-tab href="#tab-1"> Product </v-tab>
      <v-tab href="#tab-2"> Coustomer </v-tab>
    </v-tabs>
    <v-tabs-items v-model="tab">
      <v-tab-item :key="1" :value="'tab-' + 1">
        <b-col md="12" sm="12">
          <v-card id="printMe">
            <v-card-title>
              Oissssssssssl
              <v-spacer></v-spacer>
              <!-- <a :href="$router.resolve({ name: 'oil' }).href">
          <v-icon>refresh</v-icon></a
        > -->
              <v-spacer></v-spacer>
              <v-text-field
                v-model="search_1"
                append-icon="mdi-magnify"
                label="Search"
                single-line
                hide-details
              ></v-text-field>
            </v-card-title>
            <v-data-table
              :footer-props="{
                'items-per-page-options': [10, 20, 30, 40, 50],
              }"
              :search="search_1"
              :headers="headers"
              :items="desserts"
              :sort-by="['', '']"
              multi-sort
            >

            <template v-slot:top>
                <v-card>
                  <v-dialog v-model="dialog">
                    <v-card>
                      <v-data-table
                        :footer-props="{
                          'items-per-page-options': [10, 20, 30, 40, 50],
                        }"
                        :headers="headers_product_1"
                        :items="table_view1"
                        :sort-by="['', '']"
                        multi-sort
                      ></v-data-table>
                      <!-- <v-card-text>
                        <v-form ref="form3" v-model="valid_3"> </v-form>
                      </v-card-text> -->
                    </v-card>
                  </v-dialog>
                </v-card>
              </template>

              <template v-slot:item.actions="{ item }">
                <v-icon small class="mr-2" @click="v_model_ithem1(item)">
                  mdi-pencil
                </v-icon>
              </template>
            </v-data-table>
          </v-card>
        </b-col>
      </v-tab-item>
      <v-tab-item :key="2" :value="'tab-' + 2">
        <b-col md="12" sm="12">
          <v-card id="printMe">
            <v-card-title>
              Oissssssssssl
              <v-spacer></v-spacer>
              <!-- <a :href="$router.resolve({ name: 'oil' }).href">
          <v-icon>refresh</v-icon></a
        > -->
              <v-spacer></v-spacer>
              <v-text-field
                v-model="search_2"
                append-icon="mdi-magnify"
                label="Search"
                single-line
                hide-details
              ></v-text-field>
            </v-card-title>
            <v-data-table
              :footer-props="{
                'items-per-page-options': [10, 20, 30, 40, 50],
              }"
              :search="search_2"
              :headers="headers_customer"
              :items="desserts_customer"
              :sort-by="['', '']"
              multi-sort
            >
              <template v-slot:top>
                <v-card>
                  <v-dialog v-model="dialog_2">
                    <v-card>
                      <v-data-table
                        :footer-props="{
                          'items-per-page-options': [10, 20, 30, 40, 50],
                        }"
                        :headers="headers_customer_2"
                        :items="table_view2"
                        :sort-by="['', '']"
                        multi-sort
                      ></v-data-table>
                      <!-- <v-card-text>
                        <v-form ref="form3" v-model="valid_3"> </v-form>
                      </v-card-text> -->
                    </v-card>
                  </v-dialog>
                </v-card>
              </template>

              <template v-slot:item.actions="{ item }">
                <v-icon small class="mr-2" @click="v_model_ithem2(item)">
                  mdi-pencil
                </v-icon>
              </template>
            </v-data-table>
          </v-card>
        </b-col>
      </v-tab-item>
    </v-tabs-items>
    <template>
      <v-snackbar :color="snackbar.color" v-model="snackbar.show">
        {{ snackbar.message }}
      </v-snackbar>
    </template>
  </v-app>
</template>

<script>
import { HTTP_shwethe_n } from "../../../../plugins/http";

export default {
  data() {
    return {
      tab: null,
      output: null,
      search_1: "",
      search_2: "",
      dialog: false,
      dialog_2: false,
      snackbar: {
        show: false,
        message: null,
        color: null,
      },

      headers: [
        // { text: "id", value: "id" },
        { text: "product_idname", value: "product_idname" },
        { text: "product_mm_name", value: "product_mm_name" },
        { text: "product_d_name", value: "product_d_name" },
        { text: "count", value: "售價0_y" },
        { text: "Actions", value: "actions", sortable: false },
      ],
      headers_customer: [
        { text: "jia_yi_name_idname", value: "jia_yi_name_idname" },
        { text: "jia_yi_name_mm_name", value: "jia_yi_name_mm_name" },
        { text: "count", value: "售價0_y" },
        { text: "Actions", value: "actions", sortable: false },
      ],
      headers_product_1: [
        { text: "jia_yi_name_idname", value: "jia_yi_name_idname" },
        { text: "jia_yi_name_mm_name", value: "jia_yi_name_mm_name" },
        { text: "數量_x", value: "數量_x" },
        { text: "數量_y", value: "數量_y" },
      ],
      headers_customer_2: [
        { text: "product_idname", value: "product_idname" },
        { text: "product_mm_name", value: "product_mm_name" },
        { text: "product_d_name", value: "product_d_name" },
        { text: "數量_x", value: "數量_x" },
        { text: "數量_y", value: "數量_y" },
      ],
      table_view2: [],
      table_view1 : [],
      desserts_customer: [],
      desserts: [],
      watch: {
        dialog(val) {
          val || this.close();
        },

        dialog_2(val) {
          val || this.close();
        },

        dialogDelete(val) {
          val || this.closeDelete();
        },
      },
      //edit
      delete: {},
    };
  },
  methods: {
    async v_model_ithem2(item) {
      this.dialog_2 = true;
      var editedItem = Object.assign({}, item);
      console.log(editedItem.jia_yi_name_id);
      await HTTP_shwethe_n.get(
        "/api/v1/order_list_n_trace/order_list_n_trace_customer_by",
        {
          params: {
            jia_yi_name_id: editedItem.jia_yi_name_id,
          },
        }
      ).then((response) => (this.table_view2 = JSON.parse(response.data)));
    },


    async v_model_ithem1(item) {
      this.dialog = true;
      var editedItem = Object.assign({}, item);
      console.log(editedItem.jia_yi_name_id);
      await HTTP_shwethe_n.get(
        "/api/v1/order_list_n_trace/order_list_n_trace_product_by",
        {
          params: {
            product_id: editedItem.product_id,
          },
        }
      ).then((response) => (this.table_view1 = JSON.parse(response.data)));
    },



    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },

    // editItem and delete
    // deleteItem(item) {
    //   this.delete = Object.assign({}, item);

    //   console.log(this.delete);
    //   console.log(this.delete.bill_id);
    //   // confirm("Are you sure you want to delete this item?") &&
    //   //   HTTP.post("oil_delete", { bill_id: this.delete.bill_id }).then(
    //   //     (response) =>
    //   //       console.log(response.data)
    //   //       // (this.desserts = response.data)
    //   //       //  this.columns = response.data.columns
    //   //   );
    //   confirm("Are you sure you want to delete this item?") &&
    //     HTTP.post("oil_delete", { bill_id: this.delete.bill_id }).then(
    //       (response) => {
    //         this.snackbar = {
    //           message: this.delete.mm_name_x,
    //           color: "warning",
    //           show: true,
    //         };
    //         console.log(response.data);
    //       }
    //     );
    // },
  },
  created: function () {
    HTTP_shwethe_n.get(
      "/api/v1/order_list_n_trace/order_list_n_trace_product"
    ).then(
      (response) => (
        console.log(response.data), (this.desserts = JSON.parse(response.data))
        //  this.columns = response.data.columns
      )
    );
    HTTP_shwethe_n.get(
      "/api/v1/order_list_n_trace/order_list_n_trace_customer"
    ).then(
      (response) => (
        console.log(response.data),
        (this.desserts_customer = JSON.parse(response.data))
        //  this.columns = response.data.columns
      )
    );
  },
};
</script>
