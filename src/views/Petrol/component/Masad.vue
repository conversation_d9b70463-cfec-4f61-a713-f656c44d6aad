<template>
  <!-- <v-app class="app"> -->
  <!-- <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn> -->
  <v-card id="printMe">
    <v-card-title>
      Petrol_Masad
      <v-spacer></v-spacer>
      <v-text-field v-model="search" append-icon="mdi-magnify" label="Search" single-line hide-details></v-text-field>
    </v-card-title>
    <v-data-table :search="search" :headers="headers" :items="desserts" :sort-by="['fen_dian_id', 'mean_distance_used']"
      :sort-desc="[false, false]" disable-pagination multi-sort>
      <template v-slot:top>
        <v-dialog v-model="dialog" fullscreen hide-overlay transition="dialog-bottom-transition">
          <v-card>
            <v-toolbar dark color="warning">
              <v-btn icon dark @click="dialog = false">
                <v-icon>mdi-close</v-icon>
              </v-btn>
              <v-toolbar-title>Petrol</v-toolbar-title>
              <v-spacer></v-spacer>
            </v-toolbar>

            <div class="app_1">
              <v-card>
                <v-card-title>
                  📒
                  <v-spacer></v-spacer>
                </v-card-title>
                <v-data-table :headers="headers_3" :items="desserts_3" disable-pagination></v-data-table>
              </v-card>

              <!-- <v-card>
                <v-card-title>
                  📒
                  <v-spacer></v-spacer>
                </v-card-title>
                <v-data-table :headers="headers_4" :items="desserts_4" disable-pagination></v-data-table>
              </v-card> -->

              <v-card>
                <v-card-title>
                  📒
                  <v-spacer></v-spacer>
                </v-card-title>
                <v-data-table :headers="headers_2" :items="desserts_2" disable-pagination></v-data-table>
              </v-card>
            </div>
          </v-card>
        </v-dialog>
      </template>

      <template v-slot:item.edit="{ item }">
        <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
      </template>
    </v-data-table>
  </v-card>
  <!-- </v-app> -->
</template>

<script>
import { HTTP, PetrolProduct } from "../../../plugins/http";

export default {
  data: () => ({
    valid: false,
    dialog: false,

    product_ids: [
      // { nern: "a", test: "11", test222: "q" },
    ],

    // selecte option
    shwetheOption: [
      { shwethe: "Shwethe1", shwetheValue: "1" },
      { shwethe: "Shwethe2", shwetheValue: "2" },
    ],

    // require
    rules: {
      lock: [(v) => !!v || "Field is required"],
      price: [
        (v) => !!v || "required",
        (v) => (v && v.length <= 6) || "must be less than 6",
      ],
      qty: [
        (v) => !!v || "required",
        (v) => (v && v.length <= 5) || "must be less than 5",
      ],
    },

    // answer
    answer: {
      sname: "",
      idname: "",
      price: "",
      data: [{ fname: "", qty: "" }],
    },

    // Table
    search: "",
    search_3: "",
    headers: [
      { text: "fen_dian_id", value: "fen_dian_id" },
      { text: "jia_yi_idname", value: "jia_yi_idname" },
      { text: "jia_yi_mm_name", value: "jia_yi_mm_name" },
      { text: "che_liang_id", value: "che_liang_id" },
      { text: "meanQty", value: "meanQty" },
      { text: "sumQty", value: "sumQty" },
      // { text: "mean_distance_used_2", value: "mean_distance_used_2" },
      { text: "mean_distance_used", value: "mean_distance_used" },
      { text: "hourLiter2", value: "average_2" },
      // { text: "minuteHour", value: "minute" },
      { text: "LiterHour", value: "oneKmLit" },
      // { text: "Literhundred", value: "hundredKmLit" },
      // { text: "hourLiter", value: "average" },
      { text: "mean", value: "mean" },
      // { text: "hourLitStd", value: "kmLitStd" },
      // { text: "hourLitMin", value: "kmLitMin" },
      // { text: "hourLit25Per", value: "kmLit25Per" },
      // { text: "hourLit50Per", value: "kmLit50Per" },
      // { text: "hourLit75Per", value: "kmLit75Per" },
      // { text: "hourLitMax", value: "kmLitMax" },
      // { text: "countDuplicate", value: "countDuplicate" },
      { text: "che_liang_id_Count", value: "che_liang_id_Count" },
      { text: "LastActive", value: "datetime" },
      { text: "Edit", value: "edit" },
    ],
    headers_2: [
      { text: "petrol_id", value: "petrol_id" },
      { text: "datetime", value: "datetime" },
      { text: "datetime_bill", value: "datetime_bill" },
      { text: "diff_datetime_bill", value: "diff_datetime_bill" },
      { text: "mean_datetime_bill", value: "mean_datetime_bill" },
      { text: "percent_date_bill", value: "percent_date_bill" },
      { text: "driver", value: "driver" },
      { text: "che_liang_id", value: "che_liang_id" },
      { text: "distant", value: "distant" },
      { text: "qty", value: "qty" },
      { text: "previous_distant", value: "previous_distant" },
      { text: "distance_used", value: "distance_used" },
      // { text: "mean_distance_used", value: "mean_distance_used" },
      // { text: "previous_qty", value: "previous_qty" },
      // { text: "duplicated", value: "duplicated" },
      // { text: "km_lit", value: "km_lit" },
      // { text: "average", value: "average" },
      // { text: "average_percent", value: "average_percent" },
      // { text: "distance_used_2", value: "distance_used_2" },
      { text: "mean_distance_used_2", value: "mean_distance_used_2" },
      // { text: "outliers", value: "outliers" },
      // { text: "km_lit_2", value: "km_lit_2" },
      { text: "km_lit", value: "km_lit" },
      { text: "average_2", value: "average_2" },
      { text: "average_percent_2", value: "average_percent_2" },
      { text: "mean", value: "mean" },
      { text: "count", value: "count" },
      // { text: "average_mean", value: "average_mean" },
      // { text: "show", value: "show" },
    ],
    headers_3: [
      { text: "datetime", value: "datetime" },
      { text: "che_liang_id", value: "che_liang_id" },
      { text: "driver", value: "driver" },
      // { text: "average", value: "average" },
      // { text: "average_percent", value: "average_percent" },
      { text: "mean_qty_dri", value: "mean_qty_dri" },
      { text: "km_lit_dri", value: "km_lit_dri" },
      { text: "mean_distance_used_dri", value: "mean_distance_used_dri" },
      { text: "volatility_dri", value: "volatility_dri" },
      { text: "period_dri", value: "period_dri" },
      { text: "day_pass_dri", value: "day_pass_dri" },
      { text: "count_dri", value: "count_dri" },
    ],
    headers_4: [
      { text: "che_liang_id", value: "che_liang_id" },
      { text: "datetime", value: "datetime" },
      { text: "oneKmLit", value: "oneKmLit" },
      { text: "minute", value: "minute" },
    ],
    desserts: [],
    desserts_2: [],
    desserts_3: [],
    desserts_4: [],

    // Match id
    editedItem: {
      idname: "",
    },
  }),
  methods: {
    print() {
      this.$htmlToPaper("printMe");
    },
    editItem(item) {
      console.log(item);
      this.dialog = true;

      PetrolProduct.get(`driver_compare_each_car?che_liang_id=${item.che_liang_id}`).then(
        (response) => {
          console.log(response.data), (this.desserts_3 = response.data);
        }
      );

      // PetrolProduct.get(`getMasadHourLiter?che_liang_id=${item.che_liang_id}`).then(
      //   (response) => {
      //     console.log(response.data, "rrrrrrrrrr"), (this.desserts_4 = response.data);
      //   }
      // );

      PetrolProduct.get(`list_details_masad/${item.che_liang_id}`).then(
        (response) => {
          console.log(response.data), (this.desserts_2 = response.data);
        }
      );

      // HTTP.post(`order_item_by_product_id`, {
      //   idname: this.editedItem.idname,
      // }).then((response) => {
      //     (this.desserts_2 = response.data[0].A1001),
      // });
    },
    addInput() {
      this.answer.data.push({ fname: "", qty: "" });
    },
    deleteInput(index) {
      this.answer.data.splice(index, 1);
    },
    submitForm() {
      this.$refs.form.validate();

      HTTP.post("/order_insert", this.answer)
        .then(function () {
          alert("SUCCESS!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.form.reset();
          this.dialog = false;
        });
    },

    itemText(item) {
      return `${item.id} || ${item.idname} || ${item.mm_name}`;
    },
    sname_value(item) {
      return `${item.id}`;
    },
  },
  created() {
    // HTTP.get(`order_item_list_columns_G`).then((response) => {
    //   this.headers = response.data;
    // });
    PetrolProduct.get(`listOfMasad`).then((response) => {
      console.log(response.data, "sssssssssssssssss"), (this.desserts = response.data);
    });
  },
};
</script>

<style lang="scss" scoped>
// .app {
//   margin: 5% 5%;
// }
// .app_1 {
//   margin: 3% 5%;
// }
// .app_2 {
//   margin: 0 5%;
// }

// @media only screen and (max-width: 1832px) {
//   .app {
//     margin: 5% 1%;
//   }
// }
</style>
