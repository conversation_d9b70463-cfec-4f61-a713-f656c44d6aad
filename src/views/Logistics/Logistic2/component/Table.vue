<template>
  <v-app>
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <v-card>
      <v-container> </v-container>

      <div id="printMe">
        <v-card-title>
          Logistics2
          <v-spacer></v-spacer>
          <v-select
            label="fen"
            :items="[1, 2]"
            v-model="fenType"
            clearable
          ></v-select>
        </v-card-title>

        <v-data-table
          :footer-props="{
            'items-per-page-options': [10, 20, 30, 40, 50],
          }"
          :search="search"
          :headers="headers"
          :items="filteredItems"
          :sort-by="['fen_x', 'want_qty']"
          multi-sort
        >
        </v-data-table>
      </div>
    </v-card>
  </v-app>
</template>

<script>
import { HTTP } from "../../../../plugins/http";

export default {
  data() {
    return {
      search: "",

      headers: [
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "d_name", value: "d_name" },
        { text: "qty", value: "qty" },
        { text: "fen", value: "fen" },
        { text: "datetime", value: "datetime" },
      ],
      desserts: [],
      fenType: null,
    };
  },
  computed: {
    filteredItems() {
      return this.desserts.filter((i) => {
        return !this.fenType || i.fen === this.fenType;
      });
    },
  },

  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },
  },

  created: function() {
    HTTP.get("logistics/logistics_oder_per_day").then(
      (response) =>
        // console.log(response.data),
        (this.desserts = response.data)
      //  this.columns = response.data.columns
    );
  },
};
</script>
