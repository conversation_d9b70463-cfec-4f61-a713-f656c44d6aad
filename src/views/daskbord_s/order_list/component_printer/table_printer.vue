<template>
  <div id="receipt_pos">
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <!-- <button @click="this.window.location='../pos.html'">
            ook
        </button> -->
    <iframe
      ref="printIframe"
      frameborder="0"
      scrolling="no"
      style="
        margin: 0px;
        padding: 0px;
        width: 0px;
        height: 0px;
        overflow: hidden;
        overflow: hidden;
        size: auto;
      "
    ></iframe>
    // 这里是需要打印的内容，根据需求可以隐藏也可以显示
    <div
      class="margin"
      style="
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    >
      <div
        class="border"
        style="
          margin: auto;
          padding: 1%;
          border: 1px solid black;
          margin-bottom: 20px;
        "
        id="table_L_N"
      >
        <div
          class="body_"
          id="table_L"
          style="width: 650px; background-color: white"
        >
          <h4 class="bill" style="margin: 0 0 1rem 30rem">
            {{ this.billlll }}
          </h4>

          <div
            class="recieve_header_a"
            style="
              margin-bottom: 1%;
              width: 600px;
              text-align: center;
              font-weight: bold;
            "
          >
            <h2 style="width: 20.7rem; margin-left: 2rem">ရွှေသဲ</h2>
          </div>

          <div
            class="recieve_header_b"
            style="
              margin: 0%;
              width: 550px;
              text-align: center;
              font-weight: bold;
            "
          >
            <h2 style="width: 20.7rem; margin-left: 2rem">
              အိမ်ဆောက်ပစ္စည်းရောင်း၀ယ်ရေး
            </h2>
          </div>

          <h4><b>Contact Info</b></h4>

          <label class="recieve_address" style="margin: 1% 0; font-size: 16px"
            >No6/199,Padauk street ,san sai(a)Ward,Tachileik Township</label
          ><br />
          <label class="recieve_address" style="margin: 1% 0; font-size: 16px"
            >(Th) 063-3733897,063-3733895 </label
          ><br />
          <label class="recieve_address" style="margin: 1% 0; font-size: 16px"
            >(Mm) 084-52241, 084-52027 , 09-5240209</label
          ><br />

          <br />
          <h4><b>Time</b></h4>
          <label
            class="recieve_address"
            style="margin: 1% 0; font-size: 16px"
            >{{ datetime }}</label
          >

          <table class="recieve_content">
            <tr>
              <th
                style="background-color: rgb(63, 58, 58); color: #ffffff"
                width="15%"
              >
                idname
              </th>
              <th
                style="background-color: rgb(63, 58, 58); color: #ffffff"
                width="40%"
              >
                mm_name
              </th>
              <th
                style="background-color: rgb(63, 58, 58); color: #ffffff"
                width="15%"
              >
                d_name
              </th>
              <th
                style="background-color: rgb(63, 58, 58); color: #ffffff"
                width="15%"
              >
                price
              </th>
              <th
                style="background-color: rgb(63, 58, 58); color: #ffffff"
                width="15%"
              >
                qty
              </th>
            </tr>
            <hr />
            <tr v-for="dessert in desserts" :key="dessert.id">
              <td>{{ dessert.idname }}</td>
              <td>{{ dessert.mm_name }}</td>
              <td>{{ dessert.d_name }}</td>
              <td>{{ dessert.price }}</td>
              <td>{{ dessert.qty }}</td>
            </tr>
          </table>
          <div class="ReceiptFoote">
            <label>Total Price</label> &nbsp;&nbsp;
            <b>
              <label id="totalprice2"> {{ total_price.total }} </label> </b
            ><label> ฿ </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { HTTP_2 } from "../../../../plugins/http";

export default {
  data() {
    return {
      total_price: 0,
      desserts: null,
      billlll: null,
      datetime: null,
      customer_idname: null,
      customer_name: null,
      content: null,
    };
  },
  methods: {
    printpage() {
      // 拿到要打印区域的dom结构并设置到Iframe的srcdoc属性上面
      // this.$htmlToPaper("table_L");
      // var printIframe = this.$refs.printIframe;
      // var newstr = this.$refs.odiv.innerHTML;
      // printIframe.setAttribute("srcdoc", newstr);
      // printIframe.onload = function () {
      //   console.log(printIframe.contentWindow);
      //   // 去掉iframe里面的dom的body的padding margin的默认数值
      //   printIframe.contentWindow.document.body.style.padding = "0px";
      //   printIframe.contentWindow.document.body.style.margin = "0px";
      //   // 开始打印
      //   printIframe.contentWindow.focus();
      //   printIframe.contentWindow.print();
      // };
    },
    print() {
      console.log("AAAAAAAAA");
      // this.$htmlToPaper("table_L_N");
      var printIframe = this.$refs.printIframe;
      // let newWindow = window.open("_blank"); //打开新窗口
      let codestr = document.getElementById("table_L").innerHTML;
      printIframe.setAttribute("srcdoc", codestr);
      printIframe.onload = function () {
        console.log(printIframe.contentWindow);
        // 去掉iframe里面的dom的body的padding margin的默认数值
        printIframe.contentWindow.document.body.style.padding = "0px";
        printIframe.contentWindow.document.body.style.margin = "0px";
        // 开始打印
        printIframe.contentWindow.focus();
        printIframe.contentWindow.print();
      };

      // newWindow.document.write(codestr); //向文档写入HTML表达式或者JavaScript代码
      // newWindow.document.close(); //关闭document的输出流, 显示选定的数据
      // newWindow.print();
    },
    print_rec2() {
      console.log(this.bill_id__);
      const ThermalPrinter = require("node-thermal-printer").printer;
      const PrinterTypes = require("node-thermal-printer").types;
      (async () => {
        console.log("Print");
        let printer = new ThermalPrinter({
          type: PrinterTypes.epson, // Printer type: 'star' or 'epson'
          interface: "/dev/usb/lp0",
          options: {
            // Additional options
            timeout: 5000, // Connection timeout (ms) [applicable only for network printers] - default: 3000
          },
        });
        const isConnected = await printer.isPrinterConnected();
        console.log(isConnected);
        console.log("Printer connected:", isConnected);
        // await printer.printImage('image.png');
        // await printer.printImage("image1.png");
        // await printer.printImage("image2.png");
        // await printer.printImage("image3.png");
        printer.beep();
        // printer.cut();
        printer.partialCut();
        await printer.execute();
        // try {
        //   await printer.execute();
        //   await printer.execute();
        // } catch (e) {
        // } finally {
        //   // const { getCurrentWindow, globalShortcut } = require('electron').remote;
        //   // window.location.href = "../pos.html";
        // }
      })();
    },
  },

  async created() {
    this.printpage();
    // this.print_rec2();
  },
};
</script>

<style scoped>
@media print {
  @page {
    margin: 0;
  }
  body {
    margin: 1.6cm;
  }
}

/* .margin {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
} */

/* .border {
  margin: auto;
  padding: 1%;
  border: 1px solid black;
} */

/* recieve */
/* .body_ {
  width: 650px;
  background-color: white;
} */

/* .bill {
  margin: 0 0 1rem 30rem;
} */

/* .recieve_header_a {
  margin-bottom: 1%;
  width: 600px;
  text-align: center;
  font-weight: bold;
} */

/* .hr_a {
  width: 20.7rem;
  margin-left: 7.8rem;
} */

/* .recieve_header_b {
  margin: 1%;
  width: 550px;
  text-align: center;
  font-weight: bold;
} */

/* .recieve_address {
  margin: 1% 0;
  font-size: 16px;
} */

.hr_b {
  margin-right: 2%;
}

.recieve_content {
  text-align: justify;
  width: 620px;
  border-collapse: collapse;
  font-size: 18px;
}

/* .recieve_content th {
  background-color: rgb(63, 58, 58);
  color: #ffffff;
} */

/* .recieve_content td {
  border-bottom: 1px dotted #000;
} */

.ReceiptFoote {
  margin: 1rem 0 0 23rem;
  font-size: 20px;
}

.bug {
  color: #ffffff;
}
</style>
