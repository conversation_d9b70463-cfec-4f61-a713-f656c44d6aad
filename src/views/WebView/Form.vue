<template>
  <v-app>
    <div class="myDiv">
      <div class="bg"></div>
      <b-container>
        <div class="p_title">
          <p>Employment application</p>
        </div>

        <b-row
          ><v-subheader
            ><font color="red">* လိုအပ်သည်</font></v-subheader
          ></b-row
        >

        <hr />

        <b-form @submit="onSubmit" id="todo-field" v-if="show" ref="form">
          <b-row><v-subheader>ကိုယ်ပိုင်သတင်းအချက်အလက်များ</v-subheader></b-row>
          <b-row>
            <b-col sm>
              <label>အမည်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label>
              <b-form-input
                v-model="answer.name"
                required
                placeholder="Enter name"
                autocomplete="off"
                ref="name"
              ></b-form-input>
            </b-col>
            <b-col>
              <label>မွေးသက္ကရာဇ်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label>
              <b-form-input
                type="date"
                v-model="answer.birthday"
                required
              ></b-form-input>
            </b-col>

            <div class="w-100"></div>

            <b-col sm>
              <label>လိင်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label>
              <b-form-select required v-model="answer.sex">
                <option value="" selected disabled>Choose</option>
                <option>ကျား</option>
                <option>‍မ</option>
              </b-form-select>
            </b-col>
            <b-col>
              <label
                >အိမ်ထောင်ရှိ / မရှိ:<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <b-form-select required v-model="answer.status">
                <option value="" selected disabled>Choose</option>
                <option>ရှိ</option>
                <option>မရှိ</option>
              </b-form-select>
            </b-col>
          </b-row>

          <b-row>
            <b-col sm>
              <label
                >မှတ်ပုံတင်ဖိုင် ( အရှေ့ ):<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <input
                type="file"
                @change="onFileChange_font"
                accept="image/*"
                capture
                required
              />
              <div v-if="img.font_base64">
                <img class="img" :src="img.font_base64" />
              </div>
            </b-col>
            <b-col sm>
              <label
                >မှတ်ပုံတင်ဖိုင် ( အနောက် ):<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <input
                type="file"
                @change="onFileChange_back"
                accept="image/*"
                capture
                required
              />
              <div v-if="img.back_base64">
                <img class="img" :src="img.back_base64" />
              </div>
            </b-col>
            <b-col sm>
              <label
                >လက်ရှိကိုယ်ပိုင်ဓါတ်ပုံ:<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <input
                type="file"
                @change="onFileChange_face"
                accept="image/*"
                capture
                required
              />
              <div v-if="img.face_base64">
                <img class="img" :src="img.face_base64" />
              </div>
            </b-col>
          </b-row>

          <hr />

          <b-row><v-subheader>ဆက်သွယ်ရန်</v-subheader></b-row>

          <b-row>
            <b-col sm>
              <label>ဖုန်းနံပါတ်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label>
              <b-form-input
                v-model="answer.phone"
                type="number"
                required
                autocomplete="off"
              ></b-form-input>
            </b-col>
            <b-col>
              <label
                >ဖေ့စ်ဘွတ်ခ်နာမည်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
              >
              <b-form-input
                v-model="answer.facebook"
                required
                autocomplete="off"
              ></b-form-input>
            </b-col>
          </b-row>

          <hr />

          <b-row><v-subheader>ပညာအရည်အချင်း</v-subheader></b-row>

          <b-row>
            <b-col cols="12">
              <label
                >အဆင့်မြင့်ပညာရေးအဆင့်:<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <b-form-select required v-model="answer.education">
                <option value="" selected disabled>Choose</option>
                <option
                  v-for="education in educations"
                  v-bind:value="education.value"
                  v-bind:key="education.value"
                  >{{ education.value }}</option
                >
              </b-form-select>
            </b-col>

            <template v-if="answer.education === 'အထက်တန်းကျောင်း'">
              <b-col md="6">
                <label
                  >ကျောင်းအမည်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
                >
                <b-form-input
                  v-model="answer.schoolname"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <label
                  >ပညာရေးအဆင့်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
                >
                <b-form-select
                  required
                  v-model="answer.level"
                  :options="highschool_option"
                ></b-form-select>
              </b-col>

              <b-col md="12">
                <b-form-group label="အမှတ်စာရင်း :">
                  <b-form-input
                    v-model="answer.gpa"
                    autocomplete="off"
                  ></b-form-input>
                </b-form-group>
              </b-col>

              <b-col md="6">
                <label
                  >အနာဂတ်မှာလေ့လာသင်ယူရန်ရှိပါသလား:<font color="red"
                    >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                  ></label
                >
                <b-form-select required v-model="answer.study_future">
                  <option value="" selected disabled>Choose</option>
                  <option
                    v-for="study_future in study_futures"
                    v-bind:value="study_future.id"
                    v-bind:key="study_future.value"
                    >{{ study_future.value }}</option
                  >
                </b-form-select>
              </b-col>
              <template v-if="answer.study_future === 'ရှိ'">
                <b-col>
                  <label
                    >သွားမည့်ရက်ကိုဖြည့်စွပ်ပါ:<font color="red"
                      >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                    ></label
                  >
                  <b-form-input
                    type="date"
                    v-model="answer.study_plan"
                    required
                  ></b-form-input>
                </b-col>
              </template>
            </template>

            <template v-if="answer.education === 'တက္ကသိုလ်'">
              <b-col md="6">
                <label
                  >တက္ကသိုလ်အမည်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
                >
                <b-form-input
                  v-model="answer.schoolname"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <label
                  >ပညာရေးအဆင့်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
                >

                <b-form-input
                  v-model="answer.level"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <label>မေဂျာ:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label>
                <b-form-input
                  v-model="answer.branch"
                  autocomplete="off"
                  required
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <b-form-group label="အမှတ်စာရင်း :">
                  <b-form-input
                    v-model="answer.gpa"
                    autocomplete="off"
                  ></b-form-input>
                </b-form-group>
              </b-col>

              <b-col md="6">
                <label
                  >အနာဂတ်မှာလေ့လာသင်ယူရန်ရှိပါသလား:<font color="red"
                    >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                  ></label
                >
                <b-form-select required v-model="answer.study_future">
                  <option value="" selected disabled>Choose</option>
                  <option
                    v-for="study_future in study_futures"
                    v-bind:value="study_future.id"
                    v-bind:key="study_future.value"
                    >{{ study_future.value }}</option
                  >
                </b-form-select>
              </b-col>
              <template v-if="answer.study_future === 'ရှိ'">
                <b-col>
                  <label
                    >သွားမည့်ရက်ကိုဖြည့်စွပ်ပါ:<font color="red"
                      >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                    ></label
                  >
                  <b-form-input
                    type="date"
                    v-model="answer.study_plan"
                    required
                  ></b-form-input>
                </b-col>
              </template>
            </template>

            <template v-if="answer.education === 'ကောလိပ်'">
              <b-col md="6">
                <label
                  >ကောလိပ်အမည်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
                >
                <b-form-input
                  v-model="answer.schoolname"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <label>မေဂျာ:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label>

                <b-form-input
                  v-model="answer.branch"
                  autocomplete="off"
                  required
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <b-form-group label="အမှတ်စာရင်း :">
                  <b-form-input
                    v-model="answer.gpa"
                    autocomplete="off"
                  ></b-form-input>
                </b-form-group>
              </b-col>

              <b-col md="6">
                <label
                  >အနာဂတ်မှာလေ့လာသင်ယူရန်ရှိပါသလား:<font color="red"
                    >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                  ></label
                >
                <b-form-select required v-model="answer.study_future">
                  <option value="" selected disabled>Choose</option>
                  <option
                    v-for="study_future in study_futures"
                    v-bind:value="study_future.id"
                    v-bind:key="study_future.value"
                    >{{ study_future.value }}</option
                  >
                </b-form-select>
              </b-col>
              <template v-if="answer.study_future === 'ရှိ'">
                <b-col>
                  <label
                    >သွားမည့်ရက်ကိုဖြည့်စွပ်ပါ:<font color="red"
                      >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                    ></label
                  >
                  <b-form-input
                    type="date"
                    v-model="answer.study_plan"
                    required
                  ></b-form-input>
                </b-col>
              </template>
            </template>
          </b-row>

          <hr />

          <b-row><v-subheader>ကျွမ်းကျင်မှုနဲ့စွမ်းရည်</v-subheader></b-row>

          <b-row>
            <b-col cols="12">
              <b-form-group label="လုပ်ငန်းအတွေ့အကြုံ :">
                <b-form-textarea
                  v-model="answer.experience"
                  autocomplete="off"
                  rows="1"
                  max-rows="3"
                ></b-form-textarea>
              </b-form-group>
            </b-col>
          </b-row>

          <template>
            <div v-for="(language_, index) in answer.languages" :key="index">
              <b-row>
                <b-col cols="12">
                  <label>ဘာသာစကား</label>
                  <v-combobox
                    v-model="language_.language"
                    :name="`languages[${index}][language]`"
                    :items="languages"
                    label="Choose"
                    outlined
                    dense
                  ></v-combobox>
                </b-col>
                <b-col>
                  <b-form-group>
                    <b-form-select v-model="language_.speak">
                      <option value="" selected disabled>ပြော</option>
                      <option>ကျွမ်းကျင်သူ</option>
                      <option>ကောင်းသော</option>
                      <option>အနည်းငယ်</option>
                      <option>မကောင်းဘူး</option>
                    </b-form-select>
                  </b-form-group>
                </b-col>
                <b-col>
                  <b-form-group>
                    <b-form-select v-model="language_.read">
                      <option value="" selected disabled>ဖတ်</option>
                      <option>ကျွမ်းကျင်သူ</option>
                      <option>ကောင်းသော</option>
                      <option>အနည်းငယ်</option>
                      <option>မကောင်းဘူး</option>
                    </b-form-select>
                  </b-form-group>
                </b-col>
                <b-col>
                  <b-form-group>
                    <b-form-select v-model="language_.write">
                      <option value="" selected disabled>ရေး</option>
                      <option>ကျွမ်းကျင်သူ</option>
                      <option>ကောင်းသော</option>
                      <option>အနည်းငယ်</option>
                      <option>မကောင်းဘူး</option>
                    </b-form-select>
                  </b-form-group>
                </b-col>
              </b-row>
            </div>

            <b-button size="sm" variant="light" @click="addExperience"
              >+ ဘာသာစကား</b-button
            >
          </template>

          <hr />

          <b-row>
            <b-col cols="12">
              <label
                >မိမိလုပ်ချင်သောအလုပ်:<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <v-combobox
                v-model="answer.typework"
                :items="typework"
                label="Multiple select"
                multiple
                ref="typework"
                outlined
                dense
              ></v-combobox>
            </b-col>
            <b-col sm>
              <label
                >သင်ဘယ်ဆိုင်တွင်အလုပ်လုပ်ချင်သလဲ:<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <b-form-select required v-model="answer.storeplace">
                <option value="" selected disabled>Choose</option>
                <option>ရွှေသဲ(၁)(ဆန်ဆိုင်းက)</option>
                <option>ရွှေသဲ(၂)(ဝမ်လုံး)</option>
              </b-form-select>
            </b-col>
            <b-col>
              <label
                >မျှော်မှန်းလစာ:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
              >
              <b-form-select required v-model="answer.expectedSalary">
                <option value="" selected disabled>Choose</option>
                <option>3000 - 5000 ฿</option>
                <option>3000 - 5000 ฿</option>
                <option>5000 - 7000 ฿</option>
                <option>7000 - 9000 ฿</option>
                <option>More than 9000+ ฿</option>
              </b-form-select>
            </b-col>
            <b-col cols="12">
              <label
                >ရွှေသဲ အလုပ်ကြော်ငြာ ဘယ်မှာတွေ့ခဲ့သလဲ?<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <v-combobox
                v-model="answer.KnowShwethe"
                :items="KnowShwethe"
                label="Multiple select"
                multiple
                ref="KnowShwethe"
                outlined
                dense
              ></v-combobox>
            </b-col>
          </b-row>

          <hr />

          <!-- <b-button block type="submit" variant="primary">Submit</b-button> -->

          <v-btn
            large
            type="submit"
            block
            color="primary"
            :loading="loading"
            :disabled="loading"
            @click="loader = 'loading'"
            >Submit</v-btn
          >
        </b-form>
        <!-- <b-card class="mt-3" header="Form Data Result">
          <pre class="m-0">{{ answer }}</pre>
        </b-card> -->
      </b-container>
    </div>
  </v-app>
</template>

<script>
import { HTTP } from "../../plugins/http";
export default {
  data: () => ({
    show: true,
    loader: null,
    loading: false,

    // vuetify
    highschool_option: [
      { value: "G-1", text: "G-1" },
      { value: "G-2", text: "G-2" },
      { value: "G-3", text: "G-3" },
      { value: "G-4", text: "G-4" },
      { value: "G-5", text: "G-5" },
      { value: "G-6", text: "G-6" },
      { value: "G-7", text: "G-7" },
      { value: "G-8", text: "G-8" },
      { value: "G-9", text: "G-9" },
      { value: "G-10", text: "G-10" },
      { value: "10တန်းအောင်", text: "10တန်းအောင်" },
    ],
    educations: [
      {
        option: "ကျောင်းအမည်:",
        value: "အထက်တန်းကျောင်း",
      },
      { option: "တက္ကသိုလ်အမည်:", value: "တက္ကသိုလ်" },
      { option: "ကောလိပ်အမည်:", value: "ကောလိပ်" },
    ],
    study_futures: [
      { id: "ရှိ", value: "ရှိ" },
      { id: "မရှိ", value: "မရှိ" },
    ],
    languages: ["ဗမာ", "တရုတ်", "ထိုင်း", "ရှမ်း", "အာခါ", "အင်္ဂလိပ်"],
    KnowShwethe: [
      "လက်ကမ်းစာစောင်များ",
      "အသိအကျွမ်း",
      "ရွှေသဲ facebook page",
      "သူငယ်ချင်း",
    ],
    typework: ["စျေးရောင်း", "ကွန်ပျူတာစာရင်းကိုင်"],

    // img preview
    url_font: null,
    url_back: null,
    url_face: null,
    url_paper: null,

    // form answer
    answer: {
      name: "",
      birthday: "",
      sex: "",
      status: "",
      font: null,
      back: null,
      face: null,
      phone: "",
      facebook: "",
      education: "",
      schoolname: "",
      level: "",
      branch: "",
      gpa: "",
      study_future: "",
      study_plan: "",
      experience: "",
      languages: [
        {
          language: "",
          speak: "",
          read: "",
          write: "",
        },
      ],
      typework: "",
      storeplace: "",
      expectedSalary: "",
      KnowShwethe: "",
    },
    img: {
      font_base64: null,
      back_base64: null,
      face_base64: null,
    },
  }),
  methods: {
    // autofocus
    focusInput: function(inputRef) {
      this.$refs[inputRef].focus();
      return true;
    },
    // submit
    onSubmit(evt) {
      evt.preventDefault();
      if (this.answer.typework !== "" && this.answer.KnowShwethe !== "") {
        this.loading = true;
        HTTP.post("/form_job_insert", this.answer)
          .then(() => {
            console.log("SUCCESS!!");
            this.$router.push("/page/success");
          })
          .catch(() => {
            alert("FAILURE!!");
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        if (!this.answer.typework) {
          this.focusInput("typework");
          return;
        }
        if (!this.answer.KnowShwethe) {
          this.focusInput("KnowShwethe");
          return;
        }
      }
    },
    // img preview
    onFileChange_font: function(event) {
      var input = event.target;
      if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = (e) => {
          this.img.font_base64 = e.target.result;
          this.answer.font = reader.result.split(",")[1];
        };
        reader.readAsDataURL(input.files[0]);
      }
    },
    onFileChange_back: function(event) {
      var input = event.target;
      if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = (e) => {
          this.img.back_base64 = e.target.result;
          this.answer.back = reader.result.split(",")[1];
        };
        reader.readAsDataURL(input.files[0]);
      }
    },
    onFileChange_face: function(event) {
      var input = event.target;
      if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = (e) => {
          this.img.face_base64 = e.target.result;
          this.answer.face = reader.result.split(",")[1];
        };
        reader.readAsDataURL(input.files[0]);
      }
    },
    addExperience() {
      this.answer.languages.push({
        language: "",
        speak: "",
        read: "",
        write: "",
      });
    },
  },
  created() {
    window.onbeforeunload = function() {
      return "Are you sure want to leave?";
    };
  },
};
</script>

<style lang="scss" scoped>
/* bg-image */
.myDiv {
  position: relative;
  z-index: 1;
  background-color: white;
}
.myDiv .bg {
  position: absolute;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-image: url("~@/assets/vectorstock_7808629.jpg");
  opacity: 0.025;
  width: 100%;
  height: 100%;
}
.p_title {
  font-size: 30px;
  margin: 3% 0;
}
.img {
  width: 50%;
  height: 15vh;
  cursor: pointer;
  border-radius: 4px;
  border: 3px solid white;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
}
@media only screen and (max-width: 600px) {
  .p_title {
    text-align: center;
    font-size: 25px;
    margin: 3% 0;
    // border: 3px solid green;
  }
  footer {
    width: 640px;
    position: fixed;
    bottom: 0px;
    left: 50%;
    margin-left: -320px;
  }
}
</style>
