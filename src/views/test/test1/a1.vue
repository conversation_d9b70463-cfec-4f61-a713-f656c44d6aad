<template>
  <v-app>
    <v-container>
      <div id="app">
        <v-app id="inspire">
          <v-card>
            <v-card-title>
              <v-container grid-list-md text-xs-center>
                <v-layout row wrap>
                  <v-flex xs3>
                    <v-text-field
                      append-icon="search"
                      label="Name"
                      single-line
                      hide-details
                      @change="searchName"
                      v-model="filterQry.qryName"
                    ></v-text-field>
                  </v-flex>
                  <v-flex xs3>
                    <!-- call method searchName after leaving textfield-->
                    <v-autocomplete
                      label="Gender"
                      :items="['Male', 'Female', 'Test']"
                      @change="searchGender"
                      v-model="filterQry.qryGender"
                      single-line
                      hide-details
                    ></v-autocomplete>
                  </v-flex>
                  <v-flex xs3>
                    <!-- call method searchDept after leaving textfield-->
                    <v-text-field
                      append-icon="search"
                      label="Department"
                      single-line
                      hide-details
                      @change="searchDept"
                      v-model="filterQry.qryDept"
                    ></v-text-field>
                  </v-flex>
                  <v-flex xs3>
                    <!-- call method searchCity after leaving textfield-->
                    <v-text-field
                      append-icon="search"
                      label="City"
                      single-line
                      hide-details
                      @change="searchCity"
                      v-model="filterQry.qryCity"
                    ></v-text-field>
                  </v-flex>
                </v-layout>
                <v-btn @click="clear">clear</v-btn>
                <!-- <pre>{{ filterQry }}</pre> -->
              </v-container>
            </v-card-title>
            <v-data-table :headers="headers" :items="filteredItems">
              <template v-slot:items="props">
                <td class="text-xs-left">{{ props.item.id }}</td>
                <td class="text-xs-left">{{ props.item.name }}</td>
                <td class="text-xs-left">{{ props.item.gender }}</td>
                <td class="text-xs-left">{{ props.item.department }}</td>
                <td class="text-xs-left">{{ props.item.city }}</td>
              </template>
              <template v-slot:no-results>
                <v-alert :value="true" color="error" icon="warning">
                  Found no results.
                </v-alert>
              </template>
            </v-data-table>
          </v-card>
        </v-app>
      </div>
    </v-container>
  </v-app>
</template>

<script>
// import { HTTP } from "../../../plugins/http";

export default {
  data() {
    return {
      filterQry: {
        //you can use Object if you want to do multiple-filter
        //or you can use String if you just want to single-filter
        qryName: "",
        qryGender: "",
        qryDept: "",
        qryCity: "",
      },
      headers: [
        {
          text: "ID",
          value: "id",
        },
        {
          text: "Name",
          value: "name",
        },
        {
          text: "Gender",
          value: "gender",
        },
        {
          text: "Department",
          value: "department",
        },
        {
          text: "City",
          value: "city",
        },
      ],
      employee: [
        {
          id: 1,
          name: "Silvanus J.",
          gender: "Male",
          department: "Training",
          city: "Zhouzhuang",
        },
        {
          id: 2,
          name: "Silvanus M.",
          gender: "Male",
          department: "Engineering",
          city: "Wotsogo",
        },
        {
          id: 3,
          name: "Elbertina",
          gender: "Female",
          department: "Training",
          city: "Zhangjiachang",
        },
        {
          id: 4,
          name: "Kennan",
          gender: "Female",
          department: "Human Resources",
          city: "Miedzichowo",
        },
        {
          id: 5,
          name: "Kurt",
          gender: "Test",
          department: "Training",
          city: "Cascavel",
        },
        {
          id: 6,
          name: "York",
          gender: "Male",
          department: "Services",
          city: "Kyaukse",
        },
        {
          id: 7,
          name: "Barnie",
          gender: "Test",
          department: "Training",
          city: "Pasirpanjang",
        },
        {
          id: 8,
          name: "Brigg",
          gender: "Male",
          department: "Services",
          city: "Khoronk’",
        },
        {
          id: 9,
          name: "Fredek",
          gender: "Test",
          department: "Sales",
          city: "Ishioka",
        },
        {
          id: 10,
          name: "Kordula",
          gender: "Female",
          department: "Support",
          city: "Lapas",
        },
      ],
    };
  },
  computed: {
    //use this filteredItems as the items prop in v-data-table
    filteredItems: function() {
      //call the filterItems method
      return this.filterItems(this.employee, this.filterQry);
    },
  },
  methods: {
    clear() {
      this.filterQry.qryName = "";
      this.filterQry.qryGender = "";
      this.filterQry.qryDept = "";
      this.filterQry.qryCity = "";
    },
    //method to update the name query
    searchName: function(val) {
      this.filterQry.qryName = val;
    },

    //method to update the Gender query
    searchGender: function(val) {
      this.filterQry.qryGender = val;
    },

    //method to update the department query
    searchDept: function(val) {
      this.filterQry.qryDept = val;
    },

    //method to update the department query
    searchCity: function(val) {
      this.filterQry.qryCity = val;
    },

    //method to filter the items.
    filterItems: function(arr, query) {
      console.log("--filter item--");
      return arr.filter(function(item) {
        let name = item.name
          .toLowerCase()
          .includes(query.qryName.toLowerCase());
        let gender = item.gender
          .toLowerCase()
          .includes(query.qryGender.toLowerCase());
        let dept = item.department
          .toLowerCase()
          .includes(query.qryDept.toLowerCase());
        let city = item.city
          .toLowerCase()
          .includes(query.qryCity.toLowerCase());
        return name && gender && dept && city;
      });
    },
  },
};
</script>
