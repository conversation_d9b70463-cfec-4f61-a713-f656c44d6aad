<template>
  <v-app>
    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
    <b-row>
      <b-col md="12" sm="12">
        <v-data-table
          :footer-props="{
            'items-per-page-options': [10, 20, 30, 40, 50],
          }"
          :headers="headers"
          :items="table_list"
          :sort-by="['', '']"
          multi-sort
        >
          <template v-slot:item.edit="{ item }">
            <v-btn small color="primary" @click="editItem(item)">View</v-btn>
          </template>
        </v-data-table>
      </b-col>
    </b-row>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Oillllllllllllllllllll
            <v-spacer></v-spacer>
          </v-card-title>
          <v-form ref="form" v-model="valid_2" autocomplete="off">
            <v-container>
              <v-row>
                <v-col cols="4">
                  <v-text-field
                    v-model="item_bill_id"
                    outlined
                    disabled
                    :rules="rules.lock"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="4">
                  <v-text-field
                    label="produuct idname"
                    v-model="produuct_idname"
                    outlined
                    @keyup="_produuct_idname_keyup"
                    @keyup.enter="produuct_idname_keyup"
                    :rules="rules.lock"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="4">
                  <v-text-field
                    label="duty id"
                    v-model="produuct_name"
                    outlined
                    :rules="rules.lock"
                    disabled
                    ref="produuct_name"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="4">
                  <v-text-field
                    label="produuct qty"
                    v-model="produuct_qty"
                    outlined
                    :rules="rules.lock"
                    ref="produuct_qty"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="4">
                  <v-btn
                    @click="summit_duty_id"
                    :disabled="!valid_2"
                    color="success"
                    class="mr-4"
                  >
                    confirm
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers_3"
            :items="desserts_3"
            :sort-by="['', '']"
            multi-sort
          >
            <!-- <template v-slot:item.edit="{ item }">
              <v-btn small color="primary" @click="editItem(item)">View</v-btn>
            </template> -->
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
  </v-app>
</template>

<script>
import { HTTP_2, HTTP } from "../../../../plugins/http";

export default {
  data: (vm) => ({
    checK_idname: false,
    produuct_idname: null,
    produuct_name: null,
    bill_id___: null,
    data_api_post: {},
    valid: false,
    duty_id: null,
    overlay: false,
    valid_2: false,
    delete: {},
    headers: [
      // { text: "id", value: "id" },

      { text: "bill_id", value: "bill_id" },
      { text: "duty_id", value: "duty_id" },
      { text: "duty_random_id", value: "duty_random_id" },
      { text: "bill_id", value: "edit" },
    ],
    table_list: [],
    rules: {
      lock: [(v) => !!v || "required"],
      lock_2: [
        (v) => !!v || "required",
        (v) => v >= 0 || "required",
        (v) => !!v == Number.isFinite(v) || "Must be integer",
      ],
    },

    date: new Date().toISOString().substr(0, 10),
    dateFormatted: vm.formatDate(new Date().toISOString().substr(0, 10)),
    menu1: false,
    menu2: false,
    ke_bian_mmname: null,

    answer: {
      ke_bian: "",
      uid: 0,
      bill_id: 0,
      data_jsonb: {
        kyat: 0.01,
        dollar: 0.01,
        fee_kyat: null,
        fee_bath: null,
        date: new Date().toISOString().substr(0, 10),
        company_id: null,
        company_name: "",
      },
    },
    produuct_qty: null,
    product_ithem: [],
    items: [],
    items_2: [],
    item_bill_id: null,
    item_product: null,
    // headers_3: [],
    // headers_3:[
    //    { text: "bill_id", value: "bill_id" },
    // ],
    headers_3: [
      { text: "duty_product_idname", value: "duty_product_idname" },
      { text: "duty_product_mm_name", value: "duty_product_mm_name" },
      { text: "duty_product_d_name", value: "duty_product_d_name" },
      { text: "duty_product_qty", value: "duty_product_qty" },
    ],

    desserts_3: [],
  }),

  computed: {
    computedDateFormatted() {
      return this.formatDate(this.date);
    },
  },
  watch: {
    date() {
      this.dateFormatted = this.formatDate(this.date);
    },
  },
  methods: {
    async produuct_idname_keyup() {
      this.checK_idname = true;
      this.produuct_idname = this.produuct_idname.toUpperCase();
      console.log(this.produuct_idname);

      await HTTP.get("shwethe_n/api/v1/product", {
        params: {
          ID: this.produuct_idname,
        },
      }).then((response) => (this.produuct_name = JSON.parse(response.data)));
      console.log(this.produuct_name);

      if (this.produuct_name == false) {
        this.produuct_name = null;
        this.product_ithem = null;
        console.log("");
      } else {
        this.$refs.produuct_qty.focus();
        this.product_ithem = this.produuct_name;
        this.produuct_name = this.produuct_name[0].mm_name;
      }
    },

    _produuct_idname_keyup() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },

    async editItem(item) {
      console.log(item);
      this.item_product = item;
      this.item_bill_id = item.duty_random_id;
      await HTTP_2.post(`shwethe_duty/api/v1/duty_id_detail`, {
        duty_id: item.duty_id,
        duty_random_id: item.duty_random_id,
        bill_id: item.bill_id,
        uid: 0,
      }).then((response) => {
        var PPPP = JSON.parse(response.data);
        if (PPPP[0].data_detail == null) {
          console.log(1);
          this.desserts_3 = [];
        } else {
          console.log(PPPP);
          console.log(2);
          this.desserts_3 = PPPP[0].data_detail;
        }
      });
    },
    onChange(a) {
      this.answer.data_jsonb.company_id = a.company_id;
      this.answer.data_jsonb.company_name = a.company_name;

      console.log(a.company_id);
    },
    formatDate(date) {
      if (!date) return null;

      const [year, month, day] = date.split("-");
      return `${month}/${day}/${year}`;
    },
    parseDate(date) {
      if (!date) return null;

      const [month, day, year] = date.split("/");
      return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    },

    async submitCar() {
      this.overlay = true;
      await HTTP_2.post(`shwethe_duty/api/v1/duty`, this.answer).then(
        (response) => {
          console.log(response);
        }
      );
      location.reload();
    },
    async summit_duty_id() {
      console.log("summit_duty_id");
      var id_random = Date.now().toString();
      console.log(this.product_ithem);
      await HTTP_2.put("shwethe_duty/api/v1/duty_id_detail", {
        duty_random_id: this.item_bill_id,
        data_detail: [
          {
            duty_product_random_id: id_random,
            duty_product_id: this.product_ithem[0].id,
            duty_product_idname: this.product_ithem[0].idname,
            duty_product_qty: this.produuct_qty,
            duty_product_mm_name: this.product_ithem[0].mm_name,
            duty_product_d_name: this.product_ithem[0].d_name,
          },
        ],
      })
        .then(function() {})
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          this.$refs.form.reset();
          // location.reload();
        });

      await HTTP_2.post(`shwethe_duty/api/v1/duty_id_detail`, {
        duty_id: this.item_product.duty_id,
        duty_random_id: this.item_product.duty_random_id,
        bill_id: this.item_product.bill_id,
        uid: 0,
      }).then((response) => {
        var PPPP = JSON.parse(response.data);
        if (PPPP[0].data_detail == null) {
          console.log(1);
          this.desserts_3 = [];
        } else {
          console.log(PPPP);
          console.log(2);
          this.desserts_3 = PPPP[0].data_detail;
        }
      });
    },
  },
  async created() {
    await HTTP_2.get("shwethe_duty/api/v1/duty_by").then(
      (response) => (this.table_list = JSON.parse(response.data))
    );
    console.log(this.table_list);
  },
};
</script>
