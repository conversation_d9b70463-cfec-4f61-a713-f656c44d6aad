<template>
  <v-app>
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <v-card id="printMe">
      <v-card-title>
        Logistics
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>

      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50],
        }"
        :search="search"
        :headers="headers"
        :items="desserts"
        :sort-by="[]"
        multi-sort
      >
        <template v-slot:top>
          <v-dialog v-model="dialog" max-width="1000">
            <v-card>
              <v-card-title class="headline">Edit</v-card-title>

              <v-card-text>
                <v-container>
                  <v-row>
                    <v-col cols="12">
                      <v-simple-table class="style">
                        <template v-slot:top>
                          <thead>
                            <tr>
                              <th class="text-center">idname</th>
                              <th class="text-center">mm_name</th>
                              <th class="text-center">d_name</th>
                              <th class="text-center">fen_x</th>
                              <th class="text-center">qty_x</th>
                              <th class="text-center">want_qty</th>
                              <th class="text-center">fen_y</th>
                              <th class="text-center">qty_y</th>
                              <th class="text-center">type</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ editedItem.idname }}</td>
                              <td>{{ editedItem.mm_name }}</td>
                              <td>{{ editedItem.d_name }}</td>
                              <td>{{ editedItem.fen_x }}</td>
                              <td>{{ editedItem.qty_x }}</td>
                              <td>{{ editedItem.want_qty }}</td>
                              <td>{{ editedItem.fen_y }}</td>
                              <td>{{ editedItem.qty_y }}</td>
                              <td>{{ editedItem.type }}</td>
                            </tr>
                          </tbody>
                        </template>
                      </v-simple-table>
                    </v-col>

                    <v-col cols="12">
                      <v-form
                        ref="form"
                        v-model="valid"
                        @submit.prevent="submitForm"
                        autocomplete="off"
                      >
                        <v-container>
                          <v-row>
                            <v-col cols="12">
                              <v-text-field
                                label="Qty"
                                v-model.number="answer.qty"
                                type="number"
                                :rules="rules.lock"
                              ></v-text-field>
                            </v-col>
                            <div class="w-100"></div>
                            <v-col>
                              <v-btn
                                :disabled="!valid"
                                color="success"
                                class="mr-4"
                                type="submit"
                              >Save</v-btn>
                            </v-col>
                          </v-row>
                          <!-- <pre>{{ answer }}</pre> -->
                        </v-container>
                      </v-form>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card-text>

              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="green darken-1" text @click="dialog = false">Close</v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </template>

        <template v-slot:item.actions="{ item }">
          <div class="my-2">
            <v-icon small @click="deleteItem(item)">delete</v-icon>
          </div>
        </template>

        <template v-slot:item.edit="{ item }">
          <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
        </template>
      </v-data-table>
    </v-card>

    <template>
      <v-snackbar :color="snackbar.color" v-model="snackbar.show">{{ snackbar.message }}</v-snackbar>
    </template>
  </v-app>
</template>

<script>
import { HTTP } from "../../../../plugins/http";

export default {
  data() {
    return {
      output: null,
      search: "",
      dialog: false,
      valid: false,

      snackbar: {
        show: false,
        message: null,
        color: null
      },

      // require
      rules: {
        lock: [v => !!v || "Field is required"]
      },

      // answer
      answer: {
        qty: ""
      },

      headers: [
        { text: "Edit", value: "actions", sortable: false },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "d_name", value: "d_name" },
        { text: "fen_x", value: "fen_x" },
        { text: "qty_x", value: "qty_x" },
        { text: "want_qty", value: "want_qty" },
        { text: "fen_y", value: "fen_y" },
        { text: "qty_y", value: "qty_y" },
        { text: "type", value: "type" },
        { text: "Edit", value: "edit", sortable: false }
      ],
      desserts: [
        {
          // id: "",
          idname: "",
          mm_name: "",
          d_name: "",
          fen_x: "",
          qty_x: "",
          want_qty: "",
          fen_y: "",
          qty_y: ""
        }
      ],

      //edit
      delete: {},
      // Match id
      editedItem: [],

      deleterecord:[]
    };
  },
  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },
    // submitForm
    submitForm() {
      this.$refs.form.validate();
      console.log(this.editedItem.product_id, this.answer.qty, this.editedItem.fen_x);
      // mata : {
      //   id: this.editedItem.id,
      //   qty: this.answer.qty,
      //   fen: this.editedItem.fen_x
      // };
      console.log({
        id: this.editedItem.product_id,
        qty: this.answer.qty,
        fen: this.editedItem.fen_x
      });

      HTTP.post("/logistics/insert", {
        id: this.editedItem.product_id,
        qty: this.answer.qty,
        fen: this.editedItem.fen_x
      })
        .then(function() {
          alert("SUCCESS!!");
        })
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          // console.log(this.answer);
          // this.$refs.form.reset();
          this.dialog = false;

          this.deleteItem(this.deleterecord)

          // close get Data
          
          // HTTP.get("logistics").then(
          //   response =>
          //     // console.log(response.data),
          //     (this.desserts = response.data)
          //   //  this.columns = response.data.columns
          // );

        });
    },

    // editItem and delete
    editItem(item) {
      this.dialog = true;
      this.editedItem = Object.assign({}, item);

      this.deleterecord = item

      // this.deleteItem(item)

      // HTTP.post(`oa_list_for_insert`, {}).then((response) => {
      //   response;
      //   this.answer.sid = this.editedItem.id;
      // });
    },
    deleteItem(item) {
      const index = this.desserts.indexOf(item);
      this.desserts.splice(index, 1);

      this.delete = Object.assign({}, item);

      this.snackbar = {
        message: this.delete.mm_name,
        color: "warning",
        show: true
      };
    }
  },
  created: function() {
    HTTP.get("logistics").then(
      response =>
        // console.log(response.data),
        (this.desserts = response.data)
      //  this.columns = response.data.columns
    );
  }
};
</script>
