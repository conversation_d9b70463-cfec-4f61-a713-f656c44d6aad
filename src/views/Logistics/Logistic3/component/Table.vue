<template>
  <v-app class="app">
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <v-card>
      <!-- <v-container> </v-container> -->

      <div id="printMe">
        <v-card-title>
          Logistics2
          <v-spacer></v-spacer>
          <v-select
            label="fen"
            :items="[1, 2]"
            v-model="fenType"
            clearable
          ></v-select>
        </v-card-title>

        <v-data-table
          :footer-props="{
            'items-per-page-options': [10, 20, 30, 40, 50],
          }"
          :search="search"
          :headers="headers"
          :items="filteredItems"
          :sort-by="[]"
          multi-sort
        >
        </v-data-table>
      </div>
    </v-card>
  </v-app>
</template>

<script>
import { shwethe_delivery } from "../../../../plugins/http";

export default {
  data() {
    return {
      search: "",

      headers: [
        { text: "product_idname", value: "product_idname" },
        { text: "product_mm_name", value: "product_mm_name" },
        { text: "product_d_name", value: "product_d_name" },
        { text: "shu_riqi_datetime", value: "shu_riqi_datetime" },
        { text: "jia_yi_fang_a_idname", value: "jia_yi_fang_a_idname" },
        { text: "jia_yi_fang_a_mm_name", value: "jia_yi_fang_a_mm_name" },
        { text: "jia_yi_fang_b_idname", value: "jia_yi_fang_b_idname" },
        { text: "jia_yi_fang_b_mm_name", value: "jia_yi_fang_b_mm_name" },
        { text: "ပစ္စည်းပို့အရေအတွက်", value: "data_sub.product_qty_a" },
        { text: "သတ်မှတ် အရေအတွက်", value: "data_sub.product_qty_b_pre" },
        { text: "လက်ခံ အရေအတွက်", value: "data_sub.product_qty_b" },
      ],
      desserts: [],
      fenType: null,
    };
  },
  computed: {
    filteredItems() {
      return this.desserts.filter((i) => {
        return !this.fenType || i.fen === this.fenType;
      });
    },
  },

  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },
  },

  created: function() {
    shwethe_delivery.get("api/v1/cliend/shwethe_delivery_manager/list_data").then(
      (response) =>
        // (console.log(response.data)),
        (this.desserts = response.data),
        // console.log(response.data),
      //  this.columns = response.data.columns
    );
  },
};
</script>
