<template>
  <div>
    <v-menu v-model="menu2" :close-on-content-click="false" :nudge-right="40" transition="scale-transition" offset-y
      min-width="auto">
      <template v-slot:activator="{ on, attrs }">
        <v-text-field v-model="date" label="Picker without buttons" prepend-icon="mdi-calendar" readonly v-bind="attrs"
          v-on="on"></v-text-field>
      </template>
      <v-date-picker v-model="date" @input="menu2 = false" @change="dateChange"></v-date-picker>
    </v-menu>

    <!-- {{ date }} -->

    <!-- Image Preview Dialog -->
    <v-dialog v-model="imagePreviewDialog" max-width="90vw">
      <v-card>
        <v-card-title class="headline">
          Image Preview
          <v-spacer></v-spacer>
          <v-btn icon class="mr-2" @click="previewRotateLeft">
            <v-icon>mdi-rotate-left</v-icon>
          </v-btn>
          <v-btn icon class="mr-2" @click="previewRotateRight">
            <v-icon>mdi-rotate-right</v-icon>
          </v-btn>
          <v-btn icon @click="imagePreviewDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="pa-0">
          <div class="preview-container" @wheel.prevent="handlePreviewWheel">
            <v-btn icon class="preview-nav-btn preview-nav-left" @click="previousImage" :disabled="currentImageIndex === 0">
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            
            <div class="preview-wrapper" 
              :style="previewZoomStyle"
              @mousedown="startPan"
              @mousemove="pan"
              @mouseup="endPan"
              @mouseleave="endPan">
              <img :src="currentPreviewImage" @load="initPreviewZoom" ref="previewImage" class="preview-image" />
            </div>

            <v-btn icon class="preview-nav-btn preview-nav-right" @click="nextImage" :disabled="currentImageIndex >= previewImages.length - 1">
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </div>
          <div class="text-center py-2">
            {{ currentImageIndex + 1 }} / {{ previewImages.length }}
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Add this new dialog for zooming images -->
    <v-dialog v-model="zoomDialog" fullscreen hide-overlay>
      <v-card>
        <v-toolbar dark color="primary">
          <v-btn icon dark @click="zoomDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
          <v-toolbar-title>Image Detail</v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn icon class="mr-2" @click="rotateLeft">
            <v-icon>mdi-rotate-left</v-icon>
          </v-btn>
          <v-btn icon @click="rotateRight">
            <v-icon>mdi-rotate-right</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="pa-0 fill-height">
          <div class="zoom-container" @wheel.prevent="handleWheel">
            <div class="zoom-wrapper" 
              :style="zoomStyle"
              @mousedown="startFullscreenPan"
              @mousemove="fullscreenPan"
              @mouseup="endFullscreenPan"
              @mouseleave="endFullscreenPan">
              <img :src="currentZoomImage" @load="initZoom" ref="zoomImage" class="zoom-image" />
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <v-card id="printMe" class="aaa">
      <v-card-title>
        Inday
        <v-spacer></v-spacer>
        <v-text-field v-model="search" append-icon="mdi-magnify" label="Search" single-line hide-details></v-text-field>
      </v-card-title>
      <v-data-table :search="search" :headers="headers" :items="desserts" :item-class="customRowClass"
        disable-pagination multi-sort>

        <template v-slot:item.qty="{ item }">
          <v-chip :color="getColor(item.qty, item.FirstuniqleQty, item.LastuniqleQty)">
            {{ item.qty }}
          </v-chip>
        </template>

        <template v-slot:item.average_percent_2="{ item }">
          <v-chip :color="getColor_2(item.average_percent_2, item.distant)">
            {{ item.average_percent_2 }}
          </v-chip>
        </template>

        <template v-slot:item.count="{ item }">
          <v-chip :color="getColor_3(item.count, item.distance_used)">
            {{ item.count }}
          </v-chip>
        </template>

        <template v-slot:item.mean="{ item }">
          <v-chip :color="getColor_4(item.mean)">
            {{ item.mean }}
          </v-chip>
        </template>

        <template v-slot:item.distant="{ item }">
          <v-chip :color="getColor_5(item.distant)">
            {{ item.distant }}
          </v-chip>
        </template>

        <template v-slot:item.percent="{ item }">
          <v-chip :color="getColor_6(item.percent)">
            {{ item.percent }}
          </v-chip>
        </template>

        <template v-slot:item.driver="{ item }">
          <v-chip :color="getColor_7(item.driver, item.FirstuniqleDriver, item.LastuniqleDriver, item.driverCount)">
            {{ item.driver }}
          </v-chip>
        </template>


        <template v-slot:top>
          <v-dialog v-model="dialog" fullscreen hide-overlay transition="dialog-bottom-transition">
            <v-card>
              <v-toolbar dark color="warning">
                <v-btn icon dark @click="dialog = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
                <v-toolbar-title>Petrol Details</v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn small color="success" class="mr-4" @click="success_click()" :disabled="isButtonDisabled">
                  <v-icon left>mdi-check</v-icon>
                  Confirm Success
                </v-btn>
              </v-toolbar>

              <v-container fluid class="pa-4">
                <v-row>
                  <!-- Left side - Data display -->
                  <v-col cols="4">
                    <v-card outlined>
                      <v-card-title class="subtitle-1 font-weight-bold">
                        Record Information
                      </v-card-title>
                      <v-card-text class="pa-2">
                        <v-row dense>
                          <template v-for="(data, index) in getEdit">
                            <template v-if="!index.includes('image')">
                              <v-col cols="12" :key="index" class="py-1">
                                <div class="d-flex align-center record-item">
                                  <div class="label-bg px-2 py-1">{{ index }}</div>
                                  <div class="px-2 text-body-2">{{ data }}</div>
                                </div>
                              </v-col>
                            </template>
                          </template>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>

                  <!-- Right side - Images -->
                  <v-col cols="8">
                    <v-card outlined>
                      <v-card-title class="subtitle-1 font-weight-bold">
                        Images
                        <v-spacer></v-spacer>
                        <v-btn icon class="mr-2" @click="rotateLeft">
                          <v-icon>mdi-rotate-left</v-icon>
                        </v-btn>
                        <v-btn icon @click="rotateRight">
                          <v-icon>mdi-rotate-right</v-icon>
                        </v-btn>
                      </v-card-title>
                      <v-card-text>
                        <v-row>
                          <v-col cols="6" v-if="expandImg && expandImg.length">
                            <v-card flat class="mb-4">
                              <v-card-subtitle class="py-1">Kilometer Image</v-card-subtitle>
                              <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                <template v-if="Array.isArray(expandImg)">
                                  <div v-for="(img, index) in expandImg" :key="'km-'+index" class="mb-4">
                                    <v-img 
                                      :src="'http://' + img" 
                                      max-height="400"
                                      contain
                                      class="grey lighten-2 cursor-zoom-in"
                                      @click="openZoom('http://' + img)"
                                    ></v-img>
                                  </div>
                                </template>
                              </div>
                            </v-card>
                          </v-col>

                          <v-col cols="6" v-if="expandImg3 && expandImg3.length">
                            <v-card flat class="mb-4">
                              <v-card-subtitle class="py-1">Kilometer Image 2</v-card-subtitle>
                              <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                <template v-if="Array.isArray(expandImg3)">
                                  <div v-for="(img, index) in expandImg3" :key="'km2-'+index" class="mb-4">
                                    <v-img 
                                      :src="'http://' + img" 
                                      max-height="400"
                                      contain
                                      class="grey lighten-2 cursor-zoom-in"
                                      @click="openZoom('http://' + img)"
                                    ></v-img>
                                  </div>
                                </template>
                              </div>
                            </v-card>
                          </v-col>

                          <v-col cols="6" v-if="expandImg2">
                            <v-card flat class="mb-4">
                              <v-card-subtitle class="py-1">Accept Image</v-card-subtitle>
                              <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                <v-img 
                                  :src="expandImg2" 
                                  max-height="400"
                                  contain
                                  class="grey lighten-2 cursor-zoom-in"
                                  @click="openZoom(expandImg2)"
                                ></v-img>
                              </div>
                            </v-card>
                          </v-col>

                          <v-col cols="6" v-if="expandImg4">
                            <v-card flat class="mb-4">
                              <v-card-subtitle class="py-1">Bill Image</v-card-subtitle>
                              <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                <v-img 
                                  :src="expandImg4" 
                                  max-height="400"
                                  contain
                                  class="grey lighten-2 cursor-zoom-in"
                                  @click="openZoom(expandImg4)"
                                ></v-img>
                              </div>
                            </v-card>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-container>

            </v-card>
          </v-dialog>
        </template>

        <template v-slot:item.imageKm="{ item }">
          <div class="p-2 d-flex">
            <template v-if="Array.isArray(item.imageKm)">
              <div v-for="(img, index) in item.imageKm" :key="index" class="mr-2">
                <v-img 
                  :src="'http://' + img" 
                  :alt="item.petrol_id" 
                  style="width: 50px; height: 50px; cursor: pointer"
                  @click="openImagePreview(item.imageKm, index)"
                ></v-img>
              </div>
            </template>
            <template v-else>
              <v-img 
                :src="item.imageKm" 
                :alt="item.petrol_id" 
                style="width: 50px; height: 50px; cursor: pointer"
                @click="openImagePreview([item.imageKm], 0)"
              ></v-img>
            </template>
          </div>
        </template>
        <template v-slot:item.imageKm2="{ item }">
          <div class="p-2 d-flex">
            <template v-if="Array.isArray(item.imageKm2)">
              <div v-for="(img, index) in item.imageKm2" :key="index" class="mr-2">
                <v-img 
                  :src="'http://' + img" 
                  :alt="item.petrol_id" 
                  style="width: 50px; height: 50px; cursor: pointer"
                  @click="openImagePreview(item.imageKm2, index)"
                ></v-img>
              </div>
            </template>
            <template v-else>
              <v-img 
                :src="item.imageKm2" 
                :alt="item.petrol_id" 
                style="width: 50px; height: 50px; cursor: pointer"
                @click="openImagePreview([item.imageKm2], 0)"
              ></v-img>
            </template>
          </div>
        </template>
        <template v-slot:item.imageAccept="{ item }">
          <div class="p-2">
            <v-img 
              :src="item.imageAccept" 
              :alt="item.petrol_id" 
              style="width: 50px; height: 50px; cursor: pointer"
              @click="openImagePreview([item.imageAccept], 0)"
            ></v-img>
          </div>
        </template>
        <template v-slot:item.imageBill="{ item }">
          <div class="p-2">
            <v-img 
              :src="item.imageBill" 
              :alt="item.petrol_id" 
              style="width: 50px; height: 50px; cursor: pointer"
              @click="openImagePreview([item.imageBill], 0)"
            ></v-img>
          </div>
        </template>

        <template v-slot:item.edit="{ item }">
          <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
        </template>
      </v-data-table>
    </v-card>
  </div>
</template>

<script>
import { PetrolProduct } from "../../../plugins/http";
export default {
  data: () => ({
    rotation: 0,
    date: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
      .toISOString()
      .substr(0, 10),
    menu: false,
    modal: false,
    menu2: false,

    isButtonDisabled: false,

    getEdit: [],
    get_petrol_id: null,
    expandImg: null,
    expandImg2: null,
    expandImg3: null,
    expandImg4: null,

    dialog: false,
    search: "",
    headers: [
      { text: "fen_dian_id", value: "fen_dian_id" },
      { text: "datetime", value: "datetime" },
      { text: "datetime_bill", value: "datetime_bill" },
      { text: "diff", value: "diff" },
      { text: "mean", value: "mean" },
      { text: "percent", value: "percent" },
      { text: "petrol_id", value: "petrol_id" },
      { text: "che_liang_id", value: "che_liang_id" },
      { text: "jia_yi_idname", value: "jia_yi_idname" },
      { text: "jia_yi_mm_name", value: "jia_yi_mm_name" },
      { text: "driverCount", value: "driverCount" },
      { text: "driver", value: "driver" },
      { text: "uniqleDriver", value: "uniqleDriver" },
      // { text: "FirstuniqleDriver", value: "FirstuniqleDriver" },
      // { text: "LastuniqleDriver", value: "LastuniqleDriver" },
      { text: "qty", value: "qty" },
      { text: "uniqleQty", value: "uniqleQty" },
      // { text: "FirstuniqleQty", value: "FirstuniqleQty" },
      // { text: "LastuniqleQty", value: "LastuniqleQty" },
      { text: "distant", value: "distant" },
      { text: "distance_used", value: "distance_used" },
      { text: "mean_distance_used_2", value: "mean_distance_used_2" },
      { text: "average_percent_2", value: "average_percent_2" },
      { text: "mean", value: "mean" },
      // { text: "count", value: "count" },
      { text: "bill", value: "bill" },
      { text: "status", value: "status" },
      { text: "imageKm", value: "imageKm" },
      { text: "imageKm2", value: "imageKm2" },
      { text: "imageAccept", value: "imageAccept" },
      { text: "imageBill", value: "imageBill" },
      { text: "Edit", value: "edit" },
    ],
    desserts: [],
    imagePreviewDialog: false,
    currentImageIndex: 0,
    previewImages: [],
    previewRotation: 0,
    zoomDialog: false,
    currentZoomImage: null,
    zoomLevel: 1,
    minZoom: 0.5,
    maxZoom: 5,
    previewZoomLevel: 1,
    previewPanX: 0,
    previewPanY: 0,
    isPanning: false,
    lastX: 0,
    fullscreenPanX: 0,
    fullscreenPanY: 0,
    isFullscreenPanning: false,
    fullscreenLastX: 0,
    fullscreenLastY: 0,
  }),

  methods: {
    getColor(qty, FirstuniqleQty, LastuniqleQty) {
      if (qty != FirstuniqleQty) return 'purple'
      if (FirstuniqleQty == LastuniqleQty) return ''
      if (qty == LastuniqleQty) return 'purple'
      return ''
    },
    getColor_2(average_percent_2, distant) {
      if (average_percent_2 <= -18) return 'red'
      if (average_percent_2 >= 20) return 'green'
      if (average_percent_2 == 0 && distant != 0) return 'red'
      return ''
    },
    getColor_3(count, distance_used) {
      if (distance_used != 0 && count >= 4) return 'purple'
      return ''
    },
    getColor_4(mean) {
      if (mean >= 20) return 'purple'
      if (mean <= -20) return 'purple'
      return ''
    },
    getColor_5(distant) {
      if (distant < 0) return 'red'
      return ''
    },
    getColor_6(percent) {
      if (percent <= -30) return 'red'
      return ''
    },
    // getColor_7(driver, FirstuniqleDriver, LastuniqleDriver) {
    //   if (driver != FirstuniqleDriver) return 'purple'
    //   if (FirstuniqleDriver == LastuniqleDriver) return ''
    //   if (driver == LastuniqleDriver) return 'purple'
    //   return ''
    // },
    getColor_7(driver, FirstuniqleDriver, LastuniqleDriver, driverCount) {
      if (driver != FirstuniqleDriver) return 'purple'
      if (FirstuniqleDriver == LastuniqleDriver) return ''
      if ((driver == LastuniqleDriver && driverCount >= 1) && (driver == LastuniqleDriver && driverCount <= 3)) return 'purple'
      return ''
    },

    rotateRight() {
      this.rotation -= 90
    },
    rotateLeft() {
      this.rotation += 90
    },
    customRowClass(item) {
      if (this.desserts.filter(x => x.jia_yi_idname === item.jia_yi_idname).length > 1) return "brown";
      if (item.status == "waiting") return 'yellow';
      // if (item.score == 100) return 'yellow';
      // if (this.desserts.filter(x => x.name === item.name).length > 1) return 'brown';
      return;
    },

    dateChange() {
      console.log("dcdc", this.date);

      this.getInday();
    },

    editItem(item) {
      console.log(item.imageKm);
      this.dialog = true;
      this.getEdit = item;
      this.get_petrol_id = item.petrol_id;
      
      // Handle imageKm (could be array or string)
      this.expandImg = Array.isArray(item.imageKm) ? item.imageKm : [item.imageKm];
      
      // Handle imageKm2 (could be array or string)
      this.expandImg3 = Array.isArray(item.imageKm2) ? item.imageKm2 : [item.imageKm2];
      
      // Handle imageAccept and imageBill (always strings with http://)
      this.expandImg2 = item.imageAccept;
      this.expandImg4 = item.imageBill;
      
      // Reset rotation when opening dialog
      this.rotation = 0;
    },

    success_click() {
      this.isButtonDisabled = true; // Disable the button

      const url = `confirm_status/${this.get_petrol_id}`;
      const requestBody = {
        status: "success"
      };

      PetrolProduct.put(url, requestBody).then((response) => {
        console.log(response.data); // Assuming the response data is in response.data
        this.getInday();
      }).catch((error) => {
        console.error('Error updating status:', error);
        window.alert('Error updating status:', error)

        this.isButtonDisabled = false; // Re-enable the button
      });
    },

    getInday() {
      PetrolProduct.get(`Inday/${this.date}`).then((response) => {
        var result = response.data.map((i) => ({
          ...i,
          imageKm: Array.isArray(i.imageKm) ? i.imageKm : ["" + i.imageKm],
          imageKm2: Array.isArray(i.imageKm2) ? i.imageKm2 : ["" + i.imageKm2],
          imageAccept: "http://" + i.imageAccept,
          imageBill: "http://" + i.imageBill,
        }));
        console.log(result);
        this.desserts = result;

        this.dialog = false;
        this.isButtonDisabled = false; // Re-enable the button
      });
    },

    openImagePreview(images, index) {
      this.previewImages = Array.isArray(images) 
        ? images.map(img => img.startsWith('http://') ? img : 'http://' + img)
        : [images];
      this.currentImageIndex = index;
      this.previewRotation = 0;
      this.previewZoomLevel = 1;
      this.previewPanX = 0; // Reset pan position
      this.previewPanY = 0;
      this.imagePreviewDialog = true;
    },
    
    previousImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--;
      }
    },
    
    nextImage() {
      if (this.currentImageIndex < this.previewImages.length - 1) {
        this.currentImageIndex++;
      }
    },

    previewRotateLeft() {
      this.previewRotation = (this.previewRotation + 90) % 360;
    },
    
    previewRotateRight() {
      this.previewRotation = (this.previewRotation - 90) % 360;
    },

    startFullscreenPan(e) {
      if (this.zoomLevel > 1) {
        this.isFullscreenPanning = true;
        this.fullscreenLastX = e.clientX;
        this.fullscreenLastY = e.clientY;
      }
    },

    fullscreenPan(e) {
      if (!this.isFullscreenPanning) return;
      
      const deltaX = e.clientX - this.fullscreenLastX;
      const deltaY = e.clientY - this.fullscreenLastY;
      
      this.fullscreenPanX += deltaX;
      this.fullscreenPanY += deltaY;
      
      this.fullscreenLastX = e.clientX;
      this.fullscreenLastY = e.clientY;
    },

    endFullscreenPan() {
      this.isFullscreenPanning = false;
    },

    openZoom(imageUrl) {
      this.currentZoomImage = imageUrl;
      this.zoomLevel = 1;
      this.fullscreenPanX = 0; // Reset pan position
      this.fullscreenPanY = 0;
      this.zoomDialog = true;
    },

    handleWheel(event) {
      const delta = Math.sign(event.deltaY) * -0.1;
      const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.zoomLevel + delta));
      this.zoomLevel = newZoom;
    },

    initZoom() {
      this.zoomLevel = 1;
      this.fullscreenPanX = 0; // Reset pan position
      this.fullscreenPanY = 0;
    },

    handlePreviewWheel(event) {
      const delta = Math.sign(event.deltaY) * -0.1;
      const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.previewZoomLevel + delta));
      this.previewZoomLevel = newZoom;
    },

    startPan(e) {
      if (this.previewZoomLevel > 1) {
        this.isPanning = true;
        this.lastX = e.clientX;
        this.lastY = e.clientY;
      }
    },

    pan(e) {
      if (!this.isPanning) return;
      
      const deltaX = e.clientX - this.lastX;
      const deltaY = e.clientY - this.lastY;
      
      this.previewPanX += deltaX;
      this.previewPanY += deltaY;
      
      this.lastX = e.clientX;
      this.lastY = e.clientY;
    },

    endPan() {
      this.isPanning = false;
    },

    initPreviewZoom() {
      this.previewZoomLevel = 1;
      this.previewPanX = 0; // Reset pan position
      this.previewPanY = 0;
    },
  },

  computed: {
    currentPreviewImage() {
      return this.previewImages[this.currentImageIndex];
    },
    zoomStyle() {
      return {
        transform: `translate(${this.fullscreenPanX}px, ${this.fullscreenPanY}px) scale(${this.zoomLevel}) rotate(${this.rotation}deg)`,
        transition: this.isFullscreenPanning ? 'none' : 'transform 0.3s ease',
        cursor: this.zoomLevel > 1 ? 'grab' : 'default',
      }
    },
    previewZoomStyle() {
      return {
        transform: `translate(${this.previewPanX}px, ${this.previewPanY}px) scale(${this.previewZoomLevel}) rotate(${this.previewRotation}deg)`,
        transition: this.isPanning ? 'none' : 'transform 0.3s ease',
        cursor: this.previewZoomLevel > 1 ? 'grab' : 'default',
      }
    },
  },

  created() {
    this.getInday();
  },
};
</script>

<style scoped>
.yellow {
  background-color: brown;
}

.cursor-zoom-in {
  cursor: zoom-in;
}

.zoom-container {
  width: 100%;
  height: calc(100vh - 64px);
  overflow: auto;
  position: relative;
  background: #f5f5f5;
}

.zoom-wrapper {
  min-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;
  user-select: none;
}

.zoom-wrapper:active {
  cursor: grabbing !important;
}

.zoom-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-container {
  width: 100%;
  height: 80vh;
  position: relative;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  overflow: auto;
}

.preview-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  transform-origin: center center;
  min-height: 100%;
  user-select: none;
}

.preview-wrapper:active {
  cursor: grabbing !important;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.preview-nav-btn {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  background: rgba(0, 0, 0, 0.3) !important;
}

.preview-nav-btn:hover {
  background: rgba(0, 0, 0, 0.5) !important;
}

.preview-nav-left {
  left: 16px;
}

.preview-nav-right {
  right: 16px;
}

.label-bg {
  background-color: #ffebee;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.875rem;
  min-width: 120px;
}

.record-item {
  border-bottom: 1px solid #e0e0e0;
}

.record-item:last-child {
  border-bottom: none;
}
</style>