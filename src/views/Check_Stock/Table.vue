<template>
  <v-app>
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <v-card id="printMe">
      <v-card-title>
        Check stock
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>
      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50]
        }"
        :search="search"
        :headers="headers"
        :items="desserts"
        multi-sort
      >
        <template v-slot:top>
          <v-row justify="center">
            <v-dialog
              v-model="dialog"
              fullscreen
              hide-overlay
              transition="dialog-bottom-transition"
            >
              <v-card>
                <v-toolbar dark color="primary">
                  <v-btn icon dark @click="dialog = false">
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                  <v-toolbar-title>Settings</v-toolbar-title>
                  <v-spacer></v-spacer>
                  <v-toolbar-items>
                    <v-btn dark text @click="dialog = false">Save</v-btn>
                  </v-toolbar-items>
                </v-toolbar>
                <v-container>
                  <v-row>
                    <v-col cols="12" sm="6" md="4">
                      <v-text-field
                        v-model="editedItem.th_name"
                        label="Dessert name"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
            </v-dialog>
          </v-row>
        </template>

        <template v-slot:item.edit="{ item }">
          <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
        </template>
      </v-data-table>
    </v-card>
  </v-app>
</template>

<script>
import { HTTP } from "../../plugins/http";
export default {
  data() {
    return {
      dialog: false,
      notifications: false,
      sound: true,
      widgets: false,
      output: null,
      search: "",
      headers: [
        { text: "Idname", value: "Idname" },
        { text: "idname", value: "idname_x" },
        { text: "mm_name", value: "mm_name_x" },
        { text: "th_name", value: "th_name", size: "50px" },
        { text: "d_name", value: "d_name" },
        { text: "fen", value: "fen" },
        { text: "total_x", value: "total_x" },
        { text: "total_y", value: "total_y" },
        { text: "idname_y", value: "idname_y" },
        { text: "mm_name_y", value: "mm_name_y" },
        { text: "Edit", value: "edit", sortable: false }
      ],
      desserts: [
        {
          // id: "",
          idname_x: "",
          mm_name_x: "",
          d_name: "",
          th_name: "",
          qty_x: "",
          price: "",
          PER: "",
          idname_y: "",
          mm_name_y: ""
        }
      ],
      editedItem: {
        th_name: ""
      }
    };
  },
  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },
    editItem(item) {
      this.dialog = true;
      this.editedItem = Object.assign({}, item);
    }
  },
  created() {
    HTTP.get(`re_h_ans_list`).then(response => {
      console.log(response.data), (this.desserts = response.data);
    });
  }
};
</script>
