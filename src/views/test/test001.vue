<template>
  <v-app>
    <div class="app">
      <!-- :footer-props="{ 'items-per-page-options': [200], }" -->
      <template>
        <v-data-table
          loading-text="Loading... Please wait"
          item-key="index"
          class="elevation-1"
          :loading="loading"
          :headers="headers"
          :items="itemsWithIndex"
          :search="search"
          @click:row="clickedRow"
          :custom-filter="customSearch"
          :single-expand="singleExpand"
          :expanded.sync="expanded"
          show-expand
          multi-sort
        >
          <template v-slot:expanded-item="{ headers, item }">
            <td :colspan="headers.length">
              <v-card flat>
                <v-card-text class="style">
                  <v-row>
                    <v-col cols="6">
                      <v-simple-table class="style">
                        <template v-slot:top>
                          <thead>
                            <tr>
                              <th class="text-center">အိမ်ထောင်</th>
                              <th class="text-center">ဖေ့စ်ဘွတ်ခ်</th>
                              <th class="text-center">လုပ်ငန်းအတွေ့အကြုံ</th>
                              <th class="text-center">ဘယ်ဆိုင်အလုပ်</th>
                              <th class="text-center">မျှော်မှန်းလစာ</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ item.status }}</td>
                              <td>{{ item.facebook }}</td>
                              <td>{{ item.experience }}</td>
                              <td>{{ item.storeplace }}</td>
                              <td>{{ item.expectedSalary }}</td>
                            </tr>
                          </tbody>
                        </template>
                      </v-simple-table>
                    </v-col>
                    <v-col cols="6">
                      <v-simple-table class="style">
                        <template v-slot:top>
                          <thead>
                            <tr>
                              <th class="text-center">#</th>
                              <th class="text-center">language</th>
                              <th class="text-center">speak</th>
                              <th class="text-center">read</th>
                              <th class="text-center">write</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="(edit, index) in item.languages"
                              :key="index"
                            >
                              <td>{{ index + 1 }}</td>
                              <td>{{ edit.language }}</td>
                              <td>{{ edit.speak }}</td>
                              <td>{{ edit.read }}</td>
                              <td>{{ edit.write }}</td>
                            </tr>
                          </tbody>
                        </template>
                      </v-simple-table>
                    </v-col>
                    <v-col cols="6">
                      <v-simple-table class="style">
                        <template v-slot:top>
                          <thead>
                            <tr>
                              <th class="text-center">အဆင့်မြင့်ပညာရေးအဆင့်</th>
                              <th class="text-center">ကျောင်းနာမည်</th>
                              <th class="text-center">ပညာရေးအဆင့်</th>
                              <th class="text-center">မေဂျာ</th>
                              <th class="text-center">အမှတ်စာရင်</th>
                              <th class="text-center">အနာဂတ်လေ့လာ</th>
                              <th class="text-center">ကျောင်းသွားရက်</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{{ item.education }}</td>
                              <td>{{ item.schoolname }}</td>
                              <td>{{ item.level }}</td>
                              <td>{{ item.branch }}</td>
                              <td>{{ item.gpa }}</td>
                              <td>{{ item.study_future }}</td>
                              <td>{{ item.study_plan }}</td>
                            </tr>
                          </tbody>
                        </template>
                      </v-simple-table>
                    </v-col>
                    <v-col cols="6">
                      <v-row>
                        <v-col cols="2">
                          <label><b>လုပ်ချင်အလုပ်</b></label>
                          <hr />
                          <ul
                            v-for="(edit, index) in item.typework"
                            :key="(index, edit)"
                          >
                            <li>{{ edit }}</li>
                          </ul>
                        </v-col>
                        <v-col cols="2">
                          <label><b>အလုပ်ကြော်ငြ</b></label>
                          <hr />
                          <ul
                            v-for="(edit, index) in item.KnowShwethe"
                            :key="(index, edit)"
                          >
                            <li>{{ edit }}</li>
                          </ul>
                        </v-col>
                        <v-col cols="2">
                          <template>
                            <div class="my-2">
                              <v-btn
                                text
                                small
                                color="primary"
                                @click="editItem(item)"
                                >See image</v-btn
                              >
                            </div>
                          </template>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </td>
          </template>
          <template v-slot:top>
            <v-toolbar flat color="white">
              <v-toolbar-title> Job applicant</v-toolbar-title>
              <div class="mx-2">
                <v-btn small color="primary" @click="expandAll"
                  >Expand All</v-btn
                >
              </div>
              <div class="mx-2">
                <v-btn small color="error" @click="collapseAll"
                  >collapse All</v-btn
                >
              </div>
              <v-spacer></v-spacer>

              <v-switch
                v-model="singleExpand"
                label="Single expand"
                class="mt-2"
              ></v-switch>
              <v-spacer></v-spacer>
              <v-text-field
                v-model="search"
                append-icon="mdi-magnify"
                label="Search"
                single-line
                hide-details
              ></v-text-field>
              <v-dialog v-model="edit_table" persistent>
                <v-card>
                  <v-card-title>
                    <span class="headline">Image</span>
                  </v-card-title>

                  <v-card-text>
                    <v-container>
                      <v-row>
                        <v-col cols="6">
                          <img class="img" :src="src" alt="" />
                        </v-col>
                        <v-col cols="6">
                          <v-btn color="blue darken-1" text @click="close"
                            >Back</v-btn
                          >
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card-text>
                </v-card>
              </v-dialog>
            </v-toolbar>

            <v-dialog v-model="dialog" max-width="500px">
              <v-card>
                <v-card-text>
                  <v-container>
                    <h3>{{ editedItem_2.name }}</h3>
                    <v-row>
                      <v-col cols="12" sm="6" md="4">
                        <v-select
                          :items="items"
                          v-model="editedItem_2.check"
                          label="check"
                          dense
                        ></v-select>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>

                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn color="blue darken-1" text @click="close_2">
                    Cancel
                  </v-btn>
                  <v-btn color="blue darken-1" text @click="save_2">
                    Save
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog>
          </template>
          <template v-slot:item.actions="{ item }">
            <v-icon small class="mr-2" @click="editItem_2(item)">
              mdi-pencil
            </v-icon>
          </template>
        </v-data-table>
      </template>

      <v-form ref="form">
        <v-text-field v-model="test.name" label="Name"></v-text-field>

        <v-btn color="success" class="mr-4" @click="validate"> Submit </v-btn>
      </v-form>
    </div>
    <pre>{{ desserts }}</pre>
  </v-app>
</template>


<script>
import { HTTP, HTTP_TEST } from "../../plugins/http";

export default {
  data() {
    return {
      greenRed: null,
      dialog: false,
      dialogDelete: false,
      items: ["green", "red"],

      loading: true,
      search: "",
      expanded: [],
      singleExpand: false,

      headers: [
        { text: "#", value: "index" },
        { text: "အမည်", value: "name" },
        { text: "မွေးသက္ကရာဇ်", value: "birthday" },
        { text: "လိင်", value: "sex" },
        { text: "ဖုန်းနံပါတ်", value: "phone" },
        { text: "Check", value: "check" },
        { text: "Actions", value: "actions", sortable: false },
      ],
      desserts: [],
      //edit
      edit_table: false,
      editedItem: {
        iid: "",
        languages: [
          {
            language: "",
            speak: "",
            read: "",
            write: "",
          },
        ],
        name: "",
        url: "",
      },
      src: "",

      test: {
        name: "",
      },

      editedIndex_2: -1,
      editedItem_2: {
        name: "",
        check: "",
      },
    };
  },

  computed: {
    itemsWithIndex() {
      return this.desserts.map((desserts, index) => ({
        ...desserts,
        index: index + 1,
      }));
    },
  },

  methods: {
    editItem_2(item) {
      this.editedIndex_2 = this.desserts.indexOf(item);
      this.editedItem_2 = Object.assign({}, item);
      this.dialog = true;
      console.log(item);
    },

    close_2() {
      this.dialog = false;
    },

    save_2() {
      if (this.editedIndex_2 > -1) {
        Object.assign(this.desserts[this.editedIndex_2], this.editedItem_2);
        console.log("if");
      } else {
        // this.desserts.push(this.editedItem_2);
        console.log(this.editedItem_2.check);
      }
      this.close_2();
    },

    validate() {
      console.log("ffffffffff");
      HTTP_TEST.post("items", this.test)
        .then(function (response) {
          console.log(response);
        })
        .catch(function (error) {
          console.log(error);
        })
        .finally(() => {});
    },

    // editItem and delete
    editItem(item) {
      this.editedIndex = this.desserts.indexOf(item);
      this.editedItem = Object.assign({}, item);
      this.edit_table = true;
      console.log(this.src);
      HTTP.post("form_job_img_by_id", { iid: item.iid }).then(
        (response) => (this.src = "data:image/jpeg;base64," + response.data)
        // console.log(response)
      );
    },
    close() {
      this.edit_table = false;
      this.src = "";
    },

    // custom filter
    clickedRow(value) {
      if (this.expanded.length && this.expanded[0].id == value.id) {
        this.expanded = [];
      } else {
        this.expanded = [];
        this.expanded.push(value);
      }
    },
    customSearch(value, search, item) {
      return Object.values(item).some(
        (v) => v && v.toString().includes(search)
      );
    },
    expandAll: function () {
      console.log("All expanded.");
      this.$data.expanded = this.itemsWithIndex;
    },
    collapseAll: function () {
      console.log("All collapsed.");
      this.$data.expanded = [];
    },

    // loadHTTP
    async loadHTTP() {
      this.loading = true;
      await HTTP_TEST.get("mongo").then(
        (response) => (
          console.log(response.data), (this.desserts = response.data)
        )
      );
      this.loading = false;
    },
  },

  created: function () {
    this.loadHTTP();
  },
};
</script>

<style scoped>
.margin {
  margin: auto 5% auto 5%;
}

.img {
  width: 100%;
  cursor: pointer;
  border-radius: 4px;
  border: 3px solid white;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
}

.style {
  background-color: mintcream;
}
</style>
