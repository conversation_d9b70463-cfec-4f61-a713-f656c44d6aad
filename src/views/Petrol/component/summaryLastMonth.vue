<template>
  <v-app>
    <div class="app">
      <template>
        <v-card-title>
          Calculate
          <v-spacer></v-spacer>
          <v-text-field v-model="search" append-icon="mdi-magnify" label="Search" single-line hide-details>
          </v-text-field>
        </v-card-title>
        <v-data-table loading-text="Loading... Please wait" item-key="index" class="elevation-1" :loading="loading"
          :headers="headers" :items="desserts" :search="search" :sort-by="['average_2']" :sort-desc="[false]"
          multi-sort disable-pagination>
        </v-data-table>
        <v-btn @click="getInday">Fetch</v-btn>
        <v-btn @click="count-=1">Minus</v-btn>
        <v-btn @click="count+=1">Plus</v-btn>
        {{count}}
      </template>
    </div>
  </v-app>
</template>

<script>
import { PetrolProduct } from "../../../plugins/http";

export default {
  data() {
    return {
      count: -1,

      loading: false,
      search: "",
      expanded: [],
      singleExpand: false,

      headers: [
        { text: "datetime", value: "datetime" },
        { text: "jia_yi_idname", value: "jia_yi_idname" },
        { text: "jia_yi_mm_name", value: "jia_yi_mm_name" },
        { text: "che_liang_id", value: "che_liang_id" },
        { text: "bill", value: "bill" },
        { text: "qty", value: "qty" },
        // { text: "distance_used", value: "distance_used" },
        // { text: "km_lit", value: "km_lit" },
        { text: "distance_used_2", value: "distance_used_2" },
        { text: "km_lit_2", value: "km_lit_2" },
        { text: "mean", value: "mean" },
        { text: "minute", value: "minute" },
        { text: "oneKmLit", value: "oneKmLit" },
        { text: "hundredKmLit", value: "hundredKmLit" },
        { text: "count", value: "count" },
        { text: "mean_distance_used_2", value: "mean_distance_used_2" },
        { text: "average_2", value: "average_2" },
      
      ],
      desserts: [],
      //edit
      edit_table: false,
      editedItem: {
        iid: "",
        // languages: [],
        languages: [
          {
            language: "",
            speak: "",
            read: "",
            write: "",
          },
        ],
        name: "",
        url: "",
      },
      src: "",
    };
  },

  methods: {
    getInday() {
      this.desserts = []
      this.loading = true;
      PetrolProduct.get(`summaryLastMonth/${this.count}`).then((response) => {
        console.log(response.data), (this.desserts = response.data);
      }).catch(function (error) {
        this.loading = false;
        alert(error)
      }).finally(() => {
        this.loading = false;
      });
    },
  },

  created: function () {

  },
};
</script>

<style scoped>
.margin {
  margin: auto 5% auto 5%;
}

.img {
  width: 100%;
  cursor: pointer;
  border-radius: 4px;
  border: 3px solid white;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
}

.style {
  background-color: mintcream;
}
</style>
