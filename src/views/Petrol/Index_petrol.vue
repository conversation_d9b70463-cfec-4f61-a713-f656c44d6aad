<template>
  <v-app>
    <!-- <Inday /> -->
    <!-- <FuelType />
    <Masad />
    <Car />
    <SummaryAll /> -->

    <template>
      <v-card>
        <v-tabs color="deep-purple accent-4" centered>
          <v-tab>Inday</v-tab>
          <v-tab>IndayItem</v-tab>

          <v-tab-item>
            <Inday />
          </v-tab-item>
          <v-tab-item>
            <IndayItem />
          </v-tab-item>
        </v-tabs>
      </v-card>
    </template>

    <template>
      <v-card>
        <v-tabs color="deep-purple accent-4" centered>
          <v-tab>Tab</v-tab>
          <v-tab>FuelType</v-tab>
          <v-tab>Masad</v-tab>
          <!-- <v-tab>MasadDriver</v-tab> -->
          <v-tab>Car</v-tab>

          <v-tab-item>
            Tab
          </v-tab-item>
          <v-tab-item>
            <FuelType />
          </v-tab-item>
          <v-tab-item>
            <Masad />
          </v-tab-item>
          <!-- <v-tab-item>
            <MasadDriver />
          </v-tab-item> -->
          <v-tab-item>
            <Car />
          </v-tab-item>
          <!-- <v-tab-item>
            <Car />
          </v-tab-item> -->
        </v-tabs>
      </v-card>
    </template>

    <template>
      <v-card>
        <v-tabs color="deep-purple accent-4" centered>
          <v-tab>summaryLastMonth</v-tab>
          <v-tab>summaryAll</v-tab>

          <v-tab-item>
            <summaryLastMonth />
          </v-tab-item>
          <v-tab-item>
            <summaryAll />
          </v-tab-item>
        </v-tabs>
      </v-card>
    </template>

  </v-app>
</template>

<script>
import FuelType from "./component/fuelType";
import Inday from "./component/Inday";
import IndayItem from "./component/IndayItem";
import Masad from "./component/Masad";
// import MasadDriver from "./component/masadDriver";
import Car from "./component/Car";
import summaryLastMonth from "./component/summaryLastMonth.vue";
import summaryAll from "./component/summaryAll.vue";
export default {
  components: {
    FuelType,
    Inday,
    IndayItem,
    Masad,
    // MasadDriver,
    Car,
    summaryLastMonth,
    summaryAll
  },
  data() {
    return {
    }
  },
};
</script>
