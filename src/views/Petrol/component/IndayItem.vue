<template>
    <div>
        <!-- Add Image Preview Dialog -->
        <v-dialog v-model="imagePreviewDialog" max-width="90vw">
            <v-card>
                <v-card-title class="headline">
                    Image Preview
                    <v-spacer></v-spacer>
                    <v-btn icon class="mr-2" @click="previewRotateLeft">
                        <v-icon>mdi-rotate-left</v-icon>
                    </v-btn>
                    <v-btn icon class="mr-2" @click="previewRotateRight">
                        <v-icon>mdi-rotate-right</v-icon>
                    </v-btn>
                    <v-btn icon @click="imagePreviewDialog = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </v-card-title>
                <v-card-text class="pa-0">
                    <div class="preview-container" @wheel.prevent="handlePreviewWheel">
                        <div class="preview-wrapper" 
                            :style="previewZoomStyle"
                            @mousedown="startPan"
                            @mousemove="pan"
                            @mouseup="endPan"
                            @mouseleave="endPan">
                            <img :src="currentPreviewImage" @load="initPreviewZoom" ref="previewImage" class="preview-image" />
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <!-- Add Fullscreen Zoom Dialog -->
        <v-dialog v-model="zoomDialog" fullscreen hide-overlay>
            <v-card>
                <v-toolbar dark color="primary">
                    <v-btn icon dark @click="zoomDialog = false">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                    <v-toolbar-title>Image Detail</v-toolbar-title>
                    <v-spacer></v-spacer>
                    <v-btn icon class="mr-2" @click="rotateLeft">
                        <v-icon>mdi-rotate-left</v-icon>
                    </v-btn>
                    <v-btn icon @click="rotateRight">
                        <v-icon>mdi-rotate-right</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text class="pa-0 fill-height">
                    <div class="zoom-container" @wheel.prevent="handleWheel">
                        <div class="zoom-wrapper" 
                            :style="zoomStyle"
                            @mousedown="startFullscreenPan"
                            @mousemove="fullscreenPan"
                            @mouseup="endFullscreenPan"
                            @mouseleave="endFullscreenPan">
                            <img :src="currentZoomImage" @load="initZoom" ref="zoomImage" class="zoom-image" />
                        </div>
                    </div>
                </v-card-text>
            </v-card>
        </v-dialog>

        <v-card id="printMe" class="aaa">
            <v-container>
                <v-row>
                    <v-col>
                        <v-text-field label="che_liang_id" v-model="che_liang_id"></v-text-field>
                    </v-col>
                    <v-col>
                        <v-btn @click="searchIndayItem">Click</v-btn>
                    </v-col>
                </v-row>
            </v-container>

            <v-card-title>
                IndayItem
                <v-spacer></v-spacer>
                <v-text-field v-model="search" append-icon="mdi-magnify" label="Search" single-line hide-details>
                </v-text-field>
            </v-card-title>
            <v-data-table :search="search" :headers="headers" :items="desserts" :items-per-page="3" multi-sort>
                <template v-slot:top>
                    <v-dialog v-model="dialog" fullscreen hide-overlay transition="dialog-bottom-transition">
                        <v-card>
                            <v-toolbar dark color="warning">
                                <v-btn icon dark @click="dialog = false">
                                    <v-icon>mdi-close</v-icon>
                                </v-btn>
                                <v-toolbar-title>Petrol</v-toolbar-title>
                                <v-spacer></v-spacer>
                            </v-toolbar>

                            <v-container>
                                <v-row>
                                    <v-col cols="6">
                                        <div v-for="(data, index) in getEdit" :key="index">
                                            <h3 style="background-color: pink">{{ index }}</h3>
                                            <v-spacer></v-spacer> {{ data }}
                                        </div>
                                    </v-col>
                                    <v-col cols="6">
                                        <v-card outlined>
                                            <v-card-title class="subtitle-1 font-weight-bold">
                                                Images
                                                <v-spacer></v-spacer>
                                                <v-btn icon class="mr-2" @click="rotateLeft">
                                                    <v-icon>mdi-rotate-left</v-icon>
                                                </v-btn>
                                                <v-btn icon @click="rotateRight">
                                                    <v-icon>mdi-rotate-right</v-icon>
                                                </v-btn>
                                            </v-card-title>
                                            <v-card-text>
                                                <v-row>
                                                    <v-col cols="6" v-if="expandImg">
                                                        <v-card flat class="mb-4">
                                                            <v-card-subtitle class="py-1">Kilometer Image</v-card-subtitle>
                                                            <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                                                <template v-if="Array.isArray(expandImg)">
                                                                    <div v-for="(img, index) in expandImg" :key="'km-'+index" class="mb-4">
                                                                        <v-img 
                                                                            :src="img" 
                                                                            max-height="400"
                                                                            contain
                                                                            class="grey lighten-2 cursor-zoom-in"
                                                                            @click="openImagePreview(expandImg, index)"
                                                                        ></v-img>
                                                                    </div>
                                                                </template>
                                                                <template v-else>
                                                                    <v-img 
                                                                        :src="expandImg" 
                                                                        max-height="400"
                                                                        contain
                                                                        class="grey lighten-2 cursor-zoom-in"
                                                                        @click="openImagePreview([expandImg], 0)"
                                                                    ></v-img>
                                                                </template>
                                                            </div>
                                                        </v-card>
                                                    </v-col>

                                                    <v-col cols="6" v-if="expandImg3">
                                                        <v-card flat class="mb-4">
                                                            <v-card-subtitle class="py-1">Kilometer Image 2</v-card-subtitle>
                                                            <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                                                <template v-if="Array.isArray(expandImg3)">
                                                                    <div v-for="(img, index) in expandImg3" :key="'km2-'+index" class="mb-4">
                                                                        <v-img 
                                                                            :src="img" 
                                                                            max-height="400"
                                                                            contain
                                                                            class="grey lighten-2 cursor-zoom-in"
                                                                            @click="openImagePreview(expandImg3, index)"
                                                                        ></v-img>
                                                                    </div>
                                                                </template>
                                                                <template v-else>
                                                                    <v-img 
                                                                        :src="expandImg3" 
                                                                        max-height="400"
                                                                        contain
                                                                        class="grey lighten-2 cursor-zoom-in"
                                                                        @click="openImagePreview([expandImg3], 0)"
                                                                    ></v-img>
                                                                </template>
                                                            </div>
                                                        </v-card>
                                                    </v-col>

                                                    <v-col cols="6" v-if="expandImg2">
                                                        <v-card flat class="mb-4">
                                                            <v-card-subtitle class="py-1">Accept Image</v-card-subtitle>
                                                            <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                                                <v-img 
                                                                    :src="expandImg2" 
                                                                    max-height="400"
                                                                    contain
                                                                    class="grey lighten-2 cursor-zoom-in"
                                                                    @click="openImagePreview([expandImg2], 0)"
                                                                ></v-img>
                                                            </div>
                                                        </v-card>
                                                    </v-col>

                                                    <v-col cols="6" v-if="expandImg4">
                                                        <v-card flat class="mb-4">
                                                            <v-card-subtitle class="py-1">Bill Image</v-card-subtitle>
                                                            <div class="text-center" :style="`transform: rotate(${rotation}deg); transition: transform 0.3s ease;`">
                                                                <v-img 
                                                                    :src="expandImg4" 
                                                                    max-height="400"
                                                                    contain
                                                                    class="grey lighten-2 cursor-zoom-in"
                                                                    @click="openImagePreview([expandImg4], 0)"
                                                                ></v-img>
                                                            </div>
                                                        </v-card>
                                                    </v-col>
                                                </v-row>
                                            </v-card-text>
                                        </v-card>
                                    </v-col>
                                </v-row>
                            </v-container>

                        </v-card>
                    </v-dialog>
                </template>

                <template v-slot:item.imageKm="{ item }">
                    <div class="p-2 d-flex">
                        <template v-if="Array.isArray(item.imageKm)">
                            <div v-for="(img, index) in item.imageKm" :key="index" class="mr-2">
                                <v-img 
                                    :src="img" 
                                    :alt="item.petrol_id" 
                                    max-width="500"
                                    contain
                                    @click="openImagePreview(item.imageKm, index)"
                                    class="grey lighten-2 cursor-zoom-in"
                                ></v-img>
                            </div>
                        </template>
                        <template v-else>
                            <v-img 
                                :src="item.imageKm" 
                                :alt="item.petrol_id" 
                                max-width="500"
                                contain
                                @click="openImagePreview([item.imageKm], 0)"
                                class="grey lighten-2 cursor-zoom-in"
                            ></v-img>
                        </template>
                    </div>
                </template>
                <template v-slot:item.imageKm2="{ item }">
                    <div class="p-2 d-flex">
                        <template v-if="Array.isArray(item.imageKm2)">
                            <div v-for="(img, index) in item.imageKm2" :key="index" class="mr-2">
                                <v-img 
                                    :src="img" 
                                    :alt="item.petrol_id" 
                                    max-width="500"
                                    contain
                                    @click="openImagePreview(item.imageKm2, index)"
                                    class="grey lighten-2 cursor-zoom-in"
                                ></v-img>
                            </div>
                        </template>
                        <template v-else>
                            <v-img 
                                :src="item.imageKm2" 
                                :alt="item.petrol_id" 
                                max-width="500"
                                contain
                                @click="openImagePreview([item.imageKm2], 0)"
                                class="grey lighten-2 cursor-zoom-in"
                            ></v-img>
                        </template>
                    </div>
                </template>
                <template v-slot:item.imageAccept="{ item }">
                    <div class="p-2">
                        <v-img 
                            :src="item.imageAccept" 
                            :alt="item.petrol_id" 
                            style="width: 500px; height: 300px; cursor: pointer"
                            @click="openImagePreview([item.imageAccept], 0)"
                            class="grey lighten-2 cursor-zoom-in"
                        ></v-img>
                    </div>
                </template>
                <template v-slot:item.imageBill="{ item }">
                    <div class="p-2">
                        <v-img 
                            :src="item.imageBill" 
                            :alt="item.petrol_id" 
                            style="width: 500px; height: 300px; cursor: pointer"
                            @click="openImagePreview([item.imageBill], 0)"
                            class="grey lighten-2 cursor-zoom-in"
                        ></v-img>
                    </div>
                </template>

                <template v-slot:item.edit="{ item }">
                    <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
                </template>
            </v-data-table>
        </v-card>
    </div>
</template>

<script>
import { PetrolProduct } from "../../../plugins/http";
export default {
    data: () => ({
        rotation: 0,

        getEdit: [],
        expandImg: null,
        expandImg2: null,
        expandImg3: null,
        expandImg4: null,

        dialog: false,
        search: "",
        che_liang_id: "",
        headers: [
            // { text: "fen_dian_id", value: "fen_dian_id" },
            { text: "datetime", value: "datetime" },
            { text: "datetime_bill", value: "datetime_bill" },
            { text: "petrol_id", value: "petrol_id" },
            { text: "che_liang_id", value: "che_liang_id" },
            { text: "jia_yi_idname", value: "jia_yi_idname" },
            { text: "jia_yi_mm_name", value: "jia_yi_mm_name" },
            { text: "qty", value: "qty" },
            { text: "distant", value: "distant" },
            // { text: "distance_used", value: "distance_used" },
            // { text: "mean_distance_used_2", value: "mean_distance_used_2" },
            // { text: "average_percent_2", value: "average_percent_2" },
            // { text: "average_percent", value: "average_percent" },
            // { text: "mean", value: "mean" },
            // { text: "count", value: "count" },
            // { text: "bill", value: "bill" },
            // { text: "status", value: "status" },
            { text: "imageKm", value: "imageKm" },
            { text: "imageKm2", value: "imageKm2" },
            { text: "imageAccept", value: "imageAccept" },
            { text: "imageBill", value: "imageBill" },
            { text: "Edit", value: "edit" },
        ],
        desserts: [],
        imagePreviewDialog: false,
        currentImageIndex: 0,
        previewImages: [],
        previewRotation: 0,
        zoomDialog: false,
        currentZoomImage: null,
        zoomLevel: 1,
        minZoom: 0.5,
        maxZoom: 5,
        previewZoomLevel: 1,
        previewPanX: 0,
        previewPanY: 0,
        isPanning: false,
        lastX: 0,
        lastY: 0,
        fullscreenPanX: 0,
        fullscreenPanY: 0,
        isFullscreenPanning: false,
        fullscreenLastX: 0,
        fullscreenLastY: 0,
    }),

    methods: {
        searchIndayItem() {
            PetrolProduct.get(`getIndayItem/${this.che_liang_id}`).then((response) => {
                var result = response.data.map((i) => ({
                    ...i,
                    imageKm: Array.isArray(i.imageKm) ? i.imageKm.map(img => "http://" + img) : "http://" + i.imageKm,
                    imageKm2: Array.isArray(i.imageKm2) ? i.imageKm2.map(img => "http://" + img) : "http://" + i.imageKm2,
                    imageAccept: "http://" + i.imageAccept,
                    imageBill: "http://" + i.imageBill,
                }));
                console.log(result);
                this.desserts = result;
            });
        },

        rotateRight() {
            this.rotation -= 90
        },
        rotateLeft() {
            this.rotation += 90
        },

        editItem(item) {
            console.log(item.imageKm);
            this.dialog = true;
            this.getEdit = item;
            this.expandImg = item.imageKm;
            this.expandImg3 = item.imageKm2;
            this.expandImg2 = item.imageAccept
            this.expandImg4 = item.imageBill
        },

        openImagePreview(images, index) {
            this.previewImages = images;
            this.currentImageIndex = index;
            this.previewRotation = 0;
            this.previewZoomLevel = 1;
            this.previewPanX = 0;
            this.previewPanY = 0;
            this.imagePreviewDialog = true;
        },

        openZoom(imageUrl) {
            this.currentZoomImage = imageUrl;
            this.zoomLevel = 1;
            this.fullscreenPanX = 0;
            this.fullscreenPanY = 0;
            this.zoomDialog = true;
        },

        handlePreviewWheel(event) {
            const delta = Math.sign(event.deltaY) * -0.1;
            const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.previewZoomLevel + delta));
            this.previewZoomLevel = newZoom;
        },

        handleWheel(event) {
            const delta = Math.sign(event.deltaY) * -0.1;
            const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.zoomLevel + delta));
            this.zoomLevel = newZoom;
        },

        startPan(e) {
            if (this.previewZoomLevel > 1) {
                this.isPanning = true;
                this.lastX = e.clientX;
                this.lastY = e.clientY;
            }
        },

        pan(e) {
            if (!this.isPanning) return;
            const deltaX = e.clientX - this.lastX;
            const deltaY = e.clientY - this.lastY;
            this.previewPanX += deltaX;
            this.previewPanY += deltaY;
            this.lastX = e.clientX;
            this.lastY = e.clientY;
        },

        endPan() {
            this.isPanning = false;
        },

        startFullscreenPan(e) {
            if (this.zoomLevel > 1) {
                this.isFullscreenPanning = true;
                this.fullscreenLastX = e.clientX;
                this.fullscreenLastY = e.clientY;
            }
        },

        fullscreenPan(e) {
            if (!this.isFullscreenPanning) return;
            const deltaX = e.clientX - this.fullscreenLastX;
            const deltaY = e.clientY - this.fullscreenLastY;
            this.fullscreenPanX += deltaX;
            this.fullscreenPanY += deltaY;
            this.fullscreenLastX = e.clientX;
            this.fullscreenLastY = e.clientY;
        },

        endFullscreenPan() {
            this.isFullscreenPanning = false;
        },

        initPreviewZoom() {
            this.previewZoomLevel = 1;
            this.previewPanX = 0;
            this.previewPanY = 0;
        },

        initZoom() {
            this.zoomLevel = 1;
            this.fullscreenPanX = 0;
            this.fullscreenPanY = 0;
        },

        previewRotateLeft() {
            this.previewRotation = (this.previewRotation + 90) % 360;
        },
        
        previewRotateRight() {
            this.previewRotation = (this.previewRotation - 90) % 360;
        },
    },

    computed: {
        currentPreviewImage() {
            return this.previewImages[this.currentImageIndex];
        },
        
        zoomStyle() {
            return {
                transform: `translate(${this.fullscreenPanX}px, ${this.fullscreenPanY}px) scale(${this.zoomLevel}) rotate(${this.rotation}deg)`,
                transition: this.isFullscreenPanning ? 'none' : 'transform 0.3s ease',
                cursor: this.zoomLevel > 1 ? 'grab' : 'default',
            }
        },
        
        previewZoomStyle() {
            return {
                transform: `translate(${this.previewPanX}px, ${this.previewPanY}px) scale(${this.previewZoomLevel}) rotate(${this.previewRotation}deg)`,
                transition: this.isPanning ? 'none' : 'transform 0.3s ease',
                cursor: this.previewZoomLevel > 1 ? 'grab' : 'default',
            }
        },
    },

    created() {
        // this.getInday();
    },
};
</script>

<style scoped>
.yellow {
    background-color: brown;
}

.cursor-zoom-in {
    cursor: zoom-in;
}

.zoom-container {
    width: 100%;
    height: calc(100vh - 64px);
    overflow: auto;
    position: relative;
    background: #f5f5f5;
}

.zoom-wrapper {
    min-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform-origin: center center;
    user-select: none;
}

.zoom-wrapper:active {
    cursor: grabbing !important;
}

.zoom-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.preview-container {
    width: 100%;
    height: 80vh;
    position: relative;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    overflow: auto;
}

.preview-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transform-origin: center center;
    min-height: 100%;
    user-select: none;
}

.preview-wrapper:active {
    cursor: grabbing !important;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
</style>