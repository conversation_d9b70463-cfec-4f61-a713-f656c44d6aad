<template>
  <div>
    <v-container>
      <b-row>
        <!-- <pre>{{ answer }}</pre> -->
        <b-col md="12" sm="12">
          <v-card id="printMe">
            <v-card-title>
              Judysssssssssssssss
              <v-spacer></v-spacer>
            </v-card-title>

            <v-data-table
              :headers="headers"
              :items="desserts"
              :items-per-page="10"
              class="elevation-1"
            >
              <template v-slot:item.qty_ans="{ item }">
                <v-chip :color="getColor_2(item.qty_ans)" dark>
                  {{ item.qty_ans }}
                </v-chip>
              </template>
              <template v-slot:top>
                <v-dialog v-model="dialog" hide-overlay max-width="80%">
                  <v-card>
                    <v-card-title class="headline"></v-card-title>
                    <v-card-text>
                      <v-data-table
                        :headers="headers_2"
                        :items="desserts_2"
                        :items-per-page="5"
                        class="elevation-1"
                      >
                      </v-data-table>
                    </v-card-text>
                  </v-card>
                </v-dialog>
              </template>
              <template v-slot:item.search="{ item }">
                <div v-if="item.product_id_y_ > 0.5">
                  <v-btn small color="success" @click="searchItem(item)">Search</v-btn>
                </div>
                <div v-if="item.product_id_y_ <= 0.5">
                  <v-btn small color="success" @click="searchItem(item)">Search</v-btn>
                </div>
              </template>
              <template v-slot:item.che_liang_mmname_2="{ item }">
                <v-btn small color="primary" @click="click_bill_id(item)">View</v-btn>
              </template>
              <template v-slot:item.che_liang_mmname_3="{ item }">
                <v-btn small color="primary" @click="click_bill_id2(item)">View2</v-btn>
              </template>
              <template v-slot:item.che_liang_mmname_4="{ item }">
                <v-btn small color="primary" @click="searchItem____5(item)">View3</v-btn>
              </template>
              <template v-slot:item.status="{ item }">
                <v-chip :color="getColor_3(item.status)" dark>
                  {{ item.status }}
                </v-chip>
              </template>
              <template v-slot:top>
                <v-dialog v-model="dialog2" hide-overlay max-width="30%">
                  <v-card>
                    <v-card-title class="headline"></v-card-title>

                    <v-card-text>
                      <p>
                        <b>Car Round</b>
                      </p>
                      <v-btn @click="summit_click" color="success" class="mr-4">
                        Save
                      </v-btn>
                      <v-btn
                        :disabled="!isPasswordCorrect"
                        @click="summit_click_cancel"
                        color="success"
                        class="mr-4"
                      >
                        can cancel
                      </v-btn>

                      <v-text-field
                        v-model="password"
                        label="Password"
                        type="password"
                      ></v-text-field>
                      <v-btn @click="checkPassword" color="success" class="mr-4">
                        Check Password
                      </v-btn>
                    </v-card-text>
                  </v-card>
                </v-dialog>
              </template>
            </v-data-table>
          </v-card>
        </b-col>
      </b-row>

      <b-row>
        <!-- <pre>{{ answer }}</pre> -->
        <b-col md="12" sm="12">
          <v-card id="printMe">
            <v-card-title>
              Judy
              <v-spacer></v-spacer>
            </v-card-title>
            <v-data-table
              :headers="headers3"
              :items="desserts_3"
              :items-per-page="10"
              class="elevation-1"
            >
              <template v-slot:item.qty_ans="{ item }">
                <v-chip :color="getColor_2(item.qty_ans)" dark>
                  {{ item.qty_ans }}
                </v-chip>
              </template>
              <template v-slot:top>
                <v-dialog v-model="dialog" hide-overlay max-width="80%">
                  <v-card>
                    <v-card-title class="headline"></v-card-title>
                    <v-card-text>
                      <v-data-table
                        :headers="headers_2"
                        :items="desserts_2"
                        :items-per-page="5"
                        class="elevation-1"
                      >
                      </v-data-table>
                    </v-card-text>
                  </v-card>
                </v-dialog>
              </template>
              <template v-slot:item.search="{ item }">
                <div v-if="item.product_id_y_ > 0.5">
                  <v-btn small color="success" @click="searchItem(item)">Search</v-btn>
                </div>
                <div v-if="item.product_id_y_ <= 0.5">
                  <v-btn small color="success" @click="searchItem(item)">Search</v-btn>
                </div>
              </template>
              <template v-slot:item.che_liang_mmname_2="{ item }">
                <v-btn small color="primary" @click="click_bill_id(item)">View</v-btn>
              </template>
              <template v-slot:item.che_liang_mmname_3="{ item }">
                <v-btn small color="primary" @click="click_bill_id2(item)">View2</v-btn>
              </template>
            </v-data-table>
          </v-card>
        </b-col>
      </b-row>
    </v-container>
    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import { shwethe_order_in_fast_api } from "../../../../plugins/http";
export default {
  data: () => ({
    desserts: [],
    desserts_2: [],
    desserts_3: [],
    headers_2: [],
    headers: [
      {
        text: "jin_huo_bian",
        value: "jin_huo_bian",
      },
      {
        text: "che_liang_idname",
        value: "che_liang_idname",
      },
      {
        text: "che_liang_mmname",
        value: "che_liang_mmname",
      },
      {
        text: "che_liang_mmname_2",
        value: "che_liang_mmname_2",
      },
      {
        text: "che_liang_mmname_3",
        value: "che_liang_mmname_3",
      },
      {
        text: "che_liang_mmname_4",
        value: "che_liang_mmname_4",
      },
      {
        text: "status",
        value: "status",
      },
    ],
    headers3: [
      {
        text: "datetime",
        value: "datetime",
      },
      {
        text: "product_idname",
        value: "product_idname",
      },
      {
        text: "product_mmname",
        value: "product_mmname",
      },
      {
        text: "product_d_name",
        value: "product_d_name",
      },
      {
        text: "product_qty",
        value: "product_qty",
      },
      {
        text: "round",
        value: "round",
      },
    ],
    dialog: null,
    overlay: false,
    dialog2: false,
    jin_huo_bian_summit: false,
    password: "",
    // Add password data property
    isPasswordCorrect: false,
    // Add boolean to track password state
  }),

  methods: {
    getColor_3(ans) {
      if (ans != "sucess") return "red";
      else if (ans == "sucess") return "green";
    },

    // Method to check password
    checkPassword() {
      const correctPassword = "33439"; // Replace with your actual password
      this.isPasswordCorrect = this.password === correctPassword;
      if (!this.isPasswordCorrect) {
        alert("Incorrect password!");
      }
    },

    async summit_click() {
      this.overlay = true;
      this.dialog2 = false;

      await shwethe_order_in_fast_api
        .post("/api/v1/order_in_judy/order_in_judy_summit", {
          jin_huo_bian: this.jin_huo_bian_summit,
        })
        .then(function () {})
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // this.list_rerst();
        });

      await shwethe_order_in_fast_api
        .get(`/api/v1/order_in_judy/order_in_judy`, {})
        .then((response) => {
          (this.desserts = response.data), console.log(response.data);
        })
        .catch(() => {})
        .finally(() => {});
      this.overlay = false;
    },

    async summit_click_cancel() {
      this.overlay = true;
      this.dialog2 = false;

      await shwethe_order_in_fast_api
        .post("/api/v1/order_in_judy/order_in_judy_summit/wait_sucess", {
          jin_huo_bian: this.jin_huo_bian_summit,
        })
        .then(function () {})
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // this.list_rerst();
        });

      await shwethe_order_in_fast_api
        .get(`/api/v1/order_in_judy/order_in_judy`, {})
        .then((response) => {
          (this.desserts = response.data), console.log(response.data);
        })
        .catch(() => {})
        .finally(() => {});
      this.overlay = false;
    },
    searchItem____5(item) {
      (this.password = ""),
        (this.isPasswordCorrect = false),
        (this.jin_huo_bian_summit = null);
      this.dialog2 = true;
      console.log(item.jin_huo_bian);
      this.jin_huo_bian_summit = item.jin_huo_bian;
      // console.log(item.fen_dian_id);
      // console.log(item);
      // console.log(item["data_sub.jin_huo_bian"]);
    },
    click_bill_id(item) {
      console.log(item);
      localStorage.setItem("jin_huo_bian_judy", JSON.stringify(item.jin_huo_bian));
      localStorage.setItem("row_type_judy", "for_duty");
      window.location.href = "in_order_judy_form";
    },
    click_bill_id2(item) {
      console.log(item);
      localStorage.setItem("jin_huo_bian_judy", JSON.stringify(item.jin_huo_bian));
      localStorage.setItem("row_type_judy", "for_duty_cost");
      window.location.href = "in_order_judy_form";
    },
  },
  async created() {
    this.overlay = true;
    var _id_ = Date.now().toString();
    this.datetime = _id_;
    await shwethe_order_in_fast_api
      .get(`/api/v1/order_in_judy/order_in_judy`, {})
      .then((response) => {
        (this.desserts = response.data), console.log(response.data);
      })
      .catch(() => {
        this.overlay = false;
      })
      .finally(() => {
        this.overlay = false;
      });

    await shwethe_order_in_fast_api
      .get(`/api/v1/order_in_judy/order_in_judy_group`, {})
      .then((response) => {
        (this.desserts_3 = response.data), console.log(response.data);
      })
      .catch(() => {
        this.overlay = false;
      })
      .finally(() => {
        this.overlay = false;
      });
  },
};
</script>
