.app {
  margin: 2% 2%;
}

.app_1 {
  margin: 2.5% 5%;
}
.app_2 {
  margin: 0 5%;
}

// ----- vuetify font height ----- //
.v-list-item .v-list-item__title,
.v-list-item .v-list-item__subtitle {
  line-height: 1.8;
}
// select option
.v-list-item--dense .v-list-item__title,
.v-list-item--dense .v-list-item__subtitle,
.v-list--dense .v-list-item .v-list-item__title,
.v-list--dense .v-list-item .v-list-item__subtitle {
  line-height: 2rem;
}
// input label
.v-input .v-label {
  height: 25px;
  line-height: 24px;
}
// input value
.v-select__selections {
  line-height: 28px !important;
}
.v-text-field input {
  line-height: 30px;
}
// ------------------------------- //

@media only screen and (max-width: 1832px) {
  .app {
    margin: 5% 1%;
  }
}
