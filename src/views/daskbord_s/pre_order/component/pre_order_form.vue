<template>
  <div>
    <b-row>
      <b-col md="4" sm="12">
        <v-card>
          <v-card-title>Pre order report</v-card-title>
          <v-card-title> {{ data_customer_mmname }} </v-card-title>
          <v-form
            ref="form"
            v-model="valid"
            @submit.prevent="submitForm"
            autocomplete="off"
          >
            <v-container>
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    label="product idname"
                    v-model="answer.sname"
                    @keyup="_uppercase"
                    @keyup.enter="uppercase"
                    ref="product_idname"
                    clearable
                    autofocus
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    label="mm_name"
                    v-model="data_product[0].mm_name"
                    :rules="rules.lock"
                    filled
                    dense
                    disabled
                  >
                  </v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    label="dname"
                    v-model="data_product[0].d_name"
                    :rules="rules.lock"
                    filled
                    dense
                    disabled
                  >
                  </v-text-field>
                </v-col>
                <v-col>
                  <v-text-field
                    label="Price"
                    type="number"
                    v-model.number="data_product[0].price"
                    :rules="rules.price"
                    ref="Price"
                    @keyup.enter="goQty"
                  ></v-text-field>
                </v-col>
                <v-col>
                  <v-text-field
                    label="Qty"
                    type="number"
                    v-model.number="data_product[0].qty"
                    :rules="rules.qty"
                    ref="Qty"
                  ></v-text-field>
                </v-col>

                <div class="w-100"></div>
                <v-col>
                  <v-btn
                    :disabled="!valid"
                    color="success"
                    class="mr-4"
                    type="submit"
                  >
                    add to list
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card>
      </b-col>
      <b-col md="8" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Lists
            <v-spacer></v-spacer>
            <v-dialog v-model="dialog" max-width="290">
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  color="primary"
                  dark
                  v-bind="attrs"
                  v-on="on"
                  :disabled="!customer"
                >
                  customer select
                </v-btn>
              </template>

              <v-card>
                <v-card-title class="headline"> Customer </v-card-title>
                <v-container>
                  <v-form ref="form" v-model="valid_2" autocomplete="off">
                    <v-row>
                      <v-col cols="12">
                        <v-text-field
                          label="Customer"
                          v-model="answer_2.sname"
                          @keyup="_uppercase_2"
                          @keyup.enter="uppercase_2"
                          clearable
                          :rules="rules.lock"
                          autofocus
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          label="Customer_mm_name"
                          v-model="data_customer_mmname"
                          :rules="rules.lock"
                          filled
                          dense
                          disabled
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          label="Customer"
                          v-model="pass_l_n"
                          @keyup="_uppercase_6"
                          clearable
                          :rules="rules.lock"
                          autofocus
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          label="ke bian"
                          v-model="ke_bian"
                          @keyup.enter="search_kebian"
                          clearable
                          :rules="rules.lock"
                          :disabled="!kebian_valid"
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          label="ke bian"
                          v-model="ke_bian_mm_name"
                          :rules="rules.lock"
                          filled
                          dense
                          disabled
                        >
                        </v-text-field>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-container>
                <!-- <di -->
                <!-- <v-card-actions > -->
                <!-- <v-container> -->
                <!-- <v-row> -->
                <!-- <v-col cols="6"> -->
                <div class="_center">
                  <v-btn
                    class="_center_btn"
                    color="primary"
                    :disabled="!isFormValid_L"
                    @click="submitForm_L"
                  >
                    L
                  </v-btn>
                  <!-- </v-col> -->
                  <!-- <v-col cols="6"> -->
                  <!-- <v-spacer></v-spacer> -->
                  <v-btn
                    class="_center_btn"
                    color="primary"
                    :disabled="!isFormValid_N"
                    @click="submitForm_2"
                  >
                    N
                  </v-btn>
                </div>
                <!-- </v-col> -->
                <!-- </v-row> -->
                <!-- </v-container> -->
                <!-- </v-card-actions>  -->
                <!-- <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn color="green darken-1" text @click="dialog = false">
                    cancel
                  </v-btn>
                  <v-btn
                    color="green darken-1"
                    text
                    :disabled="!valid_2"
                    @click="submitForm_2"
                  >
                    save
                  </v-btn>
                </v-card-actions> -->
              </v-card>
            </v-dialog>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers"
            :items="table_list"
            :sort-by="['', '']"
            multi-sort
          >
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import { HTTP_2 } from "../../../../plugins/http";

export default {
  data: () => ({
    checK_idname: false,
    overlay: false,
    data_customer_valid: false,
    ke_bian: null,
    kebian_valid: false,
    ke_bian_mm_name: null,
    valid: true,
    valid_2: true,
    dialog: false,
    prduct_mm_name_L: null,
    prduct_d_name_L: null,
    // Get api
    data_customer_kebian: null,
    isFormValid_L: false,
    isFormValid_N: false,
    pass_l_n: null,

    items_partner: [],
    items_car: [],
    items_oil_type: [],
    headers: [
      // { text: "id", value: "id" },

      { text: "product_idname", value: "product_idname" },

      { text: "product_mm_name", value: "product_mm_name" },
      { text: "product_d_name", value: "product_d_name" },
      { text: "product_qty", value: "product_qty" },
      { text: "product_price", value: "product_price" },
    ],
    // require
    rules: {
      lock: [(v) => !!v || "required"],
      price: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        // (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      qty: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      distant: [(v) => !!v || "required"],
    },
    _id: null,
    table_list: [],
    // answer
    data_product: [{ mm_name: null }],
    data_customer: null,
    data_customer_mmname: null,
    answer_2: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    answer: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    print_from: "",
    alert: false,
    customer: false,
  }),

  methods: {
    async api_process() {
      console.log(this.data_customer);
      console.log("test");
      await HTTP_2.put("shwethe_n/api/v2/pre_order_n_compls", {
        bill_id: this._id,
        yi_fang: this.data_customer[0].id,
        lei_a: 22,
        lei_b: 49,
        data_: {
          status: "sucess",
          lei_A: 49,
          lei_B: 41,
        },
      })
        .then(function () {
          // alert("SUCCESS!!");
          //  this.$refs.form.reset();
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // console.log(this.answer);
          // this.$refs.form.reset();
        });
    },
    async api_process_l() {
      console.log(this.data_customer);
      console.log("test");
      await HTTP_2.put("shwethe_n/api/v2/pre_order_n_compls", {
        bill_id: this._id,
        yi_fang: this.data_customer[0].id,
        lei_a: 22,
        lei_b: 49,
        ke_bian: this.data_customer_kebian[0].id,
        data_: {
          status: "sucess",
          lei_A: 49,
          lei_B: 24,
        },
      })
        .then(function () {
          // alert("SUCCESS!!");
          //  this.$refs.form.reset();
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // console.log(this.answer);
          // this.$refs.form.reset();
        });
    },
    submitForm_2() {
      this.alert = true;
      this.dialog = false;

      console.log("BBBBBB");
      console.log(this.data_customer[0].yi_fang);
      if (this.data_customer) {
        this.alert = false;
        this.overlay = true;
        this.api_process();
      } else {
        this.alert = true;
      }
    },
    submitForm_L() {
      this.alert = true;
      this.dialog = false;

      console.log("BBBBBB");
      console.log(this.data_customer[0].yi_fang);
      if (this.data_customer) {
        this.alert = false;
        this.overlay = true;
        this.api_process_l();
      } else {
        this.alert = true;
      }
    },
    async submitForm() {
      // var _id_ = Date.now().toString();
      this.checK_idname = false;
      const alasql = require("alasql");
      console.log(this.data_product[0]);
      var res4 = alasql(
        "SELECT id as product_id,qty as product_qty,price as product_price ,mm_name as product_mm_name,d_name as product_d_name ,idname as product_idname FROM ?",
        [this.data_product]
      );
      res4[0].product_bill_id= 0;
      console.log(res4[0]);
      this.$refs.form.validate();
      console.log("test");
      console.log("test");
      await HTTP_2.put("shwethe_n/api/v2/pre_order_n", {
        uid: 0,
        bill_id: this._id,
        yi_fang: 0,
        lei_b: 0,
        data_jsonb: [res4[0]],
      })
        .then(function () {
          // alert("SUCCESS!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.form.reset();
          this.$refs.product_idname.focus();
        });

      await HTTP_2.get("shwethe_n/api/v1/pre_order_n", {
        params: {
          ID: this._id,
        },
      }).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          console.log(this.table_list)
        )
      );

      if (
        typeof this.table_list !== "undefined" &&
        this.table_list.length > 0
      ) {
        // the array is defined and has at least one element
        console.log(1);
        this.customer = true;
      }
    },
    async uppercase() {
      this.checK_idname = true;
      this.answer.sname = this.answer.sname.toUpperCase();
      console.log(this.answer.sname);
      await HTTP_2.get("shwethe_n/api/v1/product_price", {
        params: {
          ID: this.answer.sname,
        },
      }).then((response) => (this.data_product = JSON.parse(response.data)));

      if (this.data_product == false) {
        this.data_product = [{ mm_name: null }];

        console.log("");
      } else {
        this.$refs.Price.focus();
        console.log(this.data_product);
      }
    },

    _uppercase() {
      console.log("1");
      if (this.editedItem.idname.length == 1) {
        this.checK_idname = false;
      } else {
        if (this.checK_idname != false) {
          this.$refs.form.reset();
          this.checK_idname = false;
        } else {
          console.log("1");
        }
      }
    },

    goQty() {
      this.$refs.Qty.focus();
    },

    async uppercase_2() {
      this.checK_idname = true;

      this.answer_2.sname = this.answer_2.sname.toUpperCase();
      await HTTP_2.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.answer_2.sname,
        },
      }).then(
        (response) => (this.data_customer = JSON.parse(response.data))
        // (console.log(this.data_customer[0].mm_name))
      );
      this.data_customer_mmname = null;
      if (this.data_customer == false) {
        console.log(1);
        this.data_customer_mmname = null;
      } else {
        console.log(2);
        this.data_customer_mmname = this.data_customer[0].mm_name;
      }
      this._uppercase_6();
    },
    async search_kebian() {
      // this.checK_idname = true;

      this.ke_bian = this.ke_bian.toUpperCase();
      await HTTP_2.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.ke_bian,
        },
      }).then(
        (response) => (this.data_customer_kebian = JSON.parse(response.data)),
        (this.isFormValid_L = true)
        // (console.log(this.data_customer[0].mm_name))
      );
      this.ke_bian_mm_name = null;
      if (this.data_customer_kebian == false) {
        console.log(1);
        this.ke_bian_mm_name = null;
      } else {
        console.log(2);
        this.ke_bian_mm_name = this.data_customer_kebian[0].mm_name;
      }
      // this._uppercase_6();
    },
    _uppercase_2() {
      console.log("1");
      this.data_customer[0].id = null;
      if (this.editedItem.idname.length == 1) {
        this.checK_idname = false;
      } else {
        if (this.checK_idname != false) {
          this.$refs.form.reset();
          this.checK_idname = false;
        } else {
          console.log("1");
        }
      }
      this._uppercase_6();
    },
    _uppercase_6() {
      console.log(this.pass_l_n);
      console.log(this.data_customer[0].id);
      if ((this.pass_l_n == 1150) & (this.data_customer[0].id != null)) {
        console.log("AAAAAAAAAAAAAAAAAAAAA");

        this.kebian_valid = true;
        this.ke_bian = null;
        this.ke_bian_mm_name = null;
        // this.isFormValid_L = true;
      } else {
        this.kebian_valid = false;
        this.isFormValid_L = false;
      }
      if ((this.pass_l_n == 2296) & (this.data_customer[0].id != null)) {
        console.log("AAAAAAAAAAAAAAAAAAAAA");
        this.ke_bian = null;
        this.ke_bian_mm_name = null;
        this.isFormValid_N = true;
      } else {
        this.isFormValid_N = false;
      }
      // console.log("1");
      // if (this.checK_idname != false) {
      //   this.$refs.form.reset();
      //   this.checK_idname = false;
      // } else {
      //   console.log("1");
      // }
    },
    itemText(item) {
      return `${item.id} | ${item.idname} | ${item.mm_name}`;
    },
    sname_value(item) {
      console.log(this.item);
      return `${item.id}`;
    },
  },
  async created() {
    console.log(localStorage.getItem("uid"));
    var _id_ = Date.now().toString();
    this._id = _id_;
    console.log(this._id);
    await HTTP_2.post(`shwethe_n/api/v1/pre_order_n`, {
      bill_id: this._id,
      uid: localStorage.getItem("uid"),
    }).then((response) => {
      console.log(response);
    });
  },
};
</script>

<style scoped>
._center {
  text-align: center;
}

._center_btn {
  margin-block: 4%;
  margin-left: 6%;
  margin-right: 6%;
}
</style>
