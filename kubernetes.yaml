apiVersion: apps/v1
kind: Deployment
metadata:
  name: shwethe-web-quaser
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: shwethe-web-quaser
  template:
    metadata:
      labels:
        k8s-app: shwethe-web-quaser
    spec:
      volumes:
        - name: timezone
          hostPath:
            path: /usr/share/zoneinfo/Asia/Bangkok
            type: ''
      containers:
        - name: shwethe-web-quaser
          image: 'siess223/shwethe_web_quaser:latest'
          resources:
            requests:
              cpu: '1'
              memory: 1000Mi
            volumeMounts:
              - name: timezone
                mountPath: /etc/localtime
      imagePullSecrets:
        - name: regcred



---
apiVersion: v1
kind: Service
metadata:
  name: shwethe-web-quaser-service
spec:
  selector:
    k8s-app: shwethe-web-quaser
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080