<template>
  <v-card id="printMe">
    <v-card-title>
      Table
      <v-spacer></v-spacer>
      <v-btn icon color="green" @click="table_refrest">
        <v-icon>mdi-cached</v-icon>
      </v-btn>
      <v-spacer></v-spacer>
      <v-text-field
        v-model="search"
        append-icon="mdi-magnify"
        label="Search"
        single-line
        hide-details
      ></v-text-field>
    </v-card-title>

    <v-data-table
      :footer-props="{
        'items-per-page-options': [10, 20, 30, 40, 50],
      }"
      loading-text="Loading... Please wait"
      :loading="loading"
      :search="search"
      :headers="headers"
      :items="desserts"
      :sort-by="['fen_x', 'want_qty']"
      multi-sort
    >
    </v-data-table>
  </v-card>
</template>

<script>
import { HTTP } from "../../../../plugins/http";

export default {
  data() {
    return {
      loading: true,
      search: "",
      test: "",

      headers: [
        { text: "Edit", value: "actions", sortable: false },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "d_name", value: "d_name" },
        { text: "fen_x", value: "fen_x" },
        { text: "qty_x", value: "qty_x" },
        { text: "want_qty", value: "want_qty" },
        { text: "fen_y", value: "fen_y" },
        { text: "qty_y", value: "qty_y" },
        { text: "type", value: "type" },
      ],
      desserts: [],
    };
  },
  methods: {
    async table_refrest() {
      this.loading = true;
      await HTTP.post(`order_for_order_list`, {}).then((response) => {
        console.log(response.data[0].qty_fen),
          (this.desserts = response.data[0].A1001),
          (this.headers = response.data[0].columns);
      });
      this.loading = false;
    },
  },

  created: function() {
    this.table_refrest();
  },
};
</script>
