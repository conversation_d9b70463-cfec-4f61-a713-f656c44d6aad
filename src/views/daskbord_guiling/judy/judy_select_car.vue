<template>
  <div>
    <v-container>
      <b-row>
        <b-col md="12" sm="12">
          <v-card>
            <v-card-title>Judy</v-card-title>
            <v-form
              ref="form"
              v-model="valid"
              @submit.prevent="submitForm"
              autocomplete="off"
            >
              <v-container>
                <v-row>
                  <v-col cols="6">
                    <v-select
                      :items="items"
                      item-text="text"
                      item-value="value"
                      v-model.number="answer.fen"
                      label="Shop"
                      :rules="rules.lock"
                    ></v-select>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      label="car_bill_id"
                      :rules="rules.lock_2"
                      v-model.number="answer.car_bill_id"
                      type="number"
                    >
                    </v-text-field>
                  </v-col>

                  <v-col cols="6">
                    <v-text-field
                      ref="product_idname_ref"
                      label="product idname"
                      v-model="answer.sname"
                      @keyup="_uppercase"
                      @keyup.enter="uppercase"
                      clearable
                      autofocus
                    >
                    </v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      label="mm_name"
                      :rules="rules.lock"
                      v-model="data_customer_mmname"
                      disabled
                    >
                    </v-text-field>
                  </v-col>

                  <div class="w-100"></div>
                  <v-col>
                    <v-btn
                      :disabled="!valid"
                      color="success"
                      class="mr-4"
                      type="submit"
                    >
                      add to list
                    </v-btn>
                  </v-col>
                </v-row>
              </v-container>
            </v-form>
          </v-card>
        </b-col>

        <!-- <pre>{{ answer }}</pre> -->

        <b-col md="12" sm="12">
          <v-card id="printMe">
            <v-card-title>
              Judy
              <v-spacer></v-spacer>
            </v-card-title>
            <v-data-table
              :headers="headers"
              :items="desserts"
              :items-per-page="5"
              class="elevation-1"
            >
              <template v-slot:item.edit="{ item }">
                <v-btn small color="primary" @click="goNext(item)">View</v-btn>
              </template>
            </v-data-table>
          </v-card>
        </b-col>
      </b-row>
    </v-container>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import { HTTP_2, HTTP } from "../../../plugins/http";

export default {
  data: () => ({
    overlay: false,
    valid: true,
    checK_idname: false,

    headers: [
      { text: "auto_id", value: "auto_id" },
      { text: "mm_name", value: "mm_name" },
      { text: "car_bill_id", value: "car_bill_id" },
      { text: "datetime", value: "datetime" },
      { text: "fen", value: "fen" },
      { text: "views", value: "edit" },
    ],
    desserts: [],

    // require
    rules: {
      lock: [(v) => !!v || "required"],
      lock_2: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
    },

    auto_id: "",
    answer: {
      car_bill_id: 0,
      fen: 0,
      uid: 0,
      sname: "",
    },

    data_product: [{ mm_name: null }],
    data_customer: null,
    data_customer_mmname: null,

    items: [
      { text: "shwethe 1", value: 1 },
      { text: "shwethe 2", value: 2 },
    ],
  }),

  methods: {
    async submitForm() {
      this.overlay = true;
      await HTTP_2.post("shwethe_duty/api/v1/duty_out_car", this.answer)
        .then(() => {
          console.log("SUCCESS!!");
        })
        .catch(() => {
          alert("FAILURE!!");
        })
        .finally(() => {
          this.$refs.form.reset();
        });

      await HTTP_2.get("shwethe_duty/api/v1/duty_out_car", {
        params: {
          auto_id: this.auto_id,
        },
      }).then(
        (response) => (
          (this.desserts = JSON.parse(response.data)),
          console.log(this.desserts)
        )
      );
      this.overlay = false;
    },

    async goNext(item) {
      console.log(item);
      await HTTP_2.put("shwethe_duty/api/v1/duty_out_car", {
        car_bill_id: item.car_bill_id,
        fen: item.fen,
      })
        .then(function () {
          console.log(item);
          localStorage.setItem("judy_car_item", JSON.stringify(item));
          window.location.href = "judy";
          this.overlay = true;
        })
        .catch(function () {})
        .finally(() => {
          // location.reload();
        });
    },

    _uppercase() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },

    async uppercase() {
      this.checK_idname = true;
      this.answer.sname = this.answer.sname.toUpperCase();
      await HTTP.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.answer.sname,
        },
      }).then(
        (response) => (this.data_customer = JSON.parse(response.data))
        // (console.log(this.data_customer[0].mm_name))
      );
      this.data_customer_mmname = null;
      if (this.data_customer == false) {
        console.log(1);
        this.data_customer_mmname = null;
      } else {
        console.log(this.data_customer);
        this.data_customer_mmname = this.data_customer[0].mm_name;
        this.answer.ke_bian = this.data_customer[0].id;
      }
    },
  },
  async created() {
    await HTTP_2.get("shwethe_duty/api/v1/duty_out_car", {
      params: {
        auto_id: this.auto_id,
      },
    }).then(
      (response) => (
        (this.desserts = JSON.parse(response.data)), console.log(this.desserts)
      )
    );
  },
};
</script>
