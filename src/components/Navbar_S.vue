<template>
  <nav>
    <!-- :clipped="$vuetify.breakpoint.lgAndUp" -->
    <v-navigation-drawer v-model="drawer" app>
      <v-list dense>
        <template v-for="(item, i) in items">
          <v-row v-if="item.heading" :key="i" align="center"> </v-row>

          <v-divider
            v-else-if="item.divider"
            :key="i"
            dark
            class="my-4"
          ></v-divider>

          <!-- :prepend-icon="item.model ? item.icon : item['icon-alt']"
            append-icon="" -->
          <v-list-group
            v-else-if="item.children"
            :key="i"
            v-model="item.model"
            prepend-icon="view_list"
            no-action
          >
            <template v-slot:activator>
              <v-list-item-content>
                <v-list-item-title>
                  {{ item.text }}
                </v-list-item-title>
              </v-list-item-content>
            </template>
            <v-list-item
              v-for="(child, i) in item.children"
              :key="i"
              :to="child.route"
            >
              <v-list-item-action>
                <v-icon>{{ child.icon }}</v-icon>
              </v-list-item-action>
              <v-list-item-content>
                <v-list-item-title>
                  {{ child.text }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list-group>

          <v-list-item v-else :key="item.text" :to="item.route">
            <v-list-item-action>
              <v-icon>{{ item.icon }}</v-icon>
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>
                {{ item.text }}
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </template>

        <template v-for="item of items">
          <template v-if="item.items != null">
            <v-list-group :key="item.title" no-action v-model="item.active">
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title>
                    {{ item.title }}
                  </v-list-item-title>
                </v-list-item-content>
              </template>
              <div>
                <div v-for="child in item.items" :key="child.title">
                  <template v-if="child.subitems !== null">
                    <v-list-group
                      sub-group
                      no-action
                      :key="child.id"
                      prepend-icon="m"
                      v-model="child.subactive"
                    >
                      <template v-slot:activator>
                        <v-list-item-title v-text="child.title">
                          <i class="mr-2" :class="child.action"></i>
                        </v-list-item-title>
                      </template>
                      <template v-for="subchild in child.subitems">
                        <v-list-item :key="subchild.id" :to="subchild.route">
                          <v-list-item-title
                            v-text="subchild.text"
                          ></v-list-item-title>
                        </v-list-item>
                      </template>
                    </v-list-group>
                  </template>
                </div>
              </div>
            </v-list-group>
          </template>
        </template>
      </v-list>

      <v-divider></v-divider>
    </v-navigation-drawer>
    <!-- :clipped-left="$vuetify.breakpoint.lgAndUp" -->
    <v-app-bar app color="blue darken-3" dark>
      <v-app-bar-nav-icon @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
      <v-toolbar-title style="width: 300px" class="ml-0 pl-4">
        <span class="hidden-sm-and-down">Dashboard_S</span>
      </v-toolbar-title>
      <v-spacer></v-spacer>
      <v-list-item-action key="open-history">
        <v-tooltip left>
          <template v-slot:activator="{ on, attrs }">
            <v-btn icon small v-bind="attrs" v-on="on" @click="Logout">
              <v-icon>mdi-logout-variant</v-icon>
            </v-btn>
          </template>
          <span>Logout</span>
        </v-tooltip>
      </v-list-item-action>
    </v-app-bar>
  </nav>
</template>

<script>
export default {
  props: {
    source: String,
  },
  data: () => ({
    drawer: null,
    items: [
      { icon: "home", text: "Home", route: "home" },
      { divider: true },
      {
        text: "pre order",
        model: false,
        children: [
          
          { icon: "link", text: "order", route: "order" },
          { icon: "link", text: "view_pre_order", route: "view_pre_order" },
          { icon: "link", text: "view_no", route: "view_no" },
          { icon: "link", text: "order_list", route: "order_list" },
          { icon: "link", text: "add_car_count", route: "add_car_count" },
        ],
      },
      {
        text: "pre order",
        model: false,
        children: [
          
          { icon: "link", text: "pre order", route: "pre_order" },
          { icon: "link", text: "view_car_cost_form order", route: "view_car_cost_form" },
          { icon: "link", text: "view_car_cost_product", route: "view_car_cost_product" },
        ],
      },
      {
        text: "pre order",
        model: false,
        children: [
          { icon: "link", text: "order list n trace", route: "order_list_n_trace" },
        ],
      },
    ],
  }),
  methods: {
    Logout() {
      console.log("A");
      localStorage.removeItem("access_token");
      this.$router.go("/");
    },
  },
};
</script>
