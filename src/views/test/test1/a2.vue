<template>
  <v-app class="app">
    <v-card-title>
      Logistics2
      <v-spacer></v-spacer>
      <v-select
        label="greenRed"
        :items="['green', 'red']"
        v-model="greenRed"
        clearable
      ></v-select>
    </v-card-title>

    <v-data-table
      :headers="headers"
      :items="filteredItems"
      sort-by="calories"
      class="elevation-1"
    >
      <template v-slot:top>
        <v-dialog v-model="dialog" max-width="500px">
          <v-card>
            <v-card-text>
              <v-container>
                <h3>{{ editedItem.name }}</h3>
                <v-row>
                  <v-col cols="12" sm="6" md="4">
                    <v-select
                      :items="items"
                      v-model="editedItem.check"
                      label="check"
                      dense
                    ></v-select>
                  </v-col>
                </v-row>
              </v-container>
            </v-card-text>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="blue darken-1" text @click="close"> Cancel </v-btn>
              <v-btn color="blue darken-1" text @click="save"> Save </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </template>
      <template v-slot:item.actions="{ item }">
        <v-icon small class="mr-2" @click="editItem(item)"> mdi-pencil </v-icon>
      </template>
      <template v-slot:item.check="{ item }">
        <v-chip :color="getColor(item.check)" dark>
          {{ item.check }}
        </v-chip>
      </template>
    </v-data-table>

    <pre>
      {{ desserts }}
    </pre>
  </v-app>
</template>

<script>
export default {
  data: () => ({
    greenRed: null,
    dialog: false,
    dialogDelete: false,
    items: ["green", "red"],
    headers: [
      {
        text: "Dessert (100g serving)",
        align: "start",
        sortable: false,
        value: "name",
      },
      { text: "Calories", value: "calories" },
      { text: "Fat (g)", value: "fat" },
      { text: "Carbs (g)", value: "carbs" },
      { text: "Protein (g)", value: "protein" },
      { text: "Check", value: "check" },
      { text: "Actions", value: "actions", sortable: false },
    ],
    desserts: [],
    editedIndex: -1,
    editedItem: {
      name: "",
      check: null,
    },
  }),

  watch: {
    dialog(val) {
      val || this.close();
    },
  },

  computed: {
    filteredItems() {
      return this.desserts.filter((i) => {
        return !this.greenRed || i.check === this.greenRed;
      });
    },
  },

  created() {
    this.initialize();
  },

  methods: {
    initialize() {
      this.desserts = [
        {
          name: "Frozen Yogurt",
          calories: 159,
          fat: 6.0,
          carbs: 24,
          protein: 4.0,
          check: "green",
        },
        {
          name: "Ice cream sandwich",
          calories: 237,
          fat: 9.0,
          carbs: 37,
          protein: 4.3,
          check: "red",
        },
        {
          name: "Eclair",
          calories: 262,
          fat: 16.0,
          carbs: 23,
          protein: 6.0,
          check: "green",
        },
      ];
    },

    getColor(check) {
      if (check == "green") return "green";
      else if (check == "red") return "red";
    },

    editItem(item) {
      this.editedIndex = this.desserts.indexOf(item);
      this.editedItem = Object.assign({}, item);
      this.dialog = true;
      console.log(item);
    },

    close() {
      this.dialog = false;
    },

    save() {
      if (this.editedIndex > -1) {
        Object.assign(this.desserts[this.editedIndex], this.editedItem);
      } else {
        this.desserts.push(this.editedItem);
      }
      this.close();
    },
  },
};
</script>
