<template>
  <v-app>
    <!-- <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn> -->
    <v-card id="printMe">
      <v-card-title>
        Oil
        <v-spacer></v-spacer>
        <!-- <a :href="$router.resolve({ name: 'oil' }).href">
          <v-icon>refresh</v-icon></a
        > -->
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>
      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50],
        }"
        :search="search"
        :headers="headers"
        :items="desserts"
        :sort-by="['', '']"
        multi-sort
      >
        <template v-slot:item.actions="{ item }">
          <div class="my-2">
            <v-icon small @click="deleteItem(item)">
              delete
            </v-icon>
          </div>
        </template>
      </v-data-table>
    </v-card>

    <template>
      <v-snackbar :color="snackbar.color" v-model="snackbar.show">
        {{ snackbar.message }}
      </v-snackbar>
    </template>
  </v-app>
</template>

<script>
import { HTTP } from "../../../../plugins/http";

export default {
  data() {
    return {
      output: null,
      search: "",

      snackbar: {
        show: false,
        message: null,
        color: null,
      },

      headers: [
        // { text: "id", value: "id" },

        { text: "datetime", value: "datetime" },
        { text: "bill_id", value: "bill_id" },
        { text: "idname_x", value: "idname_x" },
        { text: "mm_name_x", value: "mm_name_x" },
        { text: "d_name", value: "d_name" },
        { text: "qty", value: "qty" },
        { text: "price", value: "price" },
        { text: "ANS", value: "ANS" },
        { text: "idname_y", value: "idname_y" },
        { text: "mm_name_y", value: "mm_name_y" },
        { text: "Edit", value: "actions", sortable: false },
      ],
      desserts: [],

      //edit
      delete: {},
    };
  },
  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },

    // editItem and delete
    deleteItem(item) {
      this.delete = Object.assign({}, item);

      console.log(this.delete);
      console.log(this.delete.bill_id);
      // confirm("Are you sure you want to delete this item?") &&
      //   HTTP.post("oil_delete", { bill_id: this.delete.bill_id }).then(
      //     (response) =>
      //       console.log(response.data)
      //       // (this.desserts = response.data)
      //       //  this.columns = response.data.columns
      //   );
      confirm("Are you sure you want to delete this item?") &&
        HTTP.post("oil_delete", { bill_id: this.delete.bill_id }).then(
          (response) => {
            this.snackbar = {
              message: this.delete.mm_name_x,
              color: "warning",
              show: true,
            };
            console.log(response.data);
          }
        );
    },
  },
  created: function() {
    HTTP.get("oil_list").then(
      (response) => (
        console.log(response.data),
        (this.desserts = response.data)
        //  this.columns = response.data.columns
      )
    );
  },
};
</script>
