import axios from "axios";


export const HTTP = axios.create({
  baseURL: `http://192.168.1.85/`,
  headers: {
    Authorization: "Bearer {token}",  
    // "Content-type": "application/json"
  },
});
export const HTTP_2 = axios.create({
  baseURL: `http://192.168.1.85/`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});
export const HTTP_TEST = axios.create({
  baseURL: `http://192.168.1.39:8080/`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});

export const HTTP_shwethe_n = axios.create({
  baseURL: `http://pv-api.shwethe.com/shwethe_n`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});

export const mongodb_api = axios.create({
  baseURL: `http://pv-api.shwethe.com/mongodb_data_api`,
  // baseURL: `http://192.168.1.11:8005/mongodb_data_api`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});

export const shwethe_order_in_fast_api = axios.create({
  baseURL: `http://pv-api.shwethe.com/shwethe_order_in_fast_api`,
  // baseURL: `http://192.168.1.11:8021/shwethe_order_in_fast_api`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});
export const shwethe_duty = axios.create({
  baseURL: `http://192.168.1.85/shwethe_duty`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});

export const shwethe_file_path = axios.create({
  baseURL: `http://pv-api.shwethe.com/shwethe_file_path`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});

export const shwethe_delivery = axios.create({
  baseURL: `http://pv-api.shwethe.com/shwethe_delivery`,
  headers: { "Access-Control-Allow-Origin": "*" },
  // headers: {
  //   Authorization: 'Bearer {token}',
  //   // "Content-type": "application/json"
  // }
});

export const PetrolProduct = axios.create({
  // baseURL: `http://192.168.1.130:8222/shwethe_petrol/api/v1/product/`,
  baseURL: `http://192.168.1.30:8222/shwethe_petrol/api/v1/product/`,
  headers: { "Access-Control-Allow-Origin": "*" },
});
export const PetrolTest = axios.create({
  // baseURL: `http://192.168.1.130:8222/shwethe_petrol/api/v1/test/`,
  baseURL: `http://192.168.1.30:8222/shwethe_petrol/api/v1/test/`,
  headers: { "Access-Control-Allow-Origin": "*" },
});
