<template>
  <v-app>
    <!-- <v-container> -->
    <div class="app">
      <b-row>
        <b-col md="12" sm="12">
          <Form />
        </b-col>
        <!-- <b-col md="12" sm="12">
          <Table />
        </b-col> -->
      </b-row>
    </div>
    <!-- </v-container> -->
  </v-app>
</template>

<script>
import Form from "./component/judy_form.vue";
// import Table from "./component/judy_table";
export default {
  components: {
    Form,
    // Table
  }
};
</script>
