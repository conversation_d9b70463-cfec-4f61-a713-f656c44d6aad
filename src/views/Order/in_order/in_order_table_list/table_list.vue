<template>
<div>
    <v-container>
        <b-row>

            <!-- <pre>{{ answer }}</pre> -->

            <b-col md="12" sm="12">
                <v-card id="printMe">
                    <v-card-title>အဝင်စာရင်း
                        <v-spacer></v-spacer>
                    </v-card-title>
                    <v-data-table :headers="headers" :items="desserts" :items-per-page="10" class="elevation-1">
                        <template v-slot:item.qty_ans="{ item }">
                            <v-chip :color="getColor_2(item.qty_ans)" dark>
                                {{ item.qty_ans }}
                            </v-chip>
                        </template>
                        <template v-slot:top>
                            <v-dialog v-model="dialog" hide-overlay max-width="80%">
                                <v-card>
                                    <v-card-title class="headline"></v-card-title>
                                    <v-card-text>
                                        <v-data-table :headers="headers_2" :items="desserts_2" :items-per-page="5" class="elevation-1">
                                        </v-data-table>
                                    </v-card-text>
                                </v-card>
                            </v-dialog>
                        </template>
                        <template v-slot:item.search="{ item }">
                            <div v-if="item.product_id_y_ > 0.5">
                                <v-btn small color="success" @click="searchItem(item)">Search</v-btn>
                            </div>
                            <div v-if="item.product_id_y_ <= 0.5">
                                <v-btn small color="success" @click="searchItem(item)" disabled>Search</v-btn>
                            </div>
                        </template>
                        <template v-slot:item.edit="{ item }">
                            <div v-if="item.qty_ans == 0">
                                <v-btn small color="primary" @click="click_bill_id(item)">View</v-btn>
                            </div>
                            <div v-if="item.qty_ans != 0">
                                <v-btn small color="primary" @click="click_bill_id(item)" disabled>View222</v-btn>
                            </div>
                        </template>
                        <template v-slot:item.status="{ item }">
                            <v-chip :color="getColor_3(item.status)" dark>
                                {{ item.status }}
                            </v-chip>
                        </template>
                         
                    </v-data-table>
                </v-card>
            </b-col>
        </b-row>
        <b-row>
            <b-col md="12" sm="12">
                <v-card id="printMe">
                    <v-card-title>
                        စာရင်းချုပ်
                        <v-spacer></v-spacer>
                    </v-card-title>
                    <v-data-table :headers="headers_3" :items="desserts_3" :items-per-page="10" class="elevation-1">

                        <template v-slot:item.price_ans_2="{ item }">
                            <v-btn small color="primary" @click="click_bill_id_model(item)">View1</v-btn>
                        </template>
                        <template v-slot:item.price_ans_3="{ item }">
                            <v-btn small color="primary" @click="click_bill_id_model_2(item)">View2</v-btn>
                        </template>
                        <template v-slot:item.ANS="{ item }">
                            <v-chip :color="getColor_2(item.ANS)" dark>
                                {{ item.ANS }}
                            </v-chip>
                        </template>
                        <template v-slot:top>
                            <v-dialog v-model="dialog_2" hide-overlay max-width="80%">
                                <div v-if="awesome == 1">
                                    <v-card>
                                        <v-form ref="form" v-model="valid" @submit.prevent="submitForm_bill_id" autocomplete="off">
                                            <v-container>
                                                <v-row>
                                                    <v-col cols="8">
                                                        <v-text-field label="qty" v-model="bill_id_price" @keydown.enter="focusNext" ref="input-1" required :rules="rules.qty"></v-text-field>
                                                    </v-col>
                                                    <v-col cols="4">
                                                        <v-btn :disabled="!valid" color="success" class="mr-4" type="submit">
                                                            add to list
                                                        </v-btn>
                                                    </v-col>
                                                </v-row>
                                            </v-container>
                                        </v-form>
                                    </v-card>
                                </div>
                                <div v-else-if="awesome == 2">
                                    <v-card>
                                        <v-data-table :headers="headers_4" :items="desserts_4" :items-per-page="10" class="elevation-1">
                                        </v-data-table>
                                    </v-card>
                                </div>
                            </v-dialog>
                        </template>
                    </v-data-table>
                </v-card>
            </b-col>
        </b-row>
        <b-row>
            <b-col md="6" sm="12">
                <v-btn elevation="2" :disabled='Summit_disbled' @click='Summit_form_data'> Summit </v-btn>
            </b-col>
            <b-col md="6" sm="12">
                <v-btn elevation="2" color="error" :disabled='Summit_disbled' @click='Summit_form_data_cancel'> cancel </v-btn>
            </b-col>
        </b-row>
    </v-container>

    <!-- OVERLAY -->
    <template>
        <div class="text-center">
            <v-overlay :value="overlay">
                <v-progress-circular indeterminate size="64"></v-progress-circular>
            </v-overlay>
        </div>
    </template>
</div>
</template>

<script>
import {
    HTTP,
    // mongodb_api,
    shwethe_order_in_fast_api
} from "../../../../plugins/http";
export default {
    data: () => ({
        order_s_insert_bill_id: "",
        search_car_round: 0,
        comment: "",
        jin_huo_bian: 0,
        overlay: false,
        valid: false,
        datetime: null,
        checK_idname: false,
        smm_name: "",
        smm_id: 0,
        car_id: 0,
        car_name: "",
        parner_list_json: [],
        car_list_json: [],
        selectedCar: [],
        fen_dian_list_json: [],
        fen_dian_id: 0,
        dialog: false,
        dialog_2: false,
        form_bill_id: "",
        headers: [{
                text: "jin_huo_bian",
                value: "jin_huo_bian"
            },
            {
                text: "fen_dian_id",
                value: "fen_dian_id"
            },
            {
                text: "che_liang_mmname",
                value: "che_liang_mmname"
            },
            {
                text: "product_id_x",
                value: "product_id_x"
            },
            {
                text: "product_id_y",
                value: "product_id_y"
            },
            {
                text: "view",
                value: "edit"
            },
            {
                text: "Search",
                value: "search"
            },
            {
                text: "qty_ans",
                value: "qty_ans"
            } ,
            {
                text: "status",
                value: "status"
            },
        ],
        headers_2: [{
                text: "product_id_idname",
                value: "product_id_idname"
            },
            {
                text: "product_id_mmname",
                value: "product_id_mmname"
            },
            {
                text: "product_qty_y",
                value: "product_qty_y"
            },
            {
                text: "product_d_id_idname",
                value: "product_d_id_idname"
            },
            {
                text: "product_d_id_mmname",
                value: "product_d_id_mmname"
            },
            {
                text: "product_qty_x",
                value: "product_qty_x"
            },

        ],
        headers_3: [{
                text: "s_bill_id",
                value: "s_bill_id"
            },
            {
                text: "jia_yi_idname",
                value: "jia_yi_idname"
            },
            {
                text: "jia_yi_mmname",
                value: "jia_yi_mmname"
            },
            {
                text: "price_ans",
                value: "price_ans"
            },
            {
                text: "order_s_bill_id_total",
                value: "order_s_bill_id_total"
            },
             {
                text: "bi_zhi_idname",
                value: "bi_zhi_idname"
            },
            
            {
                text: "ANS",
                value: "ANS"
            },
            {
                text: "price_ans_2",
                value: "price_ans_2"
            },
            {
                text: "price_ans_3",
                value: "price_ans_3"
            },
        ],
        Summit_disbled: true,
        headers_4: [{
                text: "product_idname",
                value: "product_idname"
            },
            {
                text: "product_mm_name",
                value: "product_mm_name"
            },
            {
                text: "product_d_name",
                value: "product_d_name"
            },
            {
                text: "product_qty",
                value: "product_qty"
            },
            {
                text: "product_price",
                value: "product_price"
            },
            {
                text: "ANS",
                value: "ANS"
            },
            
        ],
        desserts: [],
        desserts_2: [],
        desserts_3: [],
        desserts_4: [],
        // require
        rules: {
            lock: [v => !!v || "required"],
            lock_2: [
                v => !!v || "required",
                v => v >= 0.1 || "required",
                v => !!v == Number.isInteger(v) || "Must be integer"
            ]
        },
        awesome: null,
        auto_id: "",
        answer: {
            car_bill_id: 0,
            fen: 0,
            uid: 0,
            sname: ""
        },

        data_product: [{
            mm_name: null
        }],
        data_customer: null,
        data_customer_mmname: null,
        bill_id_price: null,
        items: [{
                text: "shwethe 1",
                value: 1
            },
            {
                text: "shwethe 2",
                value: 2
            },
            {
                text: "shwethe 3",
                value: 3
            }
        ]
    }),

    methods: {
        async searchItem(item) {
            this.dialog = true;

            this.order_s_insert_bill_id = item.order_s_insert_bill_id;
            this.fen_dian_id = item.fen_dian_id;
            this.jin_huo_bian = item["data_sub.jin_huo_bian"];
            this.search_car_round = item.car_round;

            await shwethe_order_in_fast_api
                .get(`/api/v1/order_s_list/order_s_compline_product`, {
                    params: {
                        jin_huo_bian: item.jin_huo_bian,
                        fen_dian_id: this.fen_dian_id
                    }
                })
                .then(response => {
                    (this.desserts_2 = response.data),
                    console.log(response.data);
                })
                .catch(() => {
                    this.overlay = false;
                    alert("Creat FAILURE!!");
                })
                .finally(() => {});
            this.overlay = false;
        },
        getColor_2(ans) {
            if (ans > 0) return "red";
            else if (ans == 0) return "green";
        },
        getColor_3(ans) {
            if (ans != "sucess") return "red";
            else if (ans == "sucess") return "green";
        },
        async searchOrder() {
            await shwethe_order_in_fast_api
                .put(`/api/v1/order_s_insert/in_order/car_round`, {
                    order_s_insert_bill_id: this.order_s_insert_bill_id,
                    fen_dian_id: parseInt(this.fen_dian_id),
                    jin_huo_bian: parseInt(this.jin_huo_bian),
                    car_round: parseInt(this.search_car_round)
                })
                .then(response => {
                    //this.desserts = JSON.parse(response.data);
                    console.log(this.order_s_insert_bill_id);
                    console.log(this.fen_dian_id);
                    console.log(this.jin_huo_bian);
                    console.log(parseInt(this.search_car_round));
                    console.log(response.data);
                    alert("success");
                })
                .catch(e => {
                    //   console.log(this.order_s_insert_bill_id);
                    //   console.log(this.fen_dian_id);
                    //   console.log(this.jin_huo_bian);
                    //   console.log(parseInt(this.search_car_round));
                    //  // console.log(response.data);
                    //   this.overlay = false;
                    alert("FAILURE!!");
                    console.log(e);
                })
                .finally(() => {
                    this.dialog = false;
                    this.list_rerst();
                    this.$refs.form.reset();
                });
        },
        async changeCar() {
            this.smm_name = this.selectedCar.parner_mm_name;
            this.fen_dian_id = this.selectedCar.fen_dian_id;
            this.smm_id = this.selectedCar.parner_id;
            this.car_id = this.selectedCar.car_id;
            this.jin_huo_bian = this.selectedCar.jin_huo_bian;
            this.comment = this.selectedCar.comment;
            console.log(this.selectedCar);
            console.log(this.smm_name);
        },
        async search_parner_list(val) {
            console.log(val);
            await shwethe_order_in_fast_api
                .get("/api/v1/search/product_search", {
                    params: {
                        ID: val
                    }
                })
                .then(response => {
                    console.log(JSON.parse(response.data));
                    if (JSON.parse(response.data) != false) {
                        this.model = JSON.parse(response.data);
                        this.smm_id = this.model[0].jia_yi_id;
                        this.smm_name = this.model[0].jia_yi_mm_name;
                    } else {
                        this.smm_name = "";

                        console.log("");
                    }
                });
        },
        async search_car_list(val) {
            console.log(val);
            await shwethe_order_in_fast_api
                .get("/api/v1/search/jia_yi_search", {
                    params: {
                        ID: val
                    }
                })
                .then(response => {
                    console.log(JSON.parse(response.data));
                    if (JSON.parse(response.data) != false) {
                        this.model = JSON.parse(response.data);
                        this.car_id = this.model[0].jia_yi_id;
                        this.car_name = this.model[0].car_mm_name;
                    } else {
                        this.car_name = "";

                        console.log("");
                    }
                });
        },
        async submitForm_bill_id() {
            this.overlay = true;
            await shwethe_order_in_fast_api
                .post(`/api/v1/order_s_list/order_s_compline_bill_id`, {
                    order_in_s_bill_id: this.form_bill_id,
                    data_jsonb: {
                        order_s_bill_id_total: this.bill_id_price
                    }
                })
                .then(response => {
                    console.log(response);
                    console.log(this.fen_dian_id);
                })
                .catch(() => {

                })
                .finally(() => {
                    this.dialog_2 = false;
                    this.overlay = false;
                });

            await shwethe_order_in_fast_api
                .get(`/api/v1/order_s_list/order_s_compline_bill_id`, {
                    params: {
                        jin_huo_bian: this.jin_huo_bian,
                        fen_dian_id: this.fen_dian_id
                    }
                })
                .then(response => {
                    this.desserts_3 = response.data
                    console.log(response.data);
                })
                .catch(() => {
                    this.overlay = false;
                    alert("Creat FAILURE!!");
                })
                .finally(() => {});
            const alasql = require('alasql');

            var res5 = await alasql(
                "SELECT sum(ANS) as Total  FROM ?  ",
                [this.desserts_3]
            );
            console.log(res5[0].Total)
            if (res5[0].Total == 0) {
                this.Summit_disbled = false
            } else {
                this.Summit_disbled = true
                console.log("AAAAAAA")
            }

        },
        async submitForm() {
            this.overlay = true;
            // await HTTP_2.post(
            //   "shwethe_order_in_fast_api/api/v1/order_s_insert/order_s_insert",
            //   this.answer
            // )
            //   .then(() => {
            //     console.log("SUCCESS!!");
            //   })
            //   .catch(() => {
            //     alert("FAILURE!!");
            //   })
            //   .finally(() => {
            //     this.$refs.form.reset();
            //   });

            await shwethe_order_in_fast_api
                .post(`/api/v1/order_s_insert/in_order/order_s_insert`, {
                    parner_id: this.smm_id,
                    car_id: this.car_id,
                    fen_dian_id: this.fen_dian_id,
                    jin_huo_bian: this.jin_huo_bian,
                    comment: this.comment
                })
                .then(response => {
                    console.log(response);
                    console.log(this.fen_dian_id);
                })
                .catch(() => {
                    this.overlay = false;
                    alert("FAILURE!!");
                })
                .finally(() => {
                    this.$refs.form.reset();
                });

            await shwethe_order_in_fast_api
                .get(`/api/v1/order_s_insert/in_order/order_s_list`, {
                    //s_mm_id: this.smm_id,
                    //car_id: this.car_id,
                })
                .then(response => {
                    this.desserts = JSON.parse(response.data);
                    console.log(response);
                })
                .catch(() => {
                    this.overlay = false;
                    alert("FAILURE!!");
                })
                .finally(() => {
                    this.$refs.form.reset();
                });
            // await HTTP_2.get("shwethe_duty/api/v1/duty_out_car", {
            //   params: {
            //     auto_id: this.auto_id,
            //   },
            // }).then(
            //   (response) => (
            //     (his.desserts = JSON.parse(response.datta)),
            //     console.log(this.desserts)
            //   )goNext
            // );

            this.overlay = false;
            this.$refs.form.reset();
        },
        async Summit_form_data() {
            this.overlay = true;
            console.log("a")
            await shwethe_order_in_fast_api
                .put(`/api/v1/order_s_list/order_s_compline_summit`, {
                    jin_huo_bian : this.jin_huo_bian,
                    status : 'sucess'
                })
                .then(response => {
                    console.log(response)
                    this.overlay = false;
                    // this.created()
                })
                .catch(() => {
                });

        await this.sync_data();
            
            
        },
        async Summit_form_data_cancel() {
            this.overlay = true;
            console.log("a")
            await shwethe_order_in_fast_api
                .put(`/api/v1/order_s_list/order_s_compline_summit`, {
                    jin_huo_bian : this.jin_huo_bian,
                    status : 'wait_sucess'
                })
                .then(response => {
                    console.log(response)
                    this.overlay = false;
                    
                    // this.created()
                })
                .catch(() => {
                });
                await this.sync_data();
        },
        async sync_data(){
        this.overlay = true;
        var _id_ = Date.now().toString();
        this.datetime = _id_;
        await shwethe_order_in_fast_api
            .get(`/api/v2/order_s_list/order_s_compline`, {
                //s_mm_id: this.smm_id,
                //car_id: this.car_id,
            })
            .then(response => {
                (this.desserts = JSON.parse(response.data)),
                console.log(JSON.parse(response.data));
            })
            .catch(() => {
                this.overlay = false;
                alert("Creat FAILURE!!");
            })
            .finally(() => {});
            this.overlay = false;
        },
        async list_rerst() {
            console.log("list_rerst");
            await shwethe_order_in_fast_api
                .get("/api/v1/order_s_insert/in_order/order_s_list", {
                    params: {
                        list_id: this.order_s_insert_bill_id,
                    },
                })
                .then((response) => {

                    console.log(JSON.parse(response.data));
                    this.desserts = JSON.parse(response.data);

                });
        },
        async goNext(item) {
            this.overlay = true;
            console.log(item);
            localStorage.setItem(
                "order_s_insert_bill_id",
                JSON.stringify(item.order_s_insert_bill_id)
            );
            //this.overlay = false;
            window.open("in_Form", '_blank');
            this.overlay = false;
            // window.location.href = "in_Form";
        },
        async click_bill_id_model(item) {

            this.dialog_2 = true;
            this.awesome = 1;
            console.log(item)
            this.form_bill_id = item.s_bill_id

        },
        async click_bill_id_model_2(item) {
            this.overlay = true;
            this.dialog_2 = true;
            this.awesome = 2;
            console.log(item)
            this.form_bill_id = item.s_bill_id

            await shwethe_order_in_fast_api
                .get(`/api/v1/order_s_list/order_s_compline_bill_id_by_id`, {
                    params: {
                        s_bill_id: item.s_bill_id,
                        order_s_insert_bill_id: item.order_s_insert_bill_id
                    }
                })
                .then(response => {
                    this.desserts_4 = response.data
                    console.log(response.data);
                })
                .catch(() => {
                    this.overlay = false;
                    alert("Creat FAILURE!!");
                })
                .finally(() => {});
            this.overlay = false;

        },
        async click_bill_id(item) {
            const alasql = require('alasql');

            this.overlay = true;
            console.log(item)
            this.jin_huo_bian = item.jin_huo_bian
            this.fen_dian_id = item.fen_dian_id
            await shwethe_order_in_fast_api
                .get(`/api/v1/order_s_list/order_s_compline_bill_id`, {
                    params: {
                        jin_huo_bian: item.jin_huo_bian,
                        fen_dian_id: item.fen_dian_id
                    }
                })
                .then(response => {
                    this.desserts_3 = response.data
                    console.log(response.data);
                })
                .catch(() => {
                    this.overlay = false;
                    alert("Creat FAILURE!!");
                })
                .finally(() => {});

            var res5 = alasql(
                "SELECT sum(ANS) as Total  FROM ?  ",
                [this.desserts_3]
            );
            console.log(res5[0].Total)
            if (res5[0].Total == 0) {
                this.Summit_disbled = false
            } else {
                console.log("AAAAAAA")
            }
            this.overlay = false;

            // var res5 = alasql(
            //                     "SELECT *  FROM ? where printer_type = 'dot_n' ",
            //                     [this.priter_detail]
            //                 );

            //             // this.overlay = false;
            //             // window.location.href = "in_Form";
        },

        _uppercase() {
            console.log("1");
            if (this.checK_idname != false) {
                this.$refs.form.reset();
                this.checK_idname = false;
            } else {
                console.log("1");
            }
        },

        async uppercase() {
            this.checK_idname = true;
            this.answer.sname = this.answer.sname.toUpperCase();
            await HTTP.get("shwethe_n/api/v1/jia_yi_name", {
                params: {
                    ID: this.answer.sname
                }
            }).then(
                response => (this.data_customer = JSON.parse(response.data))
                // (console.log(this.data_customer[0].mm_name))
            );
            this.data_customer_mmname = null;
            if (this.data_customer == false) {
                console.log(1);
                this.data_customer_mmname = null;
            } else {
                console.log(this.data_customer);
                this.data_customer_mmname = this.data_customer[0].mm_name;
                this.answer.ke_bian = this.data_customer[0].id;
            }
        },
        parner_itemText(item) {
            return `${item.parner_idname} | ${item.parner_mmname}`;
        },
        parner_value(item) {
            return `${item.parner_id}`;
        },
        car_itemText(item) {
            return `${item.car_idname} | ${item.car_mm_name} | ${item.jin_huo_bian}`;
        },
        car_value(item) {
            return `${item.jin_huo_bian}`;
        },
        fen_dian_itemText(item) {
            return `${item.fen_dian_id} | ${item.fen_dian_name}`;
        },
        fen_dian_value(item) {
            //this.fen_dian_id = item.fen_dian_id;
            return `${item.fen_dian_id}`;
        }
    },
    async created() {
        this.overlay = true;
        var _id_ = Date.now().toString();
        this.datetime = _id_;
        await shwethe_order_in_fast_api
            .get(`/api/v2/order_s_list/order_s_compline`, {
                //s_mm_id: this.smm_id,
                //car_id: this.car_id,
            })
            .then(response => {
                (this.desserts = JSON.parse(response.data)),
                console.log(JSON.parse(response.data));
            })
            .catch(() => {
                this.overlay = false;
                alert("Creat FAILURE!!");
            })
            .finally(() => {});
        this.overlay = false;
    }
};
</script>
