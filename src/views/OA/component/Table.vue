<template>
  <v-app class="app">
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <v-card id="printMe">
      <v-card-title>
        OA
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>
      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50]
        }"
        :search="search"
        :headers="headers"
        :items="desserts"
        multi-sort
      >
        <template v-slot:top>
          <v-dialog
            v-model="dialog"
            fullscreen
            hide-overlay
            transition="dialog-bottom-transition"
          >
            <v-card>
              <v-toolbar dark color="warning">
                <v-btn icon dark @click="dialog = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
                <v-toolbar-title>OA</v-toolbar-title>
                <v-spacer></v-spacer>
              </v-toolbar>

              <b-container class="fluid" fluid>
                <v-card>
                  <b-container>
                    <v-card-text>
                      <b-row>
                        <b-col align="center" sm>
                          <p>idname</p>
                          <p class="size">{{ editedItem.idname }}</p>
                        </b-col>
                        <v-divider class="mx-12" vertical></v-divider>
                        <b-col align="center" sm>
                          <p>mm_name</p>
                          <p class="size">{{ editedItem.mm_name }}</p>
                        </b-col>
                        <v-divider class="mx-12" vertical></v-divider>
                        <b-col align="center" sm>
                          <p>d_name</p>
                          <p class="size">{{ editedItem.d_name }}</p>
                        </b-col>
                      </b-row>
                    </v-card-text>
                  </b-container>
                </v-card>
              </b-container>

              <br />

              <v-container>
                <v-card>
                  <v-card-title>
                    📒
                  </v-card-title>
                  <v-container>
                    <v-form
                      ref="form"
                      v-model="valid"
                      @submit.prevent="submitForm"
                    >
                      <v-row>
                        <v-col cols="10">
                          <v-autocomplete
                            label="Select"
                            :item-text="itemText"
                            :item-value="value"
                            :items="product_ids"
                            v-model="answer.bid"
                            :rules="rules.lock"
                            filled
                            clearable
                          >
                          </v-autocomplete>
                        </v-col>
                        <v-col>
                          <v-text-field
                            v-model="answer.qty"
                            label="Qty"
                            :counter="5"
                            type="number"
                            filled
                            :rules="rules.qty"
                          ></v-text-field>
                        </v-col>

                        <div class="w-100"></div>

                        <v-col>
                          <v-btn
                            :disabled="!valid"
                            color="success"
                            class="mr-4"
                            type="submit"
                          >
                            Save
                          </v-btn>
                        </v-col>
                      </v-row>
                      <!-- <pre>{{ answer }}</pre> -->
                    </v-form>
                  </v-container>
                </v-card>
              </v-container>
            </v-card>
          </v-dialog>
        </template>

        <template v-slot:item.edit="{ item }">
          <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
        </template>
      </v-data-table>
    </v-card>
  </v-app>
</template>

<script>
import { HTTP } from "../../../plugins/http";
import "../../../style/style.scss";

export default {
  data: () => ({
    valid: false,
    dialog: false,

    product_ids: [
      // { nern: "a", test: "11", test222: "q" },
    ],

    // require
    rules: {
      lock: [v => !!v || "Field is required"],
      qty: [
        v => !!v || "required",
        v => (v && v.length <= 5) || "must be less than 5"
      ]
    },

    // answer
    answer: {
      bid: "",
      sid: "",
      qty: ""
    },

    // Table
    search: "",

    headers: [],

    desserts: [],

    // Match id
    editedItem: {
      idname: ""
    }
  }),
  methods: {
    print() {
      this.$htmlToPaper("printMe");
    },
    editItem(item) {
      this.dialog = true;
      this.editedItem = Object.assign({}, item);

      HTTP.post(`oa_list_for_insert`, {}).then(response => {
        response;
        this.answer.sid = this.editedItem.id;
      });
    },

    submitForm() {
      this.$refs.form.validate();

      HTTP.post("/oa_list_insert", this.answer)
        .then(function() {
          alert("SUCCESS!!");
        })
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          HTTP.get(`oa_list_for_insert`).then(response => {
            (this.headers = response.data[0].colume),
              (this.desserts = response.data[0].product_list_table),
              console.log(response.data[0].product_list_select),
              (this.product_ids = response.data[0].product_list_select);
          });
          this.$refs.form.reset();
          this.dialog = false;
        });
    },

    itemText(item) {
      return `${item.id} || ${item.idname} || ${item.mm_name} || ${item.d_name}`;
    },
    value(item) {
      return `${item.id}`;
    }
  },
  created() {
    HTTP.get(`order_item_list_columns_G`).then(response => {
      this.headers = response.data;
    });
    HTTP.get(`oa_list_for_insert`).then(response => {
      (this.headers = response.data[0].colume),
        (this.desserts = response.data[0].product_list_table),
        console.log(response.data[0].product_list_select),
        (this.product_ids = response.data[0].product_list_select);
    });
  }
};
</script>

<style lang="scss" scoped>
.fluid {
  padding: 0px;
}
.size {
  font-size: 18pt;
}
</style>
