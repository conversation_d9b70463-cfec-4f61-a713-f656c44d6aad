import Vue from "vue";
import VueRouter from "vue-router";
// asdklajsd

// ----- layout -s----
import DashboardLayout from "../layout/DashboardLayout";
import DashboardLayout_S from "../layout/DashboardLayout_S";
import DashboardLayout_gui_ling from "../layout/DashboardLayout_gui_ling";
import DefaltLayout from "../layout/DefaltLayout";
// -------------------------------------------------------------------------
// ----- pages -----
import judy_select_car from "../views/daskbord_guiling/judy/judy_select_car";
import judy from "../views/daskbord_guiling/judy/judy";
import duty from "../views/daskbord_guiling/duty/duty";
import duty_id from "../views/daskbord_guiling/duty_id/duty_id";
import pre_order from "../views/daskbord_s/pre_order/pre_order";
import view_pre_order from "../views/daskbord_s/view_pre_order/view_pre_order";
import view_no from "../views/daskbord_s/view_no/view_no";
import order from "../views/daskbord_s/order/order";
import order_list from "../views/daskbord_s/order_list/order_list";
import add_car_count from "../views/daskbord_s/add_car_count/add_car_count";
import table_printer_ from "../views/daskbord_s/order_list/component_printer/table_printer";
import view_car_cost_form from "../views/daskbord_s/car_cost/component/view_car_cost_form";
import view_car_cost_product from "../views/daskbord_s/view_car_cost_product/view_car_cost_product";
import order_list_n_trace from "../views/daskbord_s/order_list_n_trace/order_list_n_trace";


// ----- pages -----
import Login from "../views/Login/App";
import Home from "../views/Home.vue";
import Check_Stock from "../views/Check_Stock/Index_Stock.vue";
import Oil from "../views/Oil/Index_oil";
import ProductDetails from "../views/ProductDetails/Index_Pro";
import OA from "../views/OA/Index_OA.vue";
import Form_table from "../views/Form_table/form_table";
// Logistic
import Logistic from "../views/Logistics/Logistic/Index_Logistics";
import Logistic2 from "../views/Logistics/Logistic2/Index_Logistics";
import Logistic3 from "../views/Logistics/Logistic3/Index_Logistics";
// Order
import Follow_Items from "../views/Order/Follow_Items/Index_Follow.vue";
import Order_items from "../views/Order/Order_items/Index_Order.vue";
import Order_items_G from "../views/Order/Order_items_G/Index_ItemsG.vue";
import Order_items_D from "../views/Order/Order_items_D/Index_ItemsD.vue";
import Compare from "../views/Order/Compare/Index_Compare.vue";
import in_order from "../views/Order/in_order/table_list.vue";
import in_order_list from  "../views/Order/in_order/in_order_table_list/table_list.vue";
import in_order_judy from  "../views/Order/in_order/in_order_judy/table.vue";
import in_order_judy_form from  "../views/Order/in_order/in_order_judy/table_form.vue";
import in_Form from "../views/Order/in_order/table.vue";
import NewFile from "../views/Order/NewFile/Index_NewFile";
import NewFile2 from "../views/Drop_2_level/NewFile2/index_newfile2";
// D_to_Gu
import D_to_Gu from "../views/D_to_Gu/D_to_Gu";
import New_product from "../views/D_to_Gu/New_product";
// Petrol
import Petrol from "../views/Petrol/Index_petrol";
// -------------------------------------------------------------------------
// ----- WebView -----
import Form from "../views/WebView/Form";
import Form2 from "../views/WebView/Form2.vue";
import SetStock from "../views/WebView/Set_stock";
import Success from "../views/WebView/success";
// -------------------------------------------------------------------------
// ----- Upload App -----
import Upload_app from "../views/Upload_app/uploadApp.vue";
// -------------------------------------------------------------------------
// ----- Test -----
import App from "../views/test/test1/App";
import Form_0 from "../views/test/test1/Form";
import A1 from "../views/test/test1/a1";
import a11 from "../views/test/test2/a1";
import A2 from "../views/test/test1/a2";
import Test001 from "../views/test/test001";
// -------------------------------------------------------------------------
// ----- Error -----
import page_404 from "../views/Error/404";
import fom_table_ochange from "@/views/o_change/fom_table_ochange";
// -------------------------------------------------------------------------
Vue.use(VueRouter);
console.log("AAAAAAa");
const access_token = localStorage.getItem("access_token");
console.log(access_token);

import jwt_decode from "jwt-decode";

const routes = [
  // ----- Login -----
  {
    path: "",
    component: DefaltLayout,
    beforeEnter: (to, from, next) => {
      access_token;

      // var T = false
      if (access_token) {
        var decodedHeader = jwt_decode(access_token);
        console.log(decodedHeader.identity.rank);
        console.log(decodedHeader.identity.register_Id);
        localStorage.setItem("uid", decodedHeader.identity.register_Id);
        if (decodedHeader.identity.rank == "sell") {
          next("/dashboard_S");
        } else if (decodedHeader.identity.rank == "gui_ling") {
          next("/dashboard_guiling");
        } else {
          next("/dashboard");
        }
      } else {
        next();
      }
    },
    children: [
      {
        path: "/",
        redirect: "/login",
      },
      {
        path: "login",
        component: Login,
      },
    ],
  },
  // Dashboard
  {
    path: "/dashboard",
    component: DashboardLayout,
    beforeEnter: (to, from, next) => {
      access_token;
      // var T = false
      if (access_token) {
        next();
      } else {
        next("/");
      }
    },
    children: [
      {
        path: "",
        redirect: "home",
      },
      {
        path: "home",
        component: Home,
      },
      {
        path: "oil",
        name: "oil",
        component: Oil,
      },
      {
        path: "o_change",
        name: "o change",
        component: fom_table_ochange,
      },
      {
        path: "productDetails",
        component: ProductDetails,
      },
      {
        path: "checkstock",
        component: Check_Stock,
      },
      {
        path: "form_table",
        component: Form_table,
      },
      // D_to_Gu
      {
        path: "d_to_gu",
        component: D_to_Gu,
      },
      {
        path: "newproduct",
        component: New_product,
      },
      // Logistic
      {
        path: "logistic",
        component: Logistic,
      },
      {
        path: "logistic2",
        component: Logistic2,
      },
      {
        path: "Logistic3",
        component: Logistic3,
      },
      // Order
      {
        path: "followitems",
        component: Follow_Items,
      },
      {
        path: "orderitems",
        component: Order_items,
      },
      {
        path: "Orderitemsg",
        component: Order_items_G,
      },
      {
        path: "Orderitemsd",
        component: Order_items_D,
      },
      {
        path: "compare",
        component: Compare,
      },
      {
        path: "oa",
        component: OA,
      },
      {
        path: "newfile",
        component: NewFile,
      },
      {
        path: "newfile2",
        component: NewFile2,
      },
      {
        path: "in_order",
        component: in_order,
      },
      {
        path: "in_order_list",
        component: in_order_list,
      },
      {
        path: "in_order_judy",
        component: in_order_judy,
      },
      {
        path: "in_order_judy_form",
        component: in_order_judy_form,
      },
      {
        path: "in_Form",
        component: in_Form,
      },
      // upload app
      {
        path: "Upload_app",
        component: Upload_app,
      },
      // Petrol
      {
        path: "Petrol",
        component: Petrol,
      },
    ],
  },
  // Dashboard
  {
    path: "/dashboard_S",
    component: DashboardLayout_S,
    beforeEnter: (to, from, next) => {
      access_token;
      // var T = false
      if (access_token) {
        next();
      } else {
        next("/");
      }
    },
    children: [
      {
        path: "",
        redirect: "home",
      },
      {
        path: "home",
        component: Home,
      },
      {
        path: "pre_order",
        name: "pre_order",
        component: pre_order,
      },
      {
        path: "view_pre_order",
        name: "view_pre_order",
        component: view_pre_order,
      },
      {
        path: "view_no",
        name: "view_no",
        component: view_no,
      },
      {
        path: "order",
        component: order,
      },
      {
        path: "order_list",
        component: order_list,
      },
      {
        path: "add_car_count",
        component: add_car_count,
      },
      {
        path: "printer",
        component: table_printer_,
      },
      {
        path: "view_car_cost_form",
        component: view_car_cost_form,
      },
      {
        path: "view_car_cost_product",
        component: view_car_cost_product,
      },
      {
        path: "order_list_n_trace",
        component: order_list_n_trace,
      }
    ],
  },

  {
    path: "/dashboard_guiling",
    component: DashboardLayout_gui_ling,
    beforeEnter: (to, from, next) => {
      access_token;
      // var T = false
      if (access_token) {
        next();
      } else {
        next("/");
      }
    },
    children: [
      {
        path: "",
        redirect: "home",
      },
      {
        path: "home",
        component: Home,
      },
      {
        path: "duty",
        name: "duty",
        component: duty,
      },
      {
        path: "duty_id",
        name: "duty_id",
        component: duty_id,
      },
      {
        path: "judy_select_car",
        name: "judy_select_car",
        component: judy_select_car,
      },
      {
        path: "judy",
        name: "judy",
        component: judy,
      },
    ],
  },
  // ----- WebView -----
  {
    path: "/page",
    component: DefaltLayout,
    children: [
      {
        path: "form",
        component: Form,
      },
      {
        path: "form2",
        component: Form2,
      },
      {
        path: "setstock",
        component: SetStock,
      },

      {
        path: "success",
        component: Success,
      },
    ],
  },

  // ----- Test -----
  {
    path: "/test",
    component: DefaltLayout,
    children: [
      {
        path: "app",
        component: App,
      },
      {
        path: "form",
        component: Form_0,
      },
      {
        path: "a1",
        component: A1,
      },
      {
        path: "a2",
        component: A2,
      },
      {
        path: "a2",
        component: a11,
      },
      {
        path: "test001",
        component: Test001,
      },
    ],
  },

  // ----- Error Pages -----
  {
    path: "",
    component: DefaltLayout,
    children: [
      {
        path: "*",
        component: page_404,
      },
    ],
  },

  // {
  //   path: "/about",
  //   name: "About",
  //   // route level code-splitting
  //   // this generates a separate chunk (about.[hash].js) for this route
  //   // which is lazy-loaded when the route is visited.
  //   component: () =>
  //     import(/* webpackChunkName: "about" */ "../views/About.vue")
  // }
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes,
});
// router.beforeEach((to, from, next) => {
//   if (to.matched.some(record => record.meta.requiresAuth)) {
//     // this route requires auth, check if logged in
//     // if not, redirect to login page.
//     // if (!auth.loggedIn()) {
//       next({
//         path: '/login',
//         query: { redirect: to.fullPath }
//       })
//     // } else {
//       // next()
//     // }
//   } else {
//     next() // make sure to always call next()!
//   }

// })
// function guardMyroute(to, from, next) {
// var isAuthenticated = false;
//this is just an example. You will have to find a better or
// centralised way to handle you localstorage data handling
// if (localStorage.getItem('LoggedUser'))
//   isAuthenticated = true;
// else
//   isAuthenticated = false;
// if (isAuthenticated) {
//   next(); // allow to enter route
// }
// else {
//   next('/login'); // go to '/login';
// }
// }
export default router;
