<template>
  <ve-line class="chart" :data="chartData"></ve-line>
</template>

<script>
import { HTTP } from "../../plugins/http";
export default {
  data: function() {
    return {
      chartData: {
        columns: ['month_year','1.0','2.0'],

        rows:null
      }
    };
  },
  created: function() {
    HTTP.get("total_month_h").then(
      response => (
        console.log(response.data),
        console.log(response.data.records),
        console.log(response.data.split),
        // (this.chartData.columns = response.data.split),
        (this.chartData.rows = response.data)
        //  this.columns = response.data.columns
      )
    );
  }
};

</script>

<style lang="scss" scoped>
.chart {
  margin: 5%;
}
</style>
