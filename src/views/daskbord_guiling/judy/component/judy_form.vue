<template>
  <div>
    <b-row>
      <b-col md="4" sm="12">
        <!-- <pre>{{ answer_3 }}</pre> -->
        <v-card>
          <v-card-title>Order report</v-card-title>
          <v-card-title> {{ data_customer_mmname }} </v-card-title>
          <v-form
            ref="form"
            v-model="valid"
            @submit.prevent="submitForm"
            autocomplete="off"
          >
            <v-container>
              <v-btn @click="toggleComponentOne" depressed> Open </v-btn>
              <v-row v-if="showComponentOne">
                <v-col cols="12">
                  <v-text-field
                    ref="answer_4_idname"
                    label="idname"
                    v-model="answer_4.idname"
                    @keyup="_uppercase_3"
                    @keyup.enter="uppercase_3"
                    clearable
                    autofocus
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    label="name"
                    ref="data_customer_mmname_2"
                    v-model="data_customer_mmname_2"
                    filled
                    disabled
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-autocomplete
                    :items="items"
                    :item-text="itemText"
                    :item-value="value_select"
                    @change="onChange"
                    :rules="rules.lock"
                    label="registration"
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    ref="product_idname_ref"
                    label="product idname"
                    v-model="answer.sname"
                    @keyup="_uppercase"
                    @keyup.enter="uppercase"
                    clearable
                    autofocus
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    label="mm_name"
                    v-model="data_product[0].mm_name"
                    :rules="rules.lock"
                    filled
                    dense
                    disabled
                  >
                  </v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    label="dname"
                    v-model="data_product[0].d_name"
                    filled
                    dense
                    disabled
                  >
                  </v-text-field>
                </v-col>
                <v-col>
                  <v-text-field
                    ref="Price_ref"
                    @keyup.enter="enter_Price"
                    label="Price"
                    type="number"
                    v-model.number="data_product[0].price"
                    :rules="rules.price"
                    autofocus
                  ></v-text-field>
                </v-col>
                <v-col>
                  <v-text-field
                    @keyup.enter="enter_qty"
                    ref="Qty_ref"
                    label="Qty"
                    type="number"
                    v-model.number="data_product[0].qty"
                    :rules="rules.qty"
                  ></v-text-field>
                </v-col>

                <div class="w-100"></div>
                <v-col>
                  <v-btn
                    :disabled="!valid"
                    color="success"
                    class="mr-4"
                    type="submit"
                  >
                    add to list
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card>
      </b-col>

      <b-col md="8" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Lists
            <v-spacer></v-spacer>
            <v-dialog v-model="dialog" max-width="290">
              <template v-slot:activator="{ on, attrs }">
                <v-btn color="primary" dark v-bind="attrs" v-on="on">
                  customer select
                </v-btn>
              </template>

              <v-card>
                <v-card-title class="headline">
                  please information
                </v-card-title>
                <v-container>
                  <v-form ref="form" v-model="valid_2">
                    <v-row>
                      <v-col cols="12">
                        <v-text-field
                          label="Customer"
                          v-model="answer_2.sname"
                          @keyup="uppercase_2"
                          clearable
                          :rules="rules.lock"
                          autofocus
                        >
                        </v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          label="Customer_mm_name"
                          v-model="data_customer_mmname"
                          :rules="rules.lock"
                          filled
                          dense
                          disabled
                        >
                        </v-text-field>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-container>
                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn color="green darken-1" text @click="dialog = false">
                    Disagree
                  </v-btn>
                  <v-btn
                    color="green darken-1"
                    text
                    :disabled="!valid_2"
                    @click="submitForm_2"
                  >
                    Agree
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-dialog>
            <!-- <v-text-field
              append-icon="mdi-magnify"
              single-line
              hide-details
            ></v-text-field> -->
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers"
            :items="table_list"
            :sort-by="['', '']"
            multi-sort
          >
            <template v-slot:item.edit="{ item }">
              <v-icon small @click="del(item)"> mdi-delete </v-icon>
              <v-icon small class="mr-2" @click="editItem(item)">
                mdi-pencil
              </v-icon>
              <v-icon small @click="del(item)"> mdi-delete </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </b-col>

      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Judy
            <v-spacer></v-spacer>
            <!-- <a :href="$router.resolve({ name: 'oil' }).href">
          <v-icon>refresh</v-icon></a
        > -->
            <v-spacer></v-spacer>
            <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
            ></v-text-field>
          </v-card-title>
          <v-data-table
            :search="search"
            :headers="headers_2"
            :items="desserts_2"
          >
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import { HTTP, shwethe_duty } from "../../../../plugins/http";
const alasql = require("alasql");
export default {
  data: () => ({
    showComponentOne: false,
    checK_idname: false,
    checK_idname_3: false,
    search: "",
    overlay: false,
    data_customer_valid: false,
    valid: true,
    valid_2: true,
    dialog: false,
    prduct_mm_name_L: null,
    prduct_d_name_L: null,
    // Get api
    items_partner: [],
    items_car: [],
    items_oil_type: [],
    headers: [
      // { text: "id", value: "id" },
      { text: "duty_product_idname", value: "duty_product_idname" },
      { text: "duty_product_mm_name", value: "duty_product_mm_name" },
      { text: "duty_product_d_name", value: "duty_product_d_name" },
      { text: "duty_product_qty", value: "duty_product_qty" },
      { text: "duty_product_price", value: "duty_product_price" },
      { text: "edit", value: "edit" },
    ],
    headers_2: [
      { text: "d_name", value: "d_name" },
      { text: "idname_x", value: "idname_x" },
      { text: "mm_name", value: "mm_name" },
      { text: "price", value: "price" },
      { text: "qty", value: "qty" },
    ],
    desserts_2: [],
    // require
    rules: {
      lock: [(v) => !!v || "required"],
      price: [
        (v) => !!v || "required",
        (v) => v >= 0.0000000000001 || "required",
        // (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      qty: [
        (v) => !!v || "required",
        (v) => v >= 0.0000000000001 || "required",
        // (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      distant: [(v) => !!v || "required"],
    },
    _id: null,
    table_list: [],
    // answer
    data_product: [{ mm_name: null }],
    data_customer: null,
    data_customer_mmname: null,

    data_customer_2: null,
    data_customer_mmname_2: null,
    answer_2: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    answer: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    answer_3: {
      duty_product_register: 0,
      duty_product_selie: 0,
    },
    answer_4: {
      idname: "",
      name: "",
    },
    alert: false,

    items: ["Foo", "Bar", "Fizz", "Buzz"],
    selie: "",
    value_select: [],

    //edit
    delete: {},
  }),

  methods: {
    toggleComponentOne() {
      this.showComponentOne = !this.showComponentOne;
    },

    async api_process() {
      console.log(this.data_customer);
      console.log("test");
      await HTTP.put("shwethe_n/api/v1/order_n/order_n_compls", {
        bill_id: this._id,
        yi_fang: this.data_customer[0].id,
        data_: {
          status: "sucess",
        },
      })
        .then(function () {
          // alert("SUCCESS!!");
          //  this.$refs.form.reset();
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
          // console.log(this.answer);
          // this.$refs.form.reset();
        });
    },
    submitForm_2() {
      this.alert = true;
      this.dialog = false;
      console.log("BBBBBB");
      console.log(this.data_customer[0].yi_fang);
      if (this.data_customer) {
        this.alert = false;
        this.overlay = true;
        this.api_process();
      } else {
        this.alert = true;
      }
    },
    async submitForm() {
      const alasql = require("alasql");
      console.log(this.data_product[0]);
      var res4 = alasql(
        "SELECT id as duty_product_id,qty as duty_product_qty,price as duty_product_price ,mm_name as duty_product_mm_name,d_name as duty_product_d_name ,idname as duty_product_idname FROM ?",
        [this.data_product]
      );

      var res5 = alasql(
        "SELECT idname_y as product_id,qty as product_qty,price as product_price FROM ?",
        [this.desserts_2]
      );

      console.log(res4[0]);
      this.$refs.form.validate();
      console.log("test");
      console.log("test");
      this.$refs.form.reset();
      this.checK_idname = false;

      res4[0].duty_product_register = this.answer_3.duty_product_register;
      res4[0].duty_product_selie = this.answer_3.duty_product_selie;
      res4[0].duty_product_yi_fang = this.answer_4.id;
      res4[0].duty_product_yi_fang_mmname = this.answer_4.mm_name;
      console.log(this.desserts_2);
      console.log(res5);
      var vvv = JSON.parse(localStorage.getItem("judy_car_item"));
      await shwethe_duty.put("/api/v1/duty_out_car_by", {
        car_bill_id: vvv.car_bill_id,
        fen: vvv.fen,
        uid: 0,
        data_detail: res4[0],
        data_detail_b: res5,
      })
        .then(function () {
          // location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {});
      this.refre();
    },
    _uppercase() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },
    enter_Price() {
      this.$refs["Qty_ref"].$refs.input.focus();
    },
    enter_qty() {
      this.$refs["product_idname_ref"].$refs.input.focus();
    },
    async uppercase() {
      this.checK_idname = true;
      this.answer.sname = this.answer.sname.toUpperCase();
      console.log(this.answer.sname);
      await HTTP.get("shwethe_n/api/v1/product_price", {
        params: {
          ID: this.answer.sname,
        },
      }).then(
        (response) => (
          (this.data_product = JSON.parse(response.data)),
          console.log(this.data_product),
          this.$refs["Price_ref"].$refs.input.focus()
        )
      );

      if (this.data_product == false) {
        this.data_product = [{ mm_name: null }];

        console.log("");
      } else {
        console.log(this.data_product);
        console.log(this.prduct_mm_name_L);
      }
    },
    async uppercase_2() {
      this.answer_2.sname = this.answer_2.sname.toUpperCase();
      await HTTP.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.answer_2.sname,
        },
      }).then(
        (response) => (this.data_customer = JSON.parse(response.data))
        // (console.log(this.data_customer[0].mm_name))
      );
      this.data_customer_mmname = null;
      if (this.data_customer == false) {
        console.log(1);
        this.data_customer_mmname = null;
      } else {
        console.log(2);
        this.data_customer_mmname = this.data_customer[0].mm_name;
      }
    },
    itemText(item) {
      return `${item.id} | ${item.Time} | ${item.selie}`;
    },
    // value(item) {
    //   console.log(item);
    //   return `${item.id}`;
    // },
    onChange(a) {
      this.answer_3.duty_product_register = a.registration;
      this.answer_3.duty_product_selie = a.selie;
      console.log(a);
    },

    async uppercase_3() {
      this.checK_idname_3 = true;
      this.answer_4.idname = this.answer_4.idname.toUpperCase();
      await HTTP.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.answer_4.idname,
        },
      }).then(
        (response) => (this.data_customer_2 = JSON.parse(response.data))
        // (console.log(this.data_customer[0].mm_name))
      );
      this.data_customer_mmname_2 = null;
      if (this.data_customer_2 == false) {
        console.log(1);
        this.data_customer_mmname_2 = null;
      } else {
        console.log(this.data_customer_2);
        this.data_customer_mmname_2 = this.data_customer_2[0].mm_name;
        this.answer_4.id = this.data_customer_2[0].id;
        this.answer_4.mm_name = this.data_customer_2[0].mm_name;
      }
    },

    _uppercase_3() {
      console.log("1");
      if (this.checK_idname_3 != false) {
        this.$refs.answer_4_idname.reset();
        this.$refs.data_customer_mmname_2.reset();
        this.checK_idname_3 = false;
      } else {
        console.log("1");
      }
    },

    async refre() {
      var vvv = JSON.parse(localStorage.getItem("judy_car_item")); //forgot to close
      console.log(vvv);
      await shwethe_duty.get("/api/v1/duty_out_car_by", {
        params: {
          ID: vvv.car_bill_id,
        },
      }).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          console.log(this.table_list)
        )
      );

      try {
        this.desserts_2 = this.table_list[0].data_detail_b;
        this.table_list = this.table_list[0].data_detail;
        // console.log(this.table_list)
      } catch {
        this.table_list = [];
        this.desserts_2 = [];
      }

      this.$refs["product_idname_ref"].$refs.input.focus();
    },
    async del(item) {
      console.log(item);
      var res45 = alasql("SELECT * FROM ?", [[item]]);
      console.log(res45[0]);
      var vvv = JSON.parse(localStorage.getItem("judy_car_item")); //forgot to close
      await shwethe_duty.delete("/api/v1/duty_out_car_by", {
        data: {
          car_bill_id: vvv.car_bill_id,
          fen: vvv.fen,
          uid: 0,
          data_detail: res45[0],
        },
      })
        .then(function () {
          // location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {});
      this.refre();
    },
    editItem(item) {
      // this.editedIndex = this.desserts.indexOf(item);
      // this.editedItem = Object.assign({}, item);
      // this.dialog = true;
      console.log(item);
    },
  },
  async created() {
    // console.log(localStorage.getItem("uid"));
    // var _id_ = Date.now().toString();
    // this._id = _id_;
    // console.log(this._id);
    var vvv = JSON.parse(localStorage.getItem("judy_car_item")); //forgot to close
    console.log(vvv);
    console.log();
    var _id_ = Date.now().toString();
    this._id = _id_;
    console.log(this._id);
    // await HTTP_2.post(`shwethe_duty/api/v1/duty_out_car`, {
    //   car_bill_id: vvv.car_bill_id,
    //   fen: 1,
    //   uid: localStorage.getItem("uid"),
    // }).then((response) => {
    //   console.log(response);
    // });
    await shwethe_duty.get("/api/v1/duty_register_id", {
      params: {
        selie: this.selie,
      },
    }).then(
      (response) => (
        (this.items = JSON.parse(response.data)), console.log(this.items)
      )
    );
    await shwethe_duty.get("/api/v1/duty_out_car_by", {
      params: {
        ID: vvv.car_bill_id,
      },
    }).then(
      (response) => (
        (this.desserts = JSON.parse(response.data)), console.log(this.desserts)
      )
    );
    try {
      this.desserts_2 = this.table_list[0].data_detail_b;
      this.table_list = this.table_list[0].data_detail;
      // console.log(this.table_list)
    } catch {
      this.table_list = [];
      this.desserts_2 = [];
    }

    this.refre();
  },
};
</script>
