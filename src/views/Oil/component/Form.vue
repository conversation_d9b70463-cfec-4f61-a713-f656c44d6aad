<template>
  <v-card>
    <v-card-title>Oil report</v-card-title>

    <v-form
      ref="form"
      v-model="valid"
      @submit.prevent="submitForm"
      autocomplete="off"
    >
      <v-container>
        <v-row>
          <v-col cols="12">
            <v-autocomplete
              label="Shop"
              :item-text="itemText"
              :item-value="sname_value"
              :items="items_partner"
              v-model="answer.sname"
              :rules="rules.lock"
              clearable
            >
            </v-autocomplete>
          </v-col>
          <v-col cols="12">
            <v-autocomplete
              label="Car"
              :item-text="itemText"
              :item-value="sname_value"
              :items="items_car"
              v-model="answer.car"
              :rules="rules.lock"
              clearable
            >
            </v-autocomplete>
          </v-col>

          <v-col cols="12">
            <v-autocomplete
              label="Car"
              :item-text="itemText"
              :item-value="sname_value"
              :items="items_oil_type"
              v-model="answer.oil"
              :rules="rules.lock"
              clearable
            >
            </v-autocomplete>
          </v-col>
          <v-col>
            <v-text-field
              label="Qty"
              type="number"
              v-model="answer.qty"
              :rules="rules.qty"
            ></v-text-field>
          </v-col>
          <v-col>
            <v-text-field
              label="Price"
              type="number"
              v-model="answer.price"
              :rules="rules.price"
            ></v-text-field>
          </v-col>
          <v-col>
            <v-text-field
              label="distant"
              type="number"
              v-model="answer.distant"
              :rules="rules.distant"
            ></v-text-field>
          </v-col>
          <div class="w-100"></div>
          <v-col>
            <v-btn
              :disabled="!valid"
              color="success"
              class="mr-4"
              type="submit"
            >
              Save
            </v-btn>
          </v-col>
        </v-row>
        <!-- <pre>{{ answer }}</pre> -->
      </v-container>
    </v-form>
  </v-card>
</template>

<script>
import { HTTP } from "../../../plugins/http";

export default {
  data: () => ({
    valid: false,

    // Get api
    items_partner: [],
    items_car: [],
    items_oil_type: [],

    // require
    rules: {
      lock: [v => !!v || "Field is required"],
      price: [
        v => !!v || "required",
        v => (v && v.length <= 6) || "must be less than 6"
      ],
      qty: [
        v => !!v || "required",
        v => (v && v.length <= 5) || "must be less than 5"
      ],
      distant: [
        v => !!v || "required",
        v => (v && v.length <= 6) || "must be less than 6"
      ]
    },

    // answer
    answer: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: ""
    }
  }),

  methods: {
    submitForm() {
      this.$refs.form.validate();
      console.log("test");

      HTTP.post("/oil_insert", this.answer)
        .then(function() {
          alert("SUCCESS!!");
        })
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.form.reset();
        });
    },
    itemText(item) {
      return `${item.id} | ${item.idname} | ${item.mm_name}`;
    },
    sname_value(item) {
      return `${item.id}`;
    }
  },
  created() {
    HTTP.get(`oil_partner_list`).then(response => {
      console.log(response);
      (this.items_partner = response.data[0].partner),
        (this.items_car = response.data[0].car),
        (this.items_oil_type = response.data[0].oil_type);
    });
  }
};
</script>
