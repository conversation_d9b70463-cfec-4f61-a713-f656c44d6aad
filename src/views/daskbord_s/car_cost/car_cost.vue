<template>
  <v-app>
    <!-- <v-container> -->
    <div class="app">
      <b-row>
        <b-col md="12" sm="12">
          <Form />
        </b-col>
        <!-- <b-col md="8" sm="12">
          <Table />
        </b-col> -->
      </b-row>
    </div>
    <!-- </v-container> -->
  </v-app>
</template>
<script>
// import Form from "./component/Form";
// import Table from "./component/pre_order_table.vue";
import Form from "./component/view_car_cost_form.vue";
export default {
  components: {
    Form,
    // Table
  }
};
</script>
