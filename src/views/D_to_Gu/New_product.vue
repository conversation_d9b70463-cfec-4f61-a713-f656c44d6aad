<template>
  <v-app>
    <v-container>
      <v-btn @click="print" icon color="primary">
        <v-icon>print</v-icon>
      </v-btn>
      <v-card id="printMe">
        <v-card-title>
          New Product
          <v-spacer></v-spacer>
          <v-text-field
            v-model="search"
            append-icon="mdi-magnify"
            label="Search"
            single-line
            hide-details
          ></v-text-field>
        </v-card-title>
        <v-data-table
          :footer-props="{
            'items-per-page-options': [10, 20, 30, 40, 50]
          }"
          :search="search"
          :headers="headers"
          :items="desserts"
          :sort-by="['fen_x', 'want_qty']"
          multi-sort
        ></v-data-table>
      </v-card>
    </v-container>
  </v-app>
</template>

<script>
import { HTTP } from "../../plugins/http";

export default {
  data() {
    return {
      output: null,
      search: "",

      headers: [
        // { text: "id", value: "id" },
        { text: "Idname", value: "Idname" },
           { text: "c_idname_x", value: "c_idname_x" },
        { text: "fen", value: "fen" },
        { text: "lei", value: "lei" },
        { text: "mm_name_x", value: "mm_name_x" },
        { text: "th_name", value: "th_name" },
        { text: "c_idname_y", value: "c_idname_y" },
        { text: "mm_name_y", value: "mm_name_y" },
        { text: "qty_x", value: "qty_x" },

             ],

					

      desserts: [
        {
          // id: "",
          idname: "",
          mm_name: "",
          d_name: "",
          fen_x: "",
          qty_x: "",
          want_qty: "",
          fen_y: "",
          qty_y: ""
        }
      ]
    };
  },
  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    }
  },
  created: function() {
    HTTP.get("new_product").then(
      response => (
        console.log(response.data),
        (this.desserts = response.data)
        //  this.columns = response.data.columns
      )
    );
  }
};
</script>
