

<template>
  <div >
    <v-container class="contain">
      <b-row>
        <b-col md="12" sm="12">
          <v-card>
            <v-card-title>Upload App</v-card-title>
            <v-form
              ref="form"
              v-model="valid"
              @submit.prevent="submitForm"
              autocomplete="off"
            >
              <v-container>
                <v-row>
               
                  <v-col cols="3">
                    <v-autocomplete
                      label="bucket_name"
                      :item-text="bucket_name_text"
                      :item-value="bucket_name_value"
                      :items="bucket_name_list"
                      v-model="selected_bucketName"
                      :rules = "rules.lock"
                      @change="bucketChange"
                      return-object
                    >
                    </v-autocomplete>
                    </v-col>
                  <v-col cols="3">
                    <v-autocomplete
                      label="app_name"
                      :item-text="app_name_text"
                      :item-value="app_name_value"
                      :items="app_name"
                      v-model="appName"
                      :rules = "rules.lock"
                    >
                    </v-autocomplete>
                  </v-col>
                  <v-col cols="3">
                    <v-autocomplete
                      label="User Role"
                      :item-text="role_text"
                      :item-value="role_value"
                      :items="user_role"
                      v-model="userRole"
                      :rules = "rules.lock"
                    >
                    </v-autocomplete>
                  </v-col>
                  
                </v-row>
                <v-row>
                <v-col cols="3">
                    <v-file-input
                        label="File Upload"
                        outlined
                        dense
                        
                        v-model="files"
                        :rules = "rules.lock"
                        
                    ></v-file-input>
                    </v-col>
                    
                    
                  <v-col cols="3">
                    <v-autocomplete
                      label="app status"
                      :item-text="app_status_text"
                      :item-value="app_status_value"
                      :items="status"
                      v-model="app_status"
                      :rules = "rules.lock"
                    >
                    </v-autocomplete>
                  </v-col>
                  <v-col cols="3">
                  <v-text-field
                      v-model="version_app"
                      label="Version App"
                      :rules = "rules.lock"
                    ></v-text-field>
                    </v-col>
                     
                </v-row>
                    <v-btn
                      :disabled = "!valid"
                      color="success"
                      class="mr-4"
                      type="submit"
                    >
                     Upload
                    </v-btn>
                
              </v-container>
            </v-form>
          </v-card>
        </b-col>
        <b-col md="12" sm="12">
          <v-card id="printMe">
            <v-card-title>
              App Info
              <v-spacer></v-spacer>
            </v-card-title>
            <v-data-table
              :headers="headers"
              :items="file_app"
              :items-per-page="5"
              class="elevation-1"
            >
            <template v-slot:item.download="{ item }">
                <v-btn small color="primary" @click="downloadAPP(item)">download</v-btn>
              </template>
              <template v-slot:item.search="{ item }">
                <v-btn small color="success" @click="searchItem(item)"
                  >Search</v-btn
                >
              </template>
            </v-data-table>
          </v-card>
        </b-col>
    </b-row>
</v-container>
<template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
</div>
</template>


<script>
  
import {shwethe_file_path} from "../../plugins/http";

export default {
  data: () => ({
    valid: false,
    appName: 0,
    bucket_name_list: [],
    app_name: [],
    user_role: [],
    selected_bucketName: [],
    bucketName: "",
    bucketId:0,
    appNname: "",
    files: [], 
    icon_files: [],
    file_path: "",
    userRole:0,
    app_status:"",
    version_app: "",
    overlay: false,
    bucket_name: "",
    headers: [
      { text: "shwethe_app_name_id", value: "shwethe_app_name_id" },
     
      { text: "shwethe_file_name", value: "shwethe_file_name" },
      { text: "file_extension", value: "file_extension" },
      
      { text: "file_path", value: "file_path" },
      
      { text: "version_app", value: "version_app" },
      { text: "user_role", value: "data_sub.user_role" },
      { text: "bucket_name", value: "data_sub.minio_bucket" },
      { text: "app_status", value: "data_sub.app_status" },
      { text: "download", value: "download" },
    ],
    file_app: [],

    // require
    rules: {
      lock: [v => !!v || "required"],
      lock_2: [
        v => !!v || "required",
        v => v >= 0.1 || "required",
        v => !!v == Number.isInteger(v) || "Must be integer"
      ]
    },
    //request_body
    req_body: {
        shwethe_app_name_id: 0,
        bucket_name_id: 0,
        bucket_name: "",
        app_status: "",
        user_role: 0,
        version_app: ""
        },
    
    
    status: [
      { text: "beta", value: "beta" },
      { text: "stable", value: "stable" }
    ],
  }),

  methods: {
    async bucketChange()
    {
      this.bucketName = this.selected_bucketName.shwethe_app_name;
      this.bucketId = this.selected_bucketName.shwethe_app_name_id;
     
    },
    
    
    async submitForm() {
      this.overlay = true;
      var formData = new FormData();
      console.log(this.files);
      this.req_body.shwethe_app_name_id = this.appName;
      this.req_body.bucket_name_id = this.bucketId;
      this.req_body.bucket_name = this.bucketName;
      this.req_body.app_status = this.app_status;
      this.req_body.user_role = this.userRole;  
      this.req_body.version_app = this.version_app;
      
        // files
      
      var jsonstr = JSON.stringify(this.req_body);
      
      formData.append("req_body",jsonstr);
      formData.append("file", this.files);
      
      console.log(jsonstr);

        // additional data
      //formData.append("test", "foo bar");
      

      await shwethe_file_path
        .post(`/api/v1/file_path/file_path`,formData,{
                headers: {
                'Content-Type': 'multipart/form-data'
                }
            })
        .then(response => {
          console.log(response);
          
        })
        .catch(e => {
          console.log(e);
          this.overlay = false;
          alert("FAILURE!!");
        })
        .finally(() => {
          
          this.$refs.form.reset();
        });

      
     
      this.list_rerst();
      this.overlay = false;
      this.$refs.form.reset();
      
    },
    async list_rerst() {
      console.log("list_rerst");
      await shwethe_file_path.get(`/api/v1/file_path/file_path`, {
        //s_mm_id: this.smm_id,
        //car_id: this.car_id,
      })
      .then(response => {
        console.log(JSON.parse(response.data));
        this.file_app = JSON.parse(response.data);
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });
    },
    
    
    app_name_text(item) {
      return `${item.shwethe_app_name}`;
    },
    app_name_value(item) {
      return `${item.shwethe_app_name_id}`;
    },
    bucket_name_text(item) {
      return `${item.shwethe_app_name}`;
    },
    bucket_name_value(item) {
      return `${item.shwethe_app_name_id}`;
    },
    role_text(item) {
      return `${item.shwethe_app_name}`;
    },
    role_value(item) {
      return `${item.shwethe_app_name_id}`;
    },
    app_status_text(item) {
      return `${item.text}`;
    },
    app_status_value(item) {
      //this.fen_dian_id = item.fen_dian_id;
      return `${item.value}`;
    },


    async downloadAPP(item){
      console.log(item);
      
      //this.overlay = true;
      await shwethe_file_path
        .post(`/api/v1/file_path/download`,{
                
              shwethe_app_name: item.shwethe_app_name,
              bucket_name_id: item.data_sub.minio_bucket,
              file_path: item.file_path,
              file_extension: item.file_extension,
              version_app: item.version_app
            
            })
        .then(response => {
           //let file = new Blob([response.data]);

          //let url = URL.createObjectURL(file);
          console.log(response);
          const a = document.createElement("a");
          a.href = response.data;
          a.setAttribute('download', item.shwethe_app_name+item.file_extension);
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          console.log(a);
         
        })
        .catch(e => {
          console.log(e);
          this.overlay = false;
          alert("FAILURE!!");
        })
        .finally(() => {
          this.overlay = false
          
        });


    }
  },
  async created() {
    this.overlay = true;
    var _id_ = Date.now().toString();
    this.datetime = _id_;
    await shwethe_file_path.get(`api/v1/file_path/bucket_name`, {
        
      })
      .then(response => {
        (this.bucket_name_list = JSON.parse(response.data)),
          console.log(JSON.parse(response.data));
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });
    
    await shwethe_file_path.get(`api/v1/file_path/app_name`, {
        
      })
      .then(response => {
        (this.app_name = JSON.parse(response.data)),
          console.log(JSON.parse(response.data));
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });

    await shwethe_file_path.get(`api/v1/file_path/user_role`, {
        
      })
      .then(response => {
        (this.user_role = JSON.parse(response.data)),
          console.log(JSON.parse(response.data));
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });

    await shwethe_file_path.get(`/api/v1/file_path/file_path`, {
        //s_mm_id: this.smm_id,
        //car_id: this.car_id,
      })
      .then(response => {
        console.log(JSON.parse(response.data));
        this.file_app = JSON.parse(response.data);
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });

    this.overlay = false;
  }
};
</script>
<style scoped>
.contain {
  margin: auto 5% auto 5%;
}
</style>