<template>
  <div>
      <b-row>
        <b-col md="12" sm="12">
          <v-card>
            <v-card-title>Form</v-card-title>
            <v-form
              ref="form"
              v-model="valid"
              @submit.prevent="submitForm"
              autocomplete="off"
            >
              <v-container>
                <v-row>
                  <v-col cols="4">
                    <v-autocomplete
                      label="Parner_Name"
                      :item-text="parner_itemText"
                      :item-value="parner_value"
                      :items="parner_list_json"
                      v-model="smm_id"
                      :rules="rules.lock"
                    >
                    </v-autocomplete>
                  </v-col> 
                  <v-col cols="4">
                    <v-autocomplete
                      label="Car_bill_id"
                      :item-text="car_itemText"
                      :item-value="car_value"
                      :items="car_list_json"
                      v-model="selectedCar"
                      :rules="rules.lock"
                      @change="changeCar"
                      return-object
                    >
                    </v-autocomplete>
                  </v-col>
                  <v-col cols="4">
                    <v-text-field
                      v-model="smm_name"
                      label="Parner Name"
                      disabled
                    ></v-text-field>
                    <!-- <v-autocomplete
                      label="fen_dian_id"
                      :item-text="fen_dian_itemText"
                      :item-value="fen_dian_value"
                      :items="fen_dian_list_json"
                      v-model="fen_dian_id"
                      :rules="rules.lock"
                    >
                    </v-autocomplete> -->
                  </v-col>
                  <v-col cols="4">
                    <!-- <v-text-field
                      v-model="fen_dian_id"
                      label="fen_dian_id"
                      disabled
                    ></v-text-field> -->
                    <v-autocomplete
                      label="fen_dian_id"
                      :item-text="fen_dian_itemText"
                      :item-value="fen_dian_value"
                      :items="fen_dian_list_json"
                      v-model="fen_dian_id"
                      :rules="rules.lock"
                    >
                    </v-autocomplete>
                  </v-col>

                  <div class="w-100"></div>
                  <v-col>
                    <v-btn
                      :disabled="!valid"
                      color="success"
                      class="mr-4"
                      type="submit"
                    >
                      add to list
                    </v-btn>
                  </v-col>
                </v-row>
              </v-container>
            </v-form>
          </v-card>
        </b-col>

        <!-- <pre>{{ answer }}</pre> -->

        <b-col md="12" sm="12">
          <v-card id="printMe">
            <v-card-title>
              Form listsss
              <v-spacer></v-spacer>
            </v-card-title>
            <v-data-table
              :headers="headers"
              :items="desserts"
              :items-per-page="5"
              class="elevation-1"
            >
              <template v-slot:top>
                <v-dialog v-model="dialog" hide-overlay max-width="30%">
                  <v-card>
                    <v-card-title class="headline"></v-card-title>

                    <v-card-text>
                      <p>
                        <b>Car Round</b>
                      </p>

                      <v-text-field
                        v-model="search_car_round"
                        label="Input to car round"
                        type="number"
                        :autofocus="true"
                      ></v-text-field>
                      <!-- <v-text-field
                        v-model="editedItem.product_price"
                        label="product_price"
                        disabled
                      ></v-text-field>
                      <v-text-field
                        v-model="editedItem.product_idname"
                        label="product_idname"
                        disabled
                      ></v-text-field>
                      <v-text-field
                        v-model="editedItem.product_mm_name"
                        label="product_mm_name"
                        disabled
                      ></v-text-field>
                      <v-text-field
                        v-model="editedItem.product_d_name"
                        label="product_d_name"
                        disabled
                      ></v-text-field> -->

                      <v-btn @click="searchOrder" color="success" class="mr-4">
                        Save
                      </v-btn>

                      <!-- <pre>{{ answer }}</pre> -->
                    </v-card-text>
                  </v-card>
                </v-dialog>
              </template>
              <template v-slot:item.edit="{ item }">
                <v-btn small color="primary" @click="goNext(item)">product</v-btn>
              </template>
              <template v-slot:item.view_2="{ item }">
                <v-btn small color="primary" @click="goNext_2(item)">car cost</v-btn>
              </template>
              <template v-slot:item.search="{ item }">
                <v-btn small color="success" @click="searchItem(item)"
                  >input round</v-btn
                >
              </template>
            </v-data-table>
          </v-card>
        </b-col>
      </b-row>
    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import {
  HTTP,
  // mongodb_api,
  shwethe_order_in_fast_api
} from "../../../plugins/http";

export default {
  data: () => ({
    order_s_insert_bill_id: "",
    search_car_round: 0,
    comment: "",
    jin_huo_bian: 0,
    overlay: false,
    valid: false,
    datetime: null,
    checK_idname: false,
    smm_name: "",
    smm_id: 0,
    car_id: 0,
    car_name: "",
    parner_list_json: [],
    car_list_json: [],
    selectedCar: [],
    fen_dian_list_json: [],
    fen_dian_id: 0,
    dialog: false,
    headers: [
      
      { text: "jin_huo_bian", value: "jin_huo_bian" },
      { text: "datetime", value: "datetime" },
      //{ text: "parner_id", value: "parner_id" },
      { text: "parner_idname", value: "parner_idname" },
      { text: "parner_mmname", value: "parner_mmname" },
      //{ text: "car_id", value: "car_id" },
      { text: "car_idname", value: "car_idname" },
      { text: "car_mm_name", value: "jia_yi_mm_name_y" },
      { text: "fen_dian_id", value: "fen_dian_id" },
      { text: "car_round", value: "car_round" },

      { text: "order_s_insert_bill_id", value: "order_s_insert_bill_id" },
      { text: "view", value: "edit" },
      { text: "view_2", value: "view_2" },
      { text: "Search", value: "search" }
    ],
    desserts: [],

    // require
    rules: {
      lock: [v => !!v || "required"],
      lock_2: [
        v => !!v || "required",
        v => v >= 0.1 || "required",
        v => !!v == Number.isInteger(v) || "Must be integer"
      ]
    },

    auto_id: "",
    answer: {
      car_bill_id: 0,
      fen: 0,
      uid: 0,
      sname: ""
    },

    data_product: [{ mm_name: null }],
    data_customer: null,
    data_customer_mmname: null,

    items: [
      { text: "shwethe 1", value: 1 },
      { text: "shwethe 2", value: 2 },
      { text: "shwethe 3", value: 3 }
    ]
  }),

  methods: {
    searchItem(item) {
      this.order_s_insert_bill_id = item.order_s_insert_bill_id;
      this.fen_dian_id = item.fen_dian_id;
      this.jin_huo_bian = item.jin_huo_bian;
      this.search_car_round = item.car_round;
      this.dialog = true;
      console.log(item)
      // console.log(item.fen_dian_id);
      // console.log(item);
      // console.log(item["data_sub.jin_huo_bian"]);
    },
    async searchOrder() {
      await shwethe_order_in_fast_api
        .put(`/api/v1/order_s_insert/in_order/car_round`, {
          order_s_insert_bill_id: this.order_s_insert_bill_id,
          fen_dian_id: parseInt(this.fen_dian_id),
          jin_huo_bian: parseInt(this.jin_huo_bian),
          car_round: parseInt(this.search_car_round)
        })
        .then(response => {
          //this.desserts = JSON.parse(response.data);
          console.log(this.order_s_insert_bill_id);
          console.log(this.fen_dian_id);
          console.log(this.jin_huo_bian);
          console.log(parseInt(this.search_car_round));
          console.log(response.data);
          alert("success");
        })
        .catch(e => {
        //   console.log(this.order_s_insert_bill_id);
        //   console.log(this.fen_dian_id);
        //   console.log(this.jin_huo_bian);
        //   console.log(parseInt(this.search_car_round));
        //  // console.log(response.data);
        //   this.overlay = false;
          alert("FAILURE!!");
          console.log(e);
        })
        .finally(() => {
          this.dialog = false;
          this.list_rerst();
          this.$refs.form.reset();
        });
    },
    async changeCar() {
      this.smm_name = this.selectedCar.parner_mm_name;
      this.fen_dian_id = this.selectedCar.fen_dian_id;
      // this.smm_id = this.selectedCar.parner_id;
      this.car_id = this.selectedCar.car_id;
      this.jin_huo_bian = this.selectedCar.jin_huo_bian;
      this.comment = this.selectedCar.comment;
      console.log(this.selectedCar);
      console.log(this.smm_name);
    },
    async search_parner_list(val) {
      console.log(val);
      await shwethe_order_in_fast_api
        .get("/api/v1/search/product_search", {
          params: {
            ID: val
          }
        })
        .then(response => {
          console.log(JSON.parse(response.data));
          if (JSON.parse(response.data) != false) {
            this.model = JSON.parse(response.data);
            this.smm_id = this.model[0].jia_yi_id;
            this.smm_name = this.model[0].jia_yi_mm_name;
          } else {
            this.smm_name = "";

            console.log("");
          }
        });
    },
    async search_car_list(val) {
      console.log(val);
      await shwethe_order_in_fast_api
        .get("/api/v1/search/jia_yi_search", {
          params: {
            ID: val
          }
        })
        .then(response => {
          console.log(JSON.parse(response.data));
          if (JSON.parse(response.data) != false) {
            this.model = JSON.parse(response.data);
            this.car_id = this.model[0].jia_yi_id;
            this.car_name = this.model[0].car_mm_name;
          } else {
            this.car_name = "";

            console.log("");
          }
        });
    },
    async submitForm() {
      this.overlay = true;
      // await HTTP_2.post(
      //   "shwethe_order_in_fast_api/api/v1/order_s_insert/order_s_insert",
      //   this.answer
      // )
      //   .then(() => {
      //     console.log("SUCCESS!!");
      //   })
      //   .catch(() => {
      //     alert("FAILURE!!");
      //   })
      //   .finally(() => {
      //     this.$refs.form.reset();
      //   });

      await shwethe_order_in_fast_api
        .post(`/api/v1/order_s_insert/in_order/order_s_insert`, {
          parner_id: this.smm_id,
          car_id: this.car_id,
          fen_dian_id: this.fen_dian_id,
          jin_huo_bian: this.jin_huo_bian,
          comment: this.comment
        })
        .then(response => {
          console.log(response);
          console.log(this.fen_dian_id);
        })
        .catch(() => {
          this.overlay = false;
          alert("FAILURE!!");
        })
        .finally(() => {
          this.$refs.form.reset();
        });

      await shwethe_order_in_fast_api
        .get(`/api/v1/order_s_insert/in_order/order_s_list`, {
          //s_mm_id: this.smm_id,
          //car_id: this.car_id,
        })
        .then(response => {
          this.desserts = JSON.parse(response.data);
          console.log(response);
        })
        .catch(() => {
          this.overlay = false;
          alert("FAILURE!!");
        })
        .finally(() => {
          this.$refs.form.reset();
        });
      // await HTTP_2.get("shwethe_duty/api/v1/duty_out_car", {
      //   params: {
      //     auto_id: this.auto_id,
      //   },
      // }).then(
      //   (response) => (
      //     (his.desserts = JSON.parse(response.datta)),
      //     console.log(this.desserts)
      //   )goNext
      // );

      this.overlay = false;
      this.$refs.form.reset();
    },
    async list_rerst() {
      console.log("list_rerst");
      await shwethe_order_in_fast_api
        .get("/api/v1/order_s_insert/in_order/order_s_list", {
          params: {
            list_id: this.order_s_insert_bill_id,
          },
        })
        .then((response) => {
          
          console.log(JSON.parse(response.data));
          this.desserts = JSON.parse(response.data);
          
        });
    },
    async goNext(item) {
      this.overlay = true;
      console.log(item);
      localStorage.setItem(
        "order_s_insert_bill_id",
        JSON.stringify(item.order_s_insert_bill_id)
      );
      localStorage.setItem(
        "jin_huo_bian",
        JSON.stringify(item.jin_huo_bian)
      );
      localStorage.setItem(
        "parner_id",
        JSON.stringify(item.parner_id)
      );
      localStorage.setItem(
        "row_type","for_product"
      );
      //this.overlay = false;
      window.location.href = "in_Form";
    },
    async goNext_2(item) {
      this.overlay = true;
      console.log(item);
      // console.log(item.data_sub.jin_huo_bian);
      console.log(item.order_s_insert_bill_id)
      console.log(item.order_s_insert_bill_id +   "2" )
      console.log('1' + '1')
      localStorage.setItem(
        "order_s_insert_bill_id",
        JSON.stringify(item.order_s_insert_bill_id +   "2" )
      );
      localStorage.setItem(
        "jin_huo_bian",
        JSON.stringify(item.jin_huo_bian)
      );
      localStorage.setItem(
        "row_type","for_car_cost"
      );
      localStorage.setItem(
        "parner_id",
        JSON.stringify(item.parner_id)
      );
      this.overlay = false;
      window.location.href = "in_Form";
    },

    _uppercase() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },

    async uppercase() {
      this.checK_idname = true;
      this.answer.sname = this.answer.sname.toUpperCase();
      await HTTP.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.answer.sname
        }
      }).then(
        response => (this.data_customer = JSON.parse(response.data))
        // (console.log(this.data_customer[0].mm_name))
      );
      this.data_customer_mmname = null;
      if (this.data_customer == false) {
        console.log(1);
        this.data_customer_mmname = null;
      } else {
        console.log(this.data_customer);
        this.data_customer_mmname = this.data_customer[0].mm_name;
        this.answer.ke_bian = this.data_customer[0].id;
      }
    },
    parner_itemText(item) {
      return `${item.parner_idname} | ${item.parner_mmname}`;
    },
    parner_value(item) {
      return `${item.parner_id}`;
    },
    car_itemText(item) {
      return `${item.car_idname} | ${item.car_mm_name} | ${item.jin_huo_bian}`;
    },
    car_value(item) {
      return `${item.jin_huo_bian}`;
    },
    fen_dian_itemText(item) {
      return `${item.fen_dian_id} | ${item.fen_dian_name}`;
    },
    fen_dian_value(item) {
      //this.fen_dian_id = item.fen_dian_id;
      return `${item.fen_dian_id}`;
    }
  },
  async created() {
    this.overlay = true;
    var _id_ = Date.now().toString();
    this.datetime = _id_;
    await shwethe_order_in_fast_api
      .get(`/api/v1/order_s_insert/in_order/order_s_list`, {
        //s_mm_id: this.smm_id,
        //car_id: this.car_id,
      })
      .then(response => {
        (this.desserts = JSON.parse(response.data)),
          console.log(JSON.parse(response.data));
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });
    await shwethe_order_in_fast_api
      // .get(`/api/v1/order_s_insert/parner_name_list`, {
        .get(`/api/v1/order_search/parner_name_list`, {
        //s_mm_id: this.smm_id,
        //car_id: this.car_id,
      })
      .then(response => {
        console.log(response.data);
        this.parner_list_json = response.data;
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });
    await shwethe_order_in_fast_api
      .get(`/api/v1/order_s_insert/jin_huo_car`, {
        //s_mm_id: this.smm_id,
        //car_id: this.car_id,
      })
      .then(response => {
        console.log(JSON.parse(response.data));
        this.car_list_json = JSON.parse(response.data);
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });
    await shwethe_order_in_fast_api
      .get(`/api/v1/order_search/fen_dian_list`, {
        //s_mm_id: this.smm_id,
        //car_id: this.car_id,
      })
      .then(response => {
        console.log(JSON.parse(response.data));
        this.fen_dian_list_json = JSON.parse(response.data);
      })
      .catch(() => {
        this.overlay = false;
        alert("Creat FAILURE!!");
      })
      .finally(() => {
        this.$refs.form.reset();
      });
    this.overlay = false;
  }
};
</script>
