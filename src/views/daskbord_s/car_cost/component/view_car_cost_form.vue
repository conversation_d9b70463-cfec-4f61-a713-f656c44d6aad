<template>
  <div>
    <b-row>
      <b-col md="4" sm="12">
        <v-card id="printMe">
          <v-card-title>
            ကားတင်စာရင်း
            <v-spacer></v-spacer>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers_1"
            :items="table_list_1"
            multi-sort
          >
            <template v-slot:item.edit="{ item }">
              <v-btn small color="primary" @click="editItem(item)">View</v-btn>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
      <b-col md="8" sm="12">
        <v-card id="printMe">
          <v-card-title>
            ပစ္စည်း စာရင်း
            <v-spacer></v-spacer>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers"
            :items="table_list"
            multi-sort
          >
            <template v-slot:top>
              <v-dialog v-model="dialog" max-width="500px">
                <v-card>
                  <v-card-text>
                    <v-container>
                      <h4>{{ editedItem.idname }}</h4>
                      <v-form ref="form" v-model="valid" autocomplete="off">
                        <v-row>
                          <v-col cols="12">
                            <v-text-field
                              v-model.number="editedItem.product_qty"
                              label="product_qty"
                              disabled
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-form>
                    </v-container>
                    <v-col cols="12">
                      <v-text-field
                        v-model="price_iv"
                        label="Price"
                        filled
                        @keyup="key_up_price"
                      ></v-text-field>
                    </v-col>
                  </v-card-text>

                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="blue darken-1" text @click="close">
                      Cancel
                    </v-btn>
                    <v-btn
                      color="blue darken-1"
                      text
                      :disabled="valid"
                      @click="save"
                    >
                      Save
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-dialog>
            </template>
            <template v-slot:item.edit_2="{ item }">
              <v-icon class="mr-2" @click="editItem_2(item)">
                mdi-pencil
              </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            ပစ္စည်းတင်ကားခ
            <v-spacer></v-spacer>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :search="search"
            :headers="car_cost_h"
            :items="car_cost_t"
            :sort-by="['', '']"
            multi-sort
          >
            <template v-slot:item.edit="{ item }">
              <v-icon small @click="del(item)"> mdi-delete </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
           ပစ္စည်းတင်ကားခ
            <v-spacer></v-spacer>
          </v-card-title>
          <v-form ref="form" v-model="valid_2" autocomplete="off">
            <v-container>
              <v-row>
                <v-col cols="5">
                  <v-text-field
                    label="idname"
                    v-model="che_liang"
                    @keyup="_uppercase_5"
                    @keyup.enter="uppercase_5"
                    outlined
                    :rules="rules.lock"
                    ref="che_liang"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="5">
                  <v-text-field
                    label="name"
                    v-model="che_liang_mmname"
                    outlined
                    :rules="rules.lock"
                    disabled
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="2">
                  <v-btn
                    @click="submitCar"
                    :disabled="!valid_2"
                    color="success"
                    class="mr-4"
                  >
                    confirm
                  </v-btn>
                </v-col>
              </v-row>
              <v-row>
                <v-text-field
                  v-model="search"
                  append-icon="mdi-magnify"
                  label="Search"
                  single-line
                  hide-details
                ></v-text-field>
              </v-row>
            </v-container>
          </v-form>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :search="search"
            :headers="headers_2"
            :items="table_list_2"
            :sort-by="['', '']"
            multi-sort
          >
            <template v-slot:item.edit="{ item }">
              <v-icon small @click="del(item)"> mdi-delete </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import { HTTP_shwethe_n } from "../../../../plugins/http";
// const alasql = require("alasql");

export default {
  data: () => ({
    chi_ci:"",
    price_iv: "",
    checK_idname: false,
    overlay: false,
    che_liang: null,
    che_liang_id: null,
    che_liang_mmname: null,
    ke_bian: null,
    ke_bian_mmname: null,
    price_datable: null,
    headers_1: [
      { text: "che_ci", value: "che_ci" },
      { text: "views", value: "edit" },
    ],
    table_list_1: [],
    car_cost_h:[
      { text: "idname", value: "idname" },
       { text: "mm_name", value: "mm_name" },
       { text: "price", value: "price" },
    ],
    car_cost_t:[],
    headers_2: [
      { text: "yi_fang_idname", value: "yi_fang_idname" },
      { text: "yi_fang_mm_name", value: "yi_fang_mm_name" },
      { text: "product_idname", value: "product_idname" },
      { text: "product_mm_name", value: "product_mm_name" },
      { text: "product_d_name", value: "product_d_name" },
      { text: "product_qty", value: "product_qty" },
      { text: "type_line", value: "type_line" },
      { text: "product_price", value: "product_price" },
      { text: "car_cost_price", value: "car_cost_price" },
      { text: "edit", value: "edit" },
    ],
    table_list_2: [],
    data_customer_valid: false,
    valid: true,
    valid_2: true,
    dialog: false,
    search: "",
    // Get api
    items_partner: [],
    items_car: [],
    items_oil_type: [],
    headers: [
      // { text: "id", value: "id" },
      { text: "che_ci", value: "che_ci" },
      { text: "idname", value: "idname" },
      { text: "mm_name", value: "mm_name" },
      { text: "product_mm_name", value: "product_mm_name" },
      { text: "product_d_name", value: "product_d_name" },
      { text: "product_qty", value: "product_qty" },
      { text: "id", value: "edit_2" },
    ],
    // require
    rules: {
      lock: [(v) => !!v || "required"],
      price: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      qty: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      distant: [(v) => !!v || "required"],
    },
    _id: null,
    table_list: [],
    // answer
    data_product: [{ mm_name: null }],
    data_customer: null,
    data_customer_mmname: null,
    answer_2: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    answer: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    recoud_table: null,
    alert: false,
    editedIndex: -1,
    editedItem: {
      idname: "",
      product_qty: "",
      qty_select: "",
    },
  }),

  computed: {
    emailConfirmationRules() {
      return [
        () =>
          this.editedItem.product_qty >= this.editedItem.qty_select ||
          "more than product_qty",
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ];
    },
  },

  methods: {
    async del(item) {
      console.log(item);
      this.overlay = true;
      // console.log(item.che_ci);
      // console.log(item.bill_id);

      await HTTP_shwethe_n.delete(
        `/api/v2/view_car_cost_form/view_car_cost_form_by`,
        {
          data: {
            che_ci: this.che_ci,
            data_jsonb: [
              {
                bill_id: item.bill_id,
                che_ci: item.che_ci,
                fen: item.fen,
                line: item.line,
                product_id: item.product_id,
                product_price: item.product_price,
                product_qty: item.product_qty,
                type_line: item.type_line,
                yi_fang: item.yi_fang,
                car_cost_price: item.car_cost_price,
              },
            ],
          },
        }
      ).then((response) => {
        console.log(response);
      });

            await HTTP_shwethe_n.get(
        "/api/v2/view_car_cost_form/view_car_cost_form_by",
        {
          params: {
            che_ci: this.che_ci,
          },
        }
      ).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          console.log(this.table_list)
        )
      );

    await this.sync_data()
    this.overlay = false;
      // this.editItem(item);
    },
    key_up_price() {
      console.log(this.price_iv);
      this.valid = false;
    },
    async submitCar() {
      if (this.table_list_2 == false) {
        console.log("log");
        alert("no data");
      } else {
        this.overlay = true;
        console.log("go");

        await HTTP_shwethe_n.put(
          "/api/v1/view_preproduct/view_preproduct_compls",
          {
            bill_id: this._id,
            che_liang: 0,
            data_: {
              status: "wait_sucess",
            },
          }
        )
          .then(function () {
            location.reload();
          })
          .catch(function () {
            alert("FAILURE!!");
          })
          .finally(() => {
            // location.reload();
          });

        // location.reload();
      }
    },

    async uppercase_5() {
      this.checK_idname = true;
      this.che_liang = this.che_liang.toUpperCase();
      console.log("AAAAAAAAAAAAA");
      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.che_liang,
        },
      }).then(
        (response) => (this.che_liang_mmname = JSON.parse(response.data))
      );
      if (this.che_liang_mmname == false) {
        console.log(1);
        this.che_liang_id = null;
        this.che_liang_mmname = null;
      } else {
        console.log(2);
        this.che_liang_id = this.che_liang_mmname[0].id;
        this.che_liang_mmname = this.che_liang_mmname[0].mm_name;
      }
    },
    _uppercase_5() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },

    async editItem_2(item) {
      console.log(Object.assign({}, item));
      this.editedIndex = this.table_list.indexOf(item);
      this.editedItem = Object.assign({}, item);
      this.recoud_table = item;
      //  this.editedItem = item
      this.dialog = true;
      // console.log(item);
      // console.log(item.product_id);
      // await HTTP_2.get(
      //   "/shwethe_n/api/v1/view_preproduct/view_preproduct_qty",
      //   {
      //     params: {
      //       id: item.product_id,
      //     },
      //   }
      // ).then((response) => (this.price_datable = JSON.parse(response.data)));
    },
    close() {
      this.dialog = false;
    },
    // click_number() {
    //   console.log(this.price_datable);
    //   console.log(this.editedItem);
    //   this.valid = true;
    //   var res4 = alasql(
    //     "SELECT Idname,SUM(Convert(Float,qty_1)) AS qty FROM ? GROUP BY Idname",
    //     [this.price_datable]
    //   );
    //   console.log(this.editedItem.product_qty);
    //   if (res4[0].qty > 0) {
    //     if (this.editedItem.product_qty < res4[0].qty) {
    //       this.valid = true;
    //       console.log(33);
    //     } else {
    //       this.valid = false;
    //       console.log(34);
    //     }
    //     console.log(1);
    //   } else {
    //     console.log(2);

    //     this.valid = true;
    //   }
    //   var res9 = alasql(
    //     "SELECT Idname as product_id,qty_1 AS product_qty,type as type_line,fen FROM ?  where qty_1 <> 0 ",
    //     [this.price_datable]
    //   );
    //   var item_2 = [this.editedItem];
    //   var res4444 = alasql(
    //     "SELECT yi_fang,product_d_name,product_id,product_idname,product_mm_name,product_price,bill_id,line FROM ? ",
    //     [item_2]
    //   );
    //   console.log(res9);
    //   console.log(res4444);
    //   var res = alasql(
    //     "SELECT  *  FROM ? res_1 inner JOIN ? res_2 ON res_1.product_id = res_2.product_id  ",
    //     [res9, res4444]
    //   );

    //   // const array3 = res9.concat(res4444);

    //   this.recoud_table = res;
    //   console.log(res);
    // },
    async editItem_(item) {
      console.log(item);

      await HTTP_shwethe_n.put(`/api/v2/view_preproduct/view_preproduct`, {
        bill_id: this._id,
        uid: localStorage.getItem("uid"),
        yi_fang: item[0].yi_fang,
        data_jsonb: item,
      }).then((response) => {
        console.log(response);
      });

      await HTTP_shwethe_n.get(
        `/api/v1/view_preproduct/view_preproduct_insert`,
        {
          params: {
            ID: this._id,
          },
        }
      ).then((response) => {
        console.log(response);
        this.table_list_2 = JSON.parse(response.data);
      });
    },
    async save() {
      this.close();
      this.overlay = true;
      // this.editItem_(this.recoud_table);
      this.valid = true
      console.log(this.recoud_table.che_ci);
      console.log(this.recoud_table);

      this.table_list.splice(this.editedIndex, 1);
      console.log({
        che_ci: this.recoud_table.che_ci,
        data_jsonb: {
          bill_id: this.recoud_table.bill_id,
          che_ci: this.recoud_table.che_ci,
          fen: this.recoud_table.fen,
          idname: this.recoud_table.idname,
          line: this.recoud_table.line,
          product_id: this.recoud_table.product_id,
          product_price: this.recoud_table.product_price,
          product_qty: this.recoud_table.product_qty,
          type_line: this.recoud_table.type_line,
          yi_fang: this.recoud_table.yi_fang,
        },
      });

      await HTTP_shwethe_n.put(
        `/api/v3/view_car_cost_form/view_car_cost_form_by`,
        {
          che_ci: this.recoud_table.che_ci,
          che_liang: this.recoud_table.che_liang,
          data_jsonb: [
            {
              bill_id: this.recoud_table.bill_id,
              che_ci: this.recoud_table.che_ci,
              fen: this.recoud_table.fen,
              idname: this.recoud_table.idname,
              line: this.recoud_table.line,
              product_id: this.recoud_table.product_id,
              product_price: this.recoud_table.product_price,
              product_qty: this.recoud_table.product_qty,
              type_line: this.recoud_table.type_line,
              yi_fang: this.recoud_table.yi_fang,
              car_cost_price: this.price_iv,
            },
          ],
        }
      ).then((response) => {
        console.log(response);
      });
      await this.sync_data()
      this.overlay = false;
      // await HTTP_shwethe_n.get(
      //   "/api/v1/view_car_cost_form/car_cost_insert_form_by",
      //   {
      //     params: {
      //       ID: this.recoud_table.che_ci,
      //     },
      //   }
      // ).then((response) => (this.table_list_2 = JSON.parse(response.data)));

      // await HTTP_shwethe_n.get(
      //   "/api/v2/view_car_cost_form/view_car_cost_by_che_ci",
      //   {
      //     params: {
      //       che_ci: this.recoud_table.che_ci,
      //     },
      //   }
      // ).then(
      //         (response) => (
      //           (this.car_cost_t = JSON.parse(response.data)),
      //           console.log(this.car_cost_t)
      //         )
      //       ).catch(function (error) {
      //     // handle error
      //     console.log(error);
      //   });

    },
    async sync_data(){
              this.car_cost_t = []

        await HTTP_shwethe_n.get(
        "/api/v1/view_car_cost_form/car_cost_insert_form_by",
        {
          params: {
            ID: this.che_ci,
          },
        }
      ).then((response) => (this.table_list_2 = JSON.parse(response.data)));

      await HTTP_shwethe_n.get(
        "/api/v2/view_car_cost_form/view_car_cost_by_che_ci",
        {
          params: {
            che_ci: this.che_ci,
          },
        }
      ).then(
              (response) => (
                (this.car_cost_t = JSON.parse(response.data)),
                console.log(this.car_cost_t)
              )
            ).catch(function (error) {
          // handle error
          console.log(error);
        });
    },
    async editItem(item) {
      this.che_ci = item.che_ci
      console.log(item.che_ci)
      this.table_list_2 = [];

      this.overlay = true;
      var _id_ = Date.now().toString();
      this._id = _id_;
      console.log(item);
      await HTTP_shwethe_n.get(
        "/api/v2/view_car_cost_form/view_car_cost_form_by",
        {
          params: {
            che_ci: this.che_ci,
          },
        }
      ).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          console.log(this.table_list)
        )
      );
      // await HTTP_shwethe_n.put("/api/v1/view_preproduct/calculate_weight", {
      //   data_jsonb: this.table_list,
      // }).then((response) =>
      //   // (this.table_list = JSON.parse(response.data)),
      //   console.log(response)
      // );
      await HTTP_shwethe_n.get(
        "/api/v1/view_car_cost_form/car_cost_insert_form_by",
        {
          params: {
            ID: item.che_ci,
          },
        }
      ).then((response) => (this.table_list_2 = JSON.parse(response.data)));

      await HTTP_shwethe_n.get(
        "/api/v2/view_car_cost_form/view_car_cost_by_che_ci",
        {
          params: {
            che_ci: item.che_ci,
          },
        }
      ).then(
        (response) => (
          (this.car_cost_t = JSON.parse(response.data)),
          console.log(this.car_cost_t)
        )
      ).catch(function (error) {
    // handle error
    console.log(error);
  });

      this.overlay = false;
    },
    async api_process() {
      console.log(this.data_customer);
      console.log("test");
      await HTTP_shwethe_n.put("/api/v1/pre_order_n_compls", {
        bill_id: this._id,
        yi_fang: this.data_customer[0].id,
        data_: {
          status: "sucess",
        },
      })
        .then(function () {
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
        });
    },
    submitForm_2() {
      this.alert = true;
      this.dialog = false;
      console.log("BBBBBB");
      console.log(this.data_customer[0].yi_fang);
      if (this.data_customer) {
        this.alert = false;
        this.api_process();
      } else {
        this.alert = true;
      }
    },
    async submitForm() {
      const alasql = require("alasql");
      console.log(this.data_product[0]);
      var res4 = alasql(
        "SELECT id as product_id,qty as product_qty,price as product_price ,mm_name as product_mm_name,d_name as product_d_name ,idname as product_idname FROM ?",
        [this.data_product]
      );
      console.log(res4[0]);
      this.$refs.form.validate();
      console.log("test");
      console.log("test");
      await HTTP_shwethe_n.put("/api/v1/pre_order_n", {
        uid: 0,
        bill_id: this._id,
        yi_fang: 0,
        lei_b: 0,
        data_jsonb: [res4[0]],
      })
        .then(function () {
          // alert("SUCCESS!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.form.reset();
        });
      await HTTP_shwethe_n.get("/api/v1/pre_order_n", {
        params: {
          ID: this._id,
        },
      }).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          console.log(this.table_list)
        )
      );
    },
    async uppercase() {
      console.log(this.answer.sname);
      await HTTP_shwethe_n.get("/api/v1/product", {
        params: {
          ID: this.answer.sname,
        },
      }).then(
        (response) => (
          (this.data_product = JSON.parse(response.data)),
          console.log(this.data_product)
        )
      );
    },
    async uppercase_2() {
      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.answer_2.sname,
        },
      }).then(
        (response) => (this.data_customer = JSON.parse(response.data))
        // (console.log(this.data_customer[0].mm_name))
      );
      this.data_customer_mmname = null;
      if (this.data_customer == false) {
        console.log(1);
        this.data_customer_mmname = null;
      } else {
        console.log(2);
        this.data_customer_mmname = this.data_customer[0].mm_name;
      }
    },
    itemText(item) {
      return `${item.id} | ${item.idname} | ${item.mm_name}`;
    },
    sname_value(item) {
      console.log(this.item);
      return `${item.id}`;
    },
  },
  async created() {
    console.log(localStorage.getItem("uid"));
    var _id_ = Date.now().toString();
    this._id = _id_;
    console.log(this._id);
    await HTTP_shwethe_n.get(
      `/api/v2/view_car_cost_form/view_car_cost_form`
    ).then((response) => {
      this.table_list_1 = response.data;
    });
  },
  // /api/v1/view_preproduct/view_preproduct_detail
};
</script>
