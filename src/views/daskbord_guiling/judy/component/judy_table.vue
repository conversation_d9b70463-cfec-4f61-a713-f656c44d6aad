<template>
  <v-app>
    <!-- <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn> -->
    <v-card id="printMe">
      <v-card-title>
        Judy
        <v-spacer></v-spacer>
        <!-- <a :href="$router.resolve({ name: 'oil' }).href">
          <v-icon>refresh</v-icon></a
        > -->
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>
      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50],
        }"
        :search="search"
        :headers="headers"
        :items="desserts"
        :sort-by="['', '']"
        multi-sort
      >
        <template v-slot:item.actions="{ item }">
          <div class="my-2">
            <v-icon small @click="deleteItem(item)"> delete </v-icon>
          </div>
        </template>
      </v-data-table>
    </v-card>

    <template>
      <v-snackbar :color="snackbar.color" v-model="snackbar.show">
        {{ snackbar.message }}
      </v-snackbar>
    </template>
  </v-app>
</template>

<script>
import { HTTP_2 } from "../../../../plugins/http";

export default {
  data() {
    return {
      output: null,
      search: "",

      snackbar: {
        show: false,
        message: null,
        color: null,
      },

      headers: [
        { text: "d_name", value: "d_name" },
        { text: "idname_x", value: "idname_x" },
        { text: "mm_name", value: "mm_name" },
        { text: "price", value: "price" },
        { text: "qty", value: "qty" },
      ],
      desserts: [],

      //edit
      delete: {},
    };
  },
  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },

    // editItem and delete
    deleteItem(item) {
      this.delete = Object.assign({}, item);

      console.log(this.delete);
      console.log(this.delete.bill_id);
      // confirm("Are you sure you want to delete this item?") &&
      //   HTTP.post("oil_delete", { bill_id: this.delete.bill_id }).then(
      //     (response) =>
      //       console.log(response.data)
      //       // (this.desserts = response.data)
      //       //  this.columns = response.data.columns
      //   );
      confirm("Are you sure you want to delete this item?") &&
        HTTP_2.post("oil_delete", { bill_id: this.delete.bill_id }).then(
          (response) => {
            this.snackbar = {
              message: this.delete.mm_name_x,
              color: "warning",
              show: true,
            };
            console.log(response.data);
          }
        );
    },
  },
  async created() {
    var vvv = JSON.parse(localStorage.getItem("judy_car_item")); //forgot to close
    console.log(vvv);
    await HTTP_2.get("shwethe_duty/api/v1/duty_out_car_by", {
      params: {
        ID: vvv.car_bill_id,
      },
    }).then(
      (response) => (
        (this.desserts = JSON.parse(response.data)), console.log(this.desserts)
      )
    );
    try {
      this.desserts = this.desserts[0].data_detail_b;
      // this.desserts_2 = this.desserts[0].data_detail_b;
    } catch {
      this.desserts = []
    }
  },
};
</script>
