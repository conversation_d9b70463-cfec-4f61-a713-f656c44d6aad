<template>
  <v-app>
    <!-- <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn> -->
    <v-card id="printMe">
<!--      <v-card-title>-->
<!--        Oil-->
<!--        <v-spacer></v-spacer>-->
<!--        <a :href="$router.resolve({ name: 'oil' }).href">-->
<!--          <v-icon>refresh</v-icon>-->
<!--        </a-->
<!--        >-->
<!--        <v-spacer></v-spacer>-->
<!--        <v-text-field-->
<!--            v-model="search"-->
<!--            append-icon="mdi-magnify"-->
<!--            label="Search"-->
<!--            single-line-->
<!--            hide-details-->
<!--        ></v-text-field>-->
<!--      </v-card-title>-->
<!--      <v-data-table-->
<!--          :footer-props="{-->
<!--          'items-per-page-options': [10, 20, 30, 40, 50],-->
<!--        }"-->
<!--          :search="search"-->
<!--          :headers="headers"-->
<!--          :items="desserts"-->
<!--          :sort-by="['', '']"-->
<!--          multi-sort-->
<!--      >-->
<!--        <template v-slot:item.actions="{ item }">-->
<!--          <div class="my-2">-->
<!--            <v-icon small @click="deleteItem(item)">-->
<!--              delete-->
<!--            </v-icon>-->
<!--          </div>-->
<!--        </template>-->
<!--      </v-data-table>-->
      <div class="app_1">
        <v-card>
          <v-card-title>
            📒
            <v-spacer></v-spacer>
          </v-card-title>
          <v-data-table
              :headers="headers"
              :items="desserts"
          ></v-data-table>
        </v-card>
      </div>
    </v-card>

    <template>
<!--      <v-snackbar :color="snackbar.color" v-model="snackbar.show">-->
<!--        {{ snackbar.message }}-->
<!--      </v-snackbar>-->
    </template>
  </v-app>
</template>

<script>
import {HTTP} from "@/plugins/http";

export default {
  name: "fom_table_ochange",
  data() {
    return {

      headers: [
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "d_name", value: "d_name" },
        { text: "進價", value: "進價" },
        { text: "price", value: "price" },
        { text: "predicted", value: "predicted" },
        { text: "score", value: "score" },
      ],
      desserts: [
        {
          // id: "",
          idname: "",
          mm_name: "",
          d_name: "",
          fen_x: "",
          qty_x: "",
          want_qty: "",
          fen_y: "",
          qty_y: ""
        }
      ],
    };
  },
  created: function () {
    HTTP.get("shwethe_web_quaser/o_change").then(
        response =>
            // console.log(response.data),
            (this.desserts = response.data)
        //  this.columns = response.data.columns
    );
  }
}
</script>

<style scoped>

</style>
