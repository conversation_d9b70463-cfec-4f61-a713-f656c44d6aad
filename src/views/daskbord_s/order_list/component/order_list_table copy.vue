<template>
  <v-app>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            VIEW ORDER
            <v-spacer></v-spacer>
            <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
            ></v-text-field>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :search="search"
            :headers="headers"
            :items="desserts"
            multi-sort
          >
            <template v-slot:item.edit="{ item }">
              <v-btn small color="primary" @click="editItem(item)">View</v-btn>
            </template>

            <template v-slot:item.che_ci="{ item }">
              <v-chip :color="getColor(item.che_ci)" dark>
                {{ item.che_ci }}
              </v-chip>
            </template>

            <template v-slot:top>
              <v-card>
                <v-dialog v-model="dialog" max-width="500px">
                  <v-card>
                    <v-card-text>
                      <v-form
                        ref="form3"
                        v-model="valid_3"
                        @submit.prevent="submitForm"
                      >
                        <v-container>
                          <v-row>
                            <v-col cols="12">
                              <v-text-field
                                v-model="editedItem.idname"
                                label="Dessert name"
                                ref="editedItem_idname"
                                :rules="rules.lock"
                                @keyup="_uppercase"
                                @keyup.enter="uppercase"
                              ></v-text-field>
                            </v-col>
                            <v-col cols="12">
                              <v-text-field
                                label="Customer_mm_name"
                                v-model="data_customer_mmname"
                                :rules="rules.lock"
                                filled
                                dense
                                disabled
                              >
                              </v-text-field>
                            </v-col>
                            <v-col cols="12">
                              <v-btn
                                :disabled="!valid_3"
                                color="success"
                                class="mr-4"
                                type="submit"
                              >
                                add to list
                              </v-btn>
                            </v-col>
                          </v-row>
                        </v-container>
                      </v-form>
                    </v-card-text>
                  </v-card>
                </v-dialog>
              </v-card>
            </template>
            <template v-slot:item.actions="{ item }">
              <v-icon small class="mr-2" @click="editItem_(item)">
                mdi-pencil
              </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Oil
            <v-spacer></v-spacer>
            <v-form ref="form2" v-model="valid">
              <v-row>
                <v-col cols="6">
                  <v-autocomplete
                    label="Select"
                    :item-text="itemText"
                    :item-value="value"
                    :items="desserts_3"
                    v-model="answer.che_ci"
                    :rules="rules.lock"
                    filled
                    clearable
                  >
                  </v-autocomplete>
                </v-col>
                <v-col cols="6">
                  <v-btn
                    @click="submitOil"
                    :disabled="!valid"
                    color="success"
                    class="mr-4"
                  >
                    confirm
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
            <v-spacer></v-spacer>
            <!-- <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
            ></v-text-field> -->
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers_2"
            :items="desserts_2"
            multi-sort
          >
            <!-- <template v-slot:item.actions="{ item }">
              <div class="my-2">
                <v-icon small @click="deleteItem(item)"> delete </v-icon>
              </div>
            </template> -->
            <template v-slot:item.ans="{ item }">
              <v-chip :color="getColor_2(item.ans)" dark>
                {{ item.ans }}
              </v-chip>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
    <template>
      <v-snackbar :color="snackbar.color" v-model="snackbar.show">
        {{ snackbar.message }}
      </v-snackbar>
    </template>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>

    <template>
      <div id="receipt_pos">
        <v-btn @click="print" icon color="primary">
          <v-icon>print</v-icon>
        </v-btn>
        <!-- <button @click="this.window.location='../pos.html'">
            ook
        </button> -->
        <iframe
          ref="printIframe"
          frameborder="0"
          scrolling="no"
          style="
            margin: 0px;
            padding: 0px;
            width: 0px;
            height: 0px;
            size: auto;
            width=100% ;
            height=100%
          "
        ></iframe>
        // 这里是需要打印的内容，根据需求可以隐藏也可以显示
        <div
          class="margin"
          style="
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          <div
            class="border"
            style="
              margin: auto;
              padding: 1%;
              border: 1px solid black;
              margin-bottom: 20px;
            "
            id="table_L_N"
          >
            <div
              class="body_"
              id="table_L"
              style="width: 650px; background-color: white"
            >
              <h4 class="bill" style="margin: 0 0 1rem 30rem">
                {{ this.billlll }}
              </h4>
             

              <!-- <div
            class="recieve_header_a"
            style="
              margin-bottom: 1%;
              width: 600px;
              text-align: center;
              font-weight: bold;
            "
          >
            <h2 style="width: 20.7rem; margin-left: 2rem">ရွှေသဲ</h2>
          </div>

          <div
            class="recieve_header_b"
            style="
              margin: 0%;
              width: 550px;
              text-align: center;
              font-weight: bold;
            "
          >
            <h2 style="width: 20.7rem; margin-left: 2rem">အိမ်ဆောက်ပစ္စည်းရောင်း၀ယ်ရေး</h2>
          </div>

          <h4><b>Contact Info</b></h4>

          <label class="recieve_address" style="margin: 1% 0; font-size: 16px"
            >No6/199,Padauk street ,san sai(a)Ward,Tachileik Township</label
          ><br />
          <label class="recieve_address" style="margin: 1% 0; font-size: 16px"
            >(Th) 063-3733897,063-3733895 </label
          ><br />
          <label class="recieve_address" style="margin: 1% 0; font-size: 16px"
            >(Mm) 084-52241, 084-52027 , 09-5240209</label
          ><br />

          <br /> -->
              <h4><b>Time</b></h4>
               <label class="recieve_address" style="margin: 0 0 1rem 30rem">
                {{ this.bill_mm_name }}
              </label>
              <label
                class="recieve_address"
                style="margin: 1% 0; font-size: 16px"
                >{{ datetime }}</label
              >
              <label
                class="recieve_address"
                style="margin: 1% 0; font-size: 16px"
                >{{ datetime }}</label
              >

              <table class="recieve_content">
                <tr>
                  <th
                    style="
                      background-color: rgb(63, 58, 58);
                      color: #ffffff;
                      font-size: 100%;
                    "
                    width="20%"
                  >
                    idname
                  </th>
                  <th
                    style="
                      background-color: rgb(63, 58, 58);
                      color: #ffffff;
                      font-size: 100%;
                    "
                    width="40%"
                  >
                    mm_name
                  </th>
                  <th
                    style="
                      background-color: rgb(63, 58, 58);
                      color: #ffffff;
                      font-size: 100%;
                    "
                    width="20%"
                  >
                    d_name
                  </th>

                  <th
                    style="
                      background-color: rgb(63, 58, 58);
                      color: #ffffff;
                      font-size: 100%;
                    "
                    width="20%"
                  >
                    qty
                  </th>
                </tr>
                <hr />
                <tr v-for="dessert in desserts_2" :key="dessert.id">
                  <td style="border-bottom: 1px dotted #000; font-size: 100%">
                    {{ dessert.product_idname }}
                  </td>
                  <td style="border-bottom: 1px dotted #000; font-size: 100%">
                    {{ dessert.product_mm_name }}
                  </td>
                  <td style="border-bottom: 1px dotted #000; font-size: 100%">
                    {{ dessert.product_d_name }}
                  </td>
                  <td style="border-bottom: 1px dotted #000; font-size: 100%">
                    {{ dessert.product_qty_x }}
                  </td>
                </tr>
              </table>
              <!-- <div class="ReceiptFoote">
            <label>Total Price</label> &nbsp;&nbsp;
            <b>
              <label id="totalprice2"> {{ total_price.total }} </label> </b
            ><label> ฿ </label>
          </div> -->
            </div>
          </div>
        </div>
      </div>
    </template>
  </v-app>
</template>

<script>
import { HTTP_2 } from "../../../../plugins/http";

export default {
  data() {
    return {
      bill_mm_name : '',
      datetime: "",
      total_price: "",
      billlll: null,
      overlay: false,
      output: null,
      search: "",
      che_liang: null,
      che_liang_id: null,
      che_liang_mmname: null,
      valid: true,
      valid_2: true,
      valid_3: true,
      snackbar: {
        show: false,
        message: null,
        color: null,
      },
      dialog: false,
      headers: [
        // { text: "bill_id", value: "bill_id" },
        { text: "datetime", value: "datetime" },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "yi_fang_name", value: "yi_fang_name" },
        { text: "yi_fang_mm_name", value: "yi_fang_mm_name" },
        { text: "bill_id", value: "edit" },
        { text: "che_ci", value: "che_ci" },
        { text: "Actions", value: "actions", sortable: false },
      ],
      headers_2: [
        { text: "bill_id", value: "bill_id" },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "type_line", value: "type_line" },
        { text: "fen", value: "fen" },
        { text: "product_idname", value: "product_idname" },
        { text: "product_mm_name", value: "product_mm_name" },
        { text: "product_d_name", value: "product_d_name" },
        { text: "product_qty_x", value: "product_qty_x" },
        { text: "product_qty", value: "product_qty" },
        { text: "product_price", value: "product_price" },
        { text: "ans", value: "ans" },
      ],
      headers_3: [
        { text: "che_ci", value: "che_ci" },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "datetime", value: "datetime" },
      ],
      watch: {
        dialog(val) {
          val || this.close();
        },
        dialogDelete(val) {
          val || this.closeDelete();
        },
      },
      desserts_2: [],
      desserts: [],
      desserts_3: [],
      id_: null,
      editItem_1: [],

      //edit
      delete: {},
      rules: {
        lock: [(v) => !!v || "required"],
        price: [
          (v) => !!v || "required",
          (v) => v >= 0.1 || "required",
          (v) => !!v == Number.isInteger(v) || "Must be integer",
        ],
        qty: [
          (v) => !!v || "required",
          (v) => v >= 0.1 || "required",
          (v) => !!v == Number.isInteger(v) || "Must be integer",
        ],
        distant: [(v) => !!v || "required"],
      },
      // answer
      answer: {
        che_ci: "",
      },
      editedIndex: -1,
      editedItem: {
        idname: "",
        product_qty: "",
        qty_select: "",
      },
      data_customer_mmname: null,
      checK_idname: false,
      recourd_line: [],
    };
  },
  methods: {
    async submitForm() {
      console.log("ABC");
      console.log(this.data_product);
      await HTTP_2.put("shwethe_n/api/v1/order_list/order_list_cheliang", {
        bill_id: this.recourd_line.bill_id,
        che_ci: this.data_product[0].id,
        data_: {
          status: "sucess",
        },
      })
        .then(function () {
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
        });
    },
    _uppercase() {
      console.log("1");
      console.log();
      if (this.editedItem.idname.length == 1) {
        this.checK_idname = false;
        console.log(123);
      } else {
        if (this.checK_idname != false) {
          this.$refs["editedItem_idname"].reset();
          this.data_customer_mmname = null;
          this.checK_idname = false;
        } else {
          console.log("1");
          console.log(this.editedItem.idname);
          this.editedItem.idname = this.editedItem.idname.toUpperCase();
        }
      }
    },
    async uppercase() {
      console.log(this.editedItem.idname);
      this.checK_idname = true;

      console.log(this.answer.sname);
      await HTTP_2.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.editedItem.idname,
        },
      }).then((response) => (this.data_product = JSON.parse(response.data)));

      if (this.data_product == false) {
        this.data_customer_mmname = [];
        console.log("");
      } else {
        this.data_customer_mmname = this.data_product[0].mm_name;
        // this.$refs["Qty"].$refs.input.focus();
        console.log(this.data_product);
      }
    },
    itemText(item) {
      return `${item.che_ci} || ${item.idname} || ${item.mm_name}`;
    },
    value(item) {
      return `${item.che_ci}`;
    },

    getColor(che_ci) {
      if (che_ci == null) return "red";
      else if (che_ci != null) return "green";
    },
    getColor_2(ans) {
      if (ans > 0) return "red";
      else if (ans == 0) return "green";
    },
    editItem_(item) {
      this.checK_idname = false;
      this.data_customer_mmname = null;
      this.editedIndex = this.desserts.indexOf(item);
      this.editedItem = Object.assign({}, item);
      this.dialog = true;
      this.recourd_line = item;
      console.log(item);
      console.log(item.bill_id);
    },
    async submitCar() {
      this.overlay = true;
      console.log(this.che_liang_id);
      await HTTP_2.post(`shwethe_n/api/v1/order_list/add_checi`, {
        che_liang: this.che_liang_id,
      }).then((response) => {
        console.log(response);
      });
      location.reload();
    },
    async uppercase_5() {
      this.che_liang = this.che_liang.toUpperCase();
      console.log("AAAAAAAAAAAAA");
      await HTTP_2.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.che_liang,
        },
      }).then(
        (response) => (this.che_liang_mmname = JSON.parse(response.data))
      );
      if (this.che_liang_mmname == false) {
        console.log(1);
        this.che_liang_id = null;
        this.che_liang_mmname = null;
      } else {
        console.log(2);
        this.che_liang_id = this.che_liang_mmname[0].id;
        this.che_liang_mmname = this.che_liang_mmname[0].mm_name;
      }
    },
    async check_api_() {
      // console.log(this.answer.che_ci)
      await HTTP_2.put("shwethe_n/api/v1/order_list/order_list_compls", {
        bill_id: this.id_,
        che_ci: this.answer.che_ci,
        data_: {
          status: "sucess",
        },
      })
        .then(function () {
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
        });
    },
    async submitOil() {
      if (this.desserts_2 == false) {
        console.log("log");
        alert("no data");
      } else {
        this.overlay = true;
        await this.check_api_();
        console.log("go");
      }
    },

    async editItem(item) {
      this.overlay = true;
      this.id_ = item.bill_id;
      this.bill_mm_name = item.yi_fang_mm_name
      console.log(item);
      item;
      await HTTP_2.get("shwethe_n/api/v2/order_list/order_list_by", {
        params: {
          ID: item.bill_id,
        },
      }).then(
        (response) => (
          console.log(response.data),
          (this.desserts_2 = JSON.parse(response.data))
          //  this.columns = response.data.columns
        )
      );

      this.overlay = false;
      await HTTP_2.get("shwethe_n/api/v1/order_list/add_checi", {
        params: {
          ID: item.che_liang,
        },
      }).then(
        (response) => (
          console.log(response.data),
          (this.desserts_3 = JSON.parse(response.data))
          //  this.columns = response.data.columns
        )
      );
    },

    print() {
      console.log("AAAAAAAAA");
      // this.$htmlToPaper("table_L_N");
      // var frame = document.querySelector("printIframe");
      // frame.contentDocument.querySelector("header").remove();;
      // frame.contentDocument.querySelector("footer").remove();
      var printIframe = this.$refs.printIframe;
      // let newWindow = window.open("_blank"); //打开新窗口
      let codestr = document.getElementById("table_L").innerHTML;
      printIframe.setAttribute("srcdoc", codestr);
      printIframe.onload = function () {
        console.log(printIframe.contentWindow);
        // 去掉iframe里面的dom的body的padding margin的默认数值
        printIframe.contentWindow.document.body.style.padding = "0px";
        printIframe.contentWindow.document.body.style.margin = "0px";
        

        // 开始打印
        printIframe.contentWindow.focus();
        printIframe.contentWindow.print();
      };

  

      // newWindow.document.write(codestr); //向文档写入HTML表达式或者JavaScript代码
      // newWindow.document.close(); //关闭document的输出流, 显示选定的数据
      // newWindow.print();
    },
  },
  async created() {
    await HTTP_2.get("shwethe_n/api/v2/order_list/order_list").then(
      (response) => (
        console.log(response.data), (this.desserts = JSON.parse(response.data))
        //  this.columns = response.data.columns
      )
    );

  },
};
</script>
