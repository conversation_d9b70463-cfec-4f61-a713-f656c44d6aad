<template>
  <v-app class="app">
    <b-row>
      <b-col md="4" sm="12">
        <b-row>
          <b-col md="8" sm="12">
            <v-autocomplete
              v-if="row_type_judy == 'for_duty'"
              label="bill_id"
              :item-text="bill_id_list_json_itemText"
              :item-value="bill_id_list_json_itemText_value"
              :items="bill_id_list_json"
              v-model="bill_id_list_model"
              @keydown.enter="focusNext"
              ref="input-3"
              :rules="rules.lock"
              @change="on_select_T(bill_id_list_model)"
              clearable
            >
            </v-autocomplete>
          </b-col>
        </b-row>
        <v-form ref="form" v-model="valid" autocomplete="off">
          <v-autocomplete
            v-if="row_type_judy == 'for_duty'"
            label="bill_id"
            :item-text="product_select_itemText"
            :item-value="product_select_value"
            :items="product_select"
            v-model="product_id_list_model"
            @keydown.enter="focusNext"
            ref="input-3"
            :rules="rules.lock"
            @change="on_select_L(product_id_list_model)"
            clearable
          >
          </v-autocomplete>
          <v-text-field
            v-if="row_type_judy == 'for_duty_cost'"
            label="Name"
            @input="input_search"
            @keydown.enter="focusNext"
            ref="input-0"
            required
            :autofocus="autoFocus"
            :rules="rules.lock"
          ></v-text-field>
          <v-text-field
            v-if="row_type_judy == 'for_duty_cost'"
            label="product name"
            v-model="product_name"
            disabled
            required
            :rules="rules.lock"
          ></v-text-field>
          <v-text-field
            v-if="row_type_judy == 'for_duty_cost'"
            label="product dname"
            v-model="product_dname"
            disabled
          ></v-text-field>
          <v-text-field
            label="qty"
            v-model="product_qty"
            @keydown.enter="focusNext"
            ref="input-1"
            required
            :rules="rules.qty"
          ></v-text-field>
          <v-text-field
            label="price"
            v-model="product_price"
            required
            @keydown.enter="focusNext"
            ref="input-2"
            :rules="rules.price"
          ></v-text-field>
          <v-btn
            v-if="row_type_judy == 'for_duty'"
            :disabled="!valid"
            color="success"
            class="mr-4"
            @click="Summit"
          >
            Summit1
          </v-btn>

          <v-autocomplete
            v-if="row_type_judy == 'for_duty_cost'"
            label=""
            :item-text="bi_zhi_select_itemText"
            :item-value="bi_zhi_select_value"
            :items="bi_zhi"
            v-model="bi_zhi_id"
            @keydown.enter="focusNext"
            ref="input-3"
            :rules="rules.lock"
            clearable
          >
          </v-autocomplete>
          <v-btn
            v-if="row_type_judy == 'for_duty_cost'"
            :disabled="!valid"
            color="success"
            class="mr-4"
            @click="Summit2"
          >
            Summit2
          </v-btn>
        </v-form>
      </b-col>
      <b-col md="8" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Follow Items
            <v-spacer></v-spacer>
            <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
            ></v-text-field>
          </v-card-title>
          <template>
            <div class="text-center">
              <v-overlay :value="overlay">
                <v-progress-circular indeterminate size="64"></v-progress-circular>
              </v-overlay>
            </div>
          </template>
          <!-- <template>
            <div class="text-center">
              <v-progress-circular
                :rotate="360"
                :size="100"
                :width="15"
                :value="value"
                
                color="teal"
              >
                {{ value }}
              </v-progress-circular>
            </div>
          </template> -->
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :search="search"
            :headers="headers"
            :items="desserts"
            multi-sort
          >
            <template v-slot:top>
              <v-dialog v-model="dialog" hide-overlay max-width="30%">
                <v-card>
                  <v-card-title class="headline"></v-card-title>
                  <v-card-text>
                    <p>
                      <b>Edit Form</b>
                    </p>
                    <v-text-field
                      v-model="editedItem.product_qty"
                      label="product_qty"
                      :autofocus="autoFocus"
                    ></v-text-field>
                    <v-text-field
                      v-model="editedItem.product_price"
                      label="product_price"
                      disabled
                    ></v-text-field>
                    <v-text-field
                      v-model="editedItem.product_idname"
                      label="product_idname"
                      disabled
                    ></v-text-field>
                    <v-text-field
                      v-model="editedItem.product_mm_name"
                      label="product_mm_name"
                      disabled
                    ></v-text-field>
                    <v-text-field
                      v-model="editedItem.product_d_name"
                      label="product_d_name"
                      disabled
                    ></v-text-field>
                    <v-btn @click="update_item" color="success" class="mr-4">
                      Save
                    </v-btn>
                    <!-- <pre>{{ answer }}</pre> -->
                  </v-card-text>
                </v-card>
              </v-dialog>
            </template>
            <!--<template v-slot:top>
              <v-row justify="center">
                <v-dialog
                  v-model="dialog"
                  fullscreen
                  hide-overlay
                  transition="dialog-bottom-transition"
                >
                  <v-card>
                    <v-toolbar dark color="primary">
                      <v-btn icon dark @click="dialog = false">
                        <v-icon>mdi-close</v-icon>
                      </v-btn>
                      <v-toolbar-title>Settings</v-toolbar-title>
                      <v-spacer></v-spacer>
                      <v-toolbar-items>
                        <v-btn dark text @click="update_item()">Save</v-btn>
                      </v-toolbar-items>
                    </v-toolbar>
                    <v-container>
                      <v-row>
                        <v-col cols="12" sm="6" md="4">
                          <v-text-field
                            v-model="editedItem.product_qty"
                            label="product_qty"
                          ></v-text-field>
                          <v-text-field
                            v-model="editedItem.product_price"
                            label="product_price"
                          ></v-text-field>
                          <v-text-field
                            v-model="editedItem.product_idname"
                            label="product_idname"
                            disabled
                          ></v-text-field>
                          <v-text-field
                            v-model="editedItem.product_mm_name"
                            label="product_mm_name"
                            disabled
                          ></v-text-field>
                          <v-text-field
                            v-model="editedItem.product_d_name"
                            label="product_d_name"
                            disabled
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </v-container>
                  </v-card>
                </v-dialog>
              </v-row>
            </template>-->
            <template v-slot:item.actions="{ item }">
              <div class="my-2">
                <v-icon small @click="deleteItem(item)">delete</v-icon>
              </div>
            </template>
            <template v-slot:item.edit="{ item }">
              <v-btn small color="primary" @click="editItem(item)">Edit</v-btn>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
  </v-app>
</template>

<script>
import { mongodb_api, shwethe_order_in_fast_api } from "../../../../plugins/http";

export default {
  data: () => ({
    jin_huo_bian_judy: null,
    row_type_judy: null,
    ithem_json: null,
    product_select: [],
    registration_id: null,
    registration_selie: null,
    product_json: null,
    product_id_car_cost: null,
    // zzzzzzzzzzzzzzzzzzz
    parner_id: null,
    bill_price_ans: false,
    autoFocus: false,
    overlay: false,
    _id: null,
    dialog: false,
    order_s_insert_bill_id: "",
    descriptionLimit: 60,
    entries: [],
    type_list_json: [],
    product_qty: "",
    product_price: "",
    product_id_search: "",
    isLoading: false,
    product_dname: "",
    product_name: [],
    product_lei1: "",
    product_lei2: "",
    model: null,
    output: null,
    valid: false,
    search_input: "",
    search: "",
    items: [],
    item_lei: "",
    r_id: "",
    currentIndex: 0,
    before_cheange_qty: 0,
    lei_a: null,
    lei_b: null,
    jia_yi_name_a: null,
    jia_yi_name_b: null,
    rules: {
      lock: [(v) => !!v || "Field is required"],
      price: [(v) => !!v || "required", (v) => v > 0 || "must be less than 15"],
      qty: [
        (v) => !!v || "required",
        (v) => (v && v.length <= 6) || "must be less than 6",
      ],
      name: [
        (v) => !!v || "required",
        (v) => (v && v.length <= 6) || "must be less than 6",
      ],
    },
    bill_id_list_json: [],
    bill_id_list_model: null,
    product_id_list_model: null,
    jin_huo_bian: null,
    headers: [
      {
        text: "registration_id",
        value: "registration_id",
      },
      {
        text: "product_idname",
        value: "product_idname",
      },
      {
        text: "product_mm_name",
        value: "product_mm_name",
      },
      {
        text: "product_d_name",
        value: "product_d_name",
      },
      {
        text: "product_qty",
        value: "product_qty",
      },
      {
        text: "product_price",
        value: "product_price",
      },
      {
        text: "Edit",
        value: "edit",
      },
      {
        text: "Delete",
        value: "actions",
        sortable: false,
      },
    ],
    bi_zhi_id: null,
    bi_zhi: [
      {
        bi_zhi_id: 358,
        bi_zhi_idname: "Yuan",
      },
      {
        bi_zhi_id: 138,
        bi_zhi_idname: "Bath",
      },
      {
        bi_zhi_id: 139,
        bi_zhi_idname: "kyat",
      },
    ],
    Lei_type: [
      {
        lei_id: 42,
        lei_idname: "S",
      },
      {
        lei_id: 24,
        lei_idname: "L",
      },
    ],
    desserts: [
      {
        idname_x: "",
        mm_name_x: "",
        d_name: "",
      },
    ],
    editedItem: {
      product_qty: "",
      product_price: "",
      product_idname: "",
      product_mm_name: "",
      product_d_name: "",
      product_id: 0,
      r_id: "",
    },
    deletedItem: {
      product_qty: "",
      product_price: "",
      product_idname: "",
      product_mm_name: "",
      product_d_name: "",
      product_id: 0,
      r_id: "",
    },
    row_type: null,
    type_type: null,
    jia_yi_search: null,
    jia_yi_dname: null,
  }),
  methods: {
    async on_select_T(ithem) {
      this.product_id_list_model = null;
      if (typeof ithem !== "undefined") {
        console.log(ithem);
        this.ithem_json = JSON.parse(ithem);
        await shwethe_order_in_fast_api
          .get("/api/v1/order_in_judy/order_in_judy_in_list", {
            params: {
              registration_id: this.ithem_json.registration_id,
              selie: this.ithem_json.selie,
            },
          })
          .then((response) => {
            this.product_select = response.data;
            // alert("success!!");
            console.log(response.data);
          });
      } else {
        this.product_select = [];
        console.log(1);
      }
    },
    async on_select_L(ithem) {
      console.log("SSSSSSSSSSSSSSSSSSSS");
      console.log(ithem);
      this.product_json = JSON.parse(ithem);
      this.registration_id = this.product_json.registration_id;
      this.registration_selie = this.product_json.registration_selie;
      this.product_id = this.product_json.judy_in_id;
      await shwethe_order_in_fast_api
        .get("/api/v1/order_search/product_price", {
          params: {
            product_id: parseInt(this.product_json.judy_in_id),
          },
        })
        .then((response) => {
          this.product_price = response.data.price;
          console.log(response.data.price);
        });
    },
    async Summit() {
      this.currentIndex = 0;
      this.$refs.form.validate();
      console.log("Summit");
      console.log(this.order_s_insert_bill_id);
      console.log(this.product_id_search);
      console.log(parseFloat(this.product_qty));
      console.log(parseFloat(this.product_price));
      console.log(this.product_lei1.toString());
      this.overlay = true;
      await shwethe_order_in_fast_api
        .post("/api/v1/order_in_judy/order_in_judy_form", {
          jin_huo_bian: this.jin_huo_bian_judy,
          row_type: this.row_type_judy,
          data_jsonb: {
            registration_id: this.registration_id,
            registration_selie: this.registration_selie,
            product_id: Number(this.product_id),
            product_qty: parseFloat(this.product_qty),
            product_price: parseFloat(this.product_price),
            lei_type_id: 5,
            jia_yi_fang_a: 18228,
            jia_yi_fang_b: 18228,
          },
        })
        .then(function () {})
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // this.list_rerst();
        });

      await shwethe_order_in_fast_api
        .get("/api/v1/order_in_judy/order_in_judy_form", {
          params: {
            jin_huo_bian: this.jin_huo_bian_judy,
            row_type: this.row_type_judy,
          },
        })
        .then((response) => {
          this.desserts = response.data;
        });
      this.overlay = false;

      this.$refs.form.reset();
    },
    async Summit2() {
      this.currentIndex = 0;
      this.$refs.form.validate();
      console.log("Summit");
      console.log(this.order_s_insert_bill_id);
      console.log(this.product_id_search);
      console.log(parseFloat(this.product_qty));
      console.log(parseFloat(this.product_price));
      console.log(this.product_lei1.toString());
      this.overlay = true;
      await shwethe_order_in_fast_api
        .post("/api/v1/order_in_judy/order_in_judy_for_car_cost_form", {
          jin_huo_bian: this.jin_huo_bian_judy,
          row_type: this.row_type_judy,
          data_jsonb: {
            product_id: Number(this.product_id_search),
            product_qty: parseFloat(this.product_qty),
            product_price: parseFloat(this.product_price),
            lei_type_id: 6,
            bi_zhi: this.bi_zhi_id,
            jia_yi_fang_a: 18228,
            jia_yi_fang_b: 18228,
          },
        })
        .then(function () {
          alert("success!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // this.list_rerst();
          this.$refs.form.reset();
          this.overlay = false;
        });

      await shwethe_order_in_fast_api
        .get("/api/v1/order_in_judy/order_in_judy_form", {
          params: {
            jin_huo_bian: this.jin_huo_bian_judy,
            row_type: this.row_type_judy,
          },
        })
        .then((response) => {
          this.desserts = response.data;
        });
    },
    //Next TextBox Focus
    focusNext() {
      //console.log('event', event)
      this.currentIndex += 1;
      const nextElement = this.$refs[`input-${this.currentIndex}`];
      if (nextElement) nextElement.focus();
    },
    async input_search(val) {
      console.log(val.toUpperCase());

      await mongodb_api
        .get("/api/v1/search/product", {
          params: {
            ID: val.toUpperCase(),
          },
        })
        .then((response) => {
          console.log(JSON.parse(response.data));
          if (JSON.parse(response.data) != false) {
            this.model = JSON.parse(response.data);
            this.product_id_search = this.model[0].product_id;
            this.product_name = this.model[0].product_mm_name;
            this.product_dname = this.model[0].product_d_name;
            this.r_id = this.model[0].r_id;
          } else {
            this.product_id_search = 0;
            this.product_name = null;
            this.product_dname = null;
          }
        });
    },
    async list_rerst() {
      console.log("list_rerst");
      await shwethe_order_in_fast_api
        .get("/api/v1/order_s_insert/in_order/table_product_form/order_s_insert", {
          params: {
            list_id: this.order_s_insert_bill_id,
          },
        })
        .then((response) => {
          console.log(JSON.parse(response.data));
          this.desserts = JSON.parse(response.data);
        });
    },

    async Summit_2() {
      this.currentIndex = 0;
      this.$refs.form.validate();
      this.overlay = true;
      await shwethe_order_in_fast_api
        .post(
          "/api/v1/order_s_insert/in_order/table_product_form/order_s_insert_car_cost",
          {
            order_s_insert_bill_id: this.order_s_insert_bill_id,
            data_jsonb: {
              s_bill_id: this.bill_id_list_model,
              product_id: Number(this.product_id_search),
              product_qty: parseFloat(this.product_qty),
              product_price: parseFloat(this.product_price),
              lei_type_id: this.product_lei1.toString(),
              jia_yi_name_a: this.jia_yi_name_a,
              jia_yi_name_b: this.jia_yi_name_b,
            },
          }
        )
        .then(function () {
          alert("success!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          this.list_rerst();
          this.$refs.form.reset();
          this.overlay = false;
        });
    },
    input_search_jia_yi_name(val) {
      console.log(val.toUpperCase());
      // this.checK_idname = true;
      // this.answer.sname = val.toUpperCase();
      mongodb_api
        .get("/api/v1/search/jia_yi_name", {
          params: {
            ID: val.toUpperCase(),
          },
        })
        .then((response) => {
          console.log(JSON.parse(response.data));
          if (JSON.parse(response.data) != false) {
            this.model = JSON.parse(response.data);
            this.jia_yi_name_a = this.model[0].jia_yi_id;
            // this.product_name = this.model[0].product_mm_name;
            this.jia_yi_dname = this.model[0].jia_yi_mm_name;
            // this.r_id = this.model[0].r_id;
          } else {
            this.jia_yi_dname = null;
            this.jia_yi_name_a = null;
            console.log("");
          }
        });
    },
    on_select(ithem) {
      console.log(ithem);
      this.jia_yi_name_a = this.parner_id;
      this.jia_yi_name_b = this.parner_id;
      if (ithem == 3) {
        console.log(2);
        this.type_type = "L";
        this.jia_yi_name_a = null;
      } else {
        this.type_type = "";
        this.jia_yi_name_a = this.parner_id;
      }
    },
    async add_bill_id_id() {
      console.log("test");

      console.log(this.order_s_insert_bill_id);

      await shwethe_order_in_fast_api
        .post(
          "/api/v1/order_s_insert/in_order/table_product_form/order_s_insert_bill_id",
          {
            order_s_insert_bill_id: this.order_s_insert_bill_id,
            jin_huo_bian: this.jin_huo_bian,
          }
        )
        .then(function () {
          alert("success!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {});
      await shwethe_order_in_fast_api
        .get(
          "/api/v1/order_s_insert/in_order/table_product_form/order_s_insert_bill_id",
          {
            params: {
              jin_huo_bian: this.jin_huo_bian,
            },
          }
        )
        .then((response) => {
          this.bill_id_list_json = response.data;

          console.log(this.bill_id_list_json);
        });
    },
    bill_id_list_json_itemText(item) {
      return `${item.registration_id} | ${item.selie}`;
    },
    bill_id_list_json_itemText_value(item) {
      return `{"registration_id":${item.registration_id} , "selie":${item.selie}}`;
    },
    product_select_itemText(item) {
      return `${item.judy_in_idname} | ${item.judy_in_mmname}`;
    },
    product_select_value(item) {
      return `{"judy_in_id": ${item.judy_in_id} , "registration_selie": ${item.selie}, "registration_id": ${item.registration_id}}`;
    },
    bi_zhi_select_itemText(item) {
      return `${item.bi_zhi_idname}`;
    },
    bi_zhi_select_value(item) {
      return `${item.bi_zhi_id}`;
    },
    itemText(item) {
      return `${item.lei_type_id} | ${item.lei_type_name}`;
    },
    leivalue(item) {
      return `${item.lei_type_id}`;
    },
    leiText2(item) {
      return `${item.lei_id} | ${item.lei_idname}`;
    },
    leivalue2(item) {
      return `${item.lei_id}`;
    },
    editItem(item) {
      this.dialog = true;
      this.autoFocus = true;
      this.editedItem = Object.assign({}, item);
      console.log(this.editedItem);
      this.before_cheange_qty = this.editedItem.product_qty;
      // HTTP.post(`oa_list_for_insert`, {}).then((response) => {
      //   response;
      //   this.answer.sid = this.editedItem.id;
      // });
    },
    async deleteItem(item) {
      this.deletedItem = Object.assign({}, item);
      console.log(this.deletedItem);
      console.log(this.order_s_insert_bill_id);
      console.log(this.deletedItem.r_id);
      console.log(this.deletedItem.product_id);
      console.log(this.deletedItem.product_qty);

      await shwethe_order_in_fast_api
        .delete("/api/v1/order_in_judy/order_in_judy_form", {
          data: {
            jin_huo_bian: this.jin_huo_bian_judy,
            row_type: this.row_type_judy,
            data_jsonb: {
              r_id: this.deletedItem.r_id,
              product_id: this.deletedItem.product_id,
              before_delete_qty: this.deletedItem.product_qty,
            },
          },
        })
        .then(function () {
          alert("SUCCESS Delete!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          this.$refs.form.reset();
        });

      await shwethe_order_in_fast_api
        .get("/api/v1/order_in_judy/order_in_judy_form", {
          params: {
            jin_huo_bian: this.jin_huo_bian_judy,
            row_type: this.row_type_judy,
          },
        })
        .then((response) => {
          this.desserts = response.data;
        });
      // HTTP.post(`oa_list_for_insert`, {}).then((response) => {
      //   response;
      //   this.answer.sid = this.editedItem.id;
      // });
    },

    async update_item() {
      console.log(this.editedItem.r_id);
      console.log(this.order_s_insert_bill_id);
      console.log(this.editedItem.product_id);
      console.log(this.product_qty);
      console.log(this.editedItem.product_qty);
      console.log(this.editedItem.product_price);
      // console.log(parseFloat(this.product_qty));
      // console.log(parseFloat(this.product_price));
      // this.product_id_search = this.editedItem.product_id_search;
      // this.r_id = this.editedItem.r_id;
      // this.product_qty = this.editedItem.product_qty;
      // this.product_price = this.editedItem.product_price;

      await shwethe_order_in_fast_api
        .put("/api/v1/order_in_judy/order_in_judy_form", {
          jin_huo_bian: this.jin_huo_bian_judy,
          row_type: this.row_type_judy,
          data_jsonb: {
            r_id: this.editedItem.r_id,
            product_id: Number(this.editedItem.product_id),
            before_cheange_qty: parseFloat(this.before_cheange_qty),
            product_qty: parseFloat(this.editedItem.product_qty),
            product_price: parseFloat(this.editedItem.product_price),
          },
        })
        .then(function () {
          alert("SUCCESS UPDATE!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          this.$refs.form.reset();
          this.dialog = false;
        });

      await shwethe_order_in_fast_api
        .get("/api/v1/order_in_judy/order_in_judy_form", {
          params: {
            jin_huo_bian: this.jin_huo_bian_judy,
            row_type: this.row_type_judy,
          },
        })
        .then((response) => {
          this.desserts = response.data;
        });
    },
  },
  watch: {
    async search_input(val) {
      console.log(val);

      console.log(val);
      // Lazily load input items
      await mongodb_api
        .get("/api/v1/search/product", {
          params: {
            ID: val,
          },
        })
        .then((response) => {
          console.log(JSON.parse(response.data));
          if (JSON.parse(response.data) != false) {
            this.model = JSON.parse(response.data);
          } else {
            console.log("");
          }
        });
      this.isLoading = true;
    },
  },

  async created() {
    this.overlay = true;
    this.autoFocus = true;
    // this.interval = setInterval(() => {
    //     if (this.value === 100) {
    //       return (this.value = 0)
    //     }
    //     this.value += 10
    //   }, 1000)
    // if (this.value ==100){
    //   clearInterval(this.interval);
    // }
    var _id_ = Date.now().toString();
    this._id = _id_;
    // this.order_s_insert_bill_id = JSON.parse(localStorage.getItem("order_s_insert_bill_id"));
    this.jin_huo_bian_judy = localStorage.getItem("jin_huo_bian_judy");
    this.row_type_judy = localStorage.getItem("row_type_judy");
    console.log(this.jin_huo_bian_judy);
    console.log(this.row_type_judy);

    await shwethe_order_in_fast_api
      .post("/api/v1/order_in_judy/order_in_judy", {
        jin_huo_bian: this.jin_huo_bian_judy,
        row_type: this.row_type_judy,
      })
      .then(function () {
        // alert("success!!");
      })
      .catch(function () {
        alert("FAILURE!!");
      })
      .finally(() => {
        // this.list_rerst();
        // this.$refs.form.reset();
        this.overlay = false;
      });

    await mongodb_api
      .get("/api/v1/data/judy/judy_list")
      .then((response) => {
        // alert("success!!");
        console.log(response.data), (this.bill_id_list_json = response.data);
      })
      .catch(function () {
        alert("FAILURE!!");
      })
      .finally(() => {});

    await shwethe_order_in_fast_api
      .get("/api/v1/order_in_judy/order_in_judy_form", {
        params: {
          jin_huo_bian: this.jin_huo_bian_judy,
          row_type: this.row_type_judy,
        },
      })
      .then((response) => {
        this.desserts = response.data;
      });

    // await shwethe_order_in_fast_api
    //     .get(`/api/v1/order_s_insert/in_order/table_product_form/list_type`, {
    //         params: {
    //             row_type: this.row_type,
    //         },
    //     })
    //     .then((response) => {
    //         console.log(JSON.parse(response.data));
    //         (this.type_list_json = JSON.parse(response.data));

    //     });

    // await shwethe_order_in_fast_api
    //     .get("/api/v1/order_s_insert/in_order/table_product_form/order_s_insert", {
    //         params: {
    //             list_id: this.order_s_insert_bill_id,
    //         },
    //     })
    //     .then((response) => {

    //         this.desserts = JSON.parse(response.data);

    //         console.log(this.desserts);
    //     });
    // await shwethe_order_in_fast_api
    //     .get("/api/v1/order_s_insert/in_order/table_product_form/order_s_insert_bill_id", {
    //         params: {
    //             jin_huo_bian: this.jin_huo_bian,
    //         },
    //     })
    //     .then((response) => {

    //         this.bill_id_list_json = response.data;

    //         console.log(this.bill_id_list_json);
    //     });
    this.overlay = false;
  },
};
</script>
