<template>
  <v-app>
    <v-container>
      <v-btn @click="print" icon color="primary">
        <v-icon>print</v-icon>
      </v-btn>
      <v-card id="printMe">
        <v-card-title>
          Logistics
          <v-spacer></v-spacer>
          <v-text-field
            v-model="search"
            append-icon="mdi-magnify"
            label="Search"
            single-line
            hide-details
          ></v-text-field>
        </v-card-title>
        <v-data-table
          :footer-props="{
            'items-per-page-options': [10, 20, 30, 40, 50]
          }"
          :search="search"
          :headers="headers"
          :items="desserts"
          :sort-by="[]"
          multi-sort
        ></v-data-table>
      </v-card>
    </v-container>
  </v-app>
</template>

<script>
import { HTTP } from "../../plugins/http";

export default {
  data() {
    return {
      output: null,
      search: "",

      headers: [
        // { text: "id", value: "id" },
        { text: "name", value: "name" },
        { text: "price", value: "price" },
        { text: "count", value: "count" },
        { text: "PER", value: "PER" },
        // { text: "2", value: "2" },
        // { text: "1", value: "1" },
        // { text: "PER", value: "PER" },
      ],
   
      desserts: [
        {
          // id: "",
          name: "",
      price:"",
      count:"",
          PER: "",
        }
      ]
    };
  },
  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    }
  },
  created: function() {
    HTTP.get("set_list").then(
      response => (
        console.log(response.data),
        (this.desserts = response.data)
        //  this.columns = response.data.columns
      )
    );
  }
};
</script>
