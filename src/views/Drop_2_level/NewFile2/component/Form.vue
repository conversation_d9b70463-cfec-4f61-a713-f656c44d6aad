<template>
  <div>
    <v-row>
      <v-col cols="12">
        <v-form
          ref="form"
          v-model="valid"
          @submit.prevent="submitForm"
          autocomplete="off"
        >
          <v-card>
            <v-card-title class="headline">NewFile</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12">
                  <v-autocomplete
                    v-model="form.product"
                    :items="items4"
                    :search-input.sync="searchProduct"
                    :item-text="itemProduct"
                    :item-value="sname_value"
                    :rules="[rules.required]"
                    cache-items
                    hide-no-data
                    hide-details
                    label="Product"
                  ></v-autocomplete>
                </v-col>

                <v-col cols="12">
                  <v-card outlined>
                    <v-card-text>
                      <div v-for="(text, index) in form.data" :key="index">
                        <v-row>
                          <v-col cols="5">
                            <v-select
                              :items="shwetheOption"
                              item-text="shwethe"
                              item-value="shwetheValue"
                              v-model="text.fname"
                              label="Shwethe"
                              :rules="[rules.required]"
                            ></v-select>
                          </v-col>
                          <v-col cols="5">
                            <v-text-field
                              v-model="text.qty"
                              label="Qty"
                              type="number"
                              :rules="[rules.required]"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="2">
                            <v-btn
                              class="ma-2"
                              @click="deleteInput(index)"
                              tile
                              large
                              color="error"
                              icon
                            >
                              <v-icon>mdi-minus</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </div>

                      <v-btn
                        class="ma-2"
                        @click="addInput"
                        tile
                        large
                        color="teal"
                        icon
                      >
                        <v-icon>mdi-plus</v-icon>
                      </v-btn>
                    </v-card-text>
                  </v-card>
                </v-col>

                <v-col cols="6">
                  <v-autocomplete
                    v-model="form.shop"
                    :items="items"
                    :search-input.sync="searchShop"
                    :item-text="itemText"
                    :item-value="sname_value"
                    :rules="[rules.required]"
                    cache-items
                    hide-no-data
                    hide-details
                    label="Shop"
                  ></v-autocomplete>
                </v-col>

                <v-col cols="6">
                  <v-text-field
                    type="number"
                    v-model="form.price"
                    label="Price"
                    :rules="[rules.required]"
                  ></v-text-field>
                </v-col>

                <v-col cols="6">
                  <v-autocomplete
                    v-model="form.dataDetails.order"
                    :items="items2"
                    :search-input.sync="searchOrder"
                    :item-text="itemText"
                    :item-value="sname_value"
                    :rules="[rules.required]"
                    cache-items
                    hide-no-data
                    hide-details
                    label="Order by"
                  ></v-autocomplete>
                </v-col>

                <v-col cols="6">
                  <v-autocomplete
                    v-model="form.dataDetails.customer"
                    :items="items3"
                    :search-input.sync="searchCustomer"
                    :item-text="itemText"
                    :item-value="sname_value"
                    :rules="[rules.required]"
                    cache-items
                    hide-no-data
                    hide-details
                    label="Customer"
                  ></v-autocomplete>
                </v-col>
              </v-row>
            </v-card-text>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                :disabled="!valid"
                color="success"
                class="mr-4"
                type="submit"
                >Save</v-btn
              >
            </v-card-actions>
          </v-card>
        </v-form>
      </v-col>
      <!-- <pre> {{ form }}</pre> -->

      <template>
        <v-snackbar :color="snackbar.color" v-model="snackbar.show">
          {{ snackbar.message }}
        </v-snackbar>
      </template>

      <v-col cols="12">
        <template>
          <v-card id="printMe">
            <v-card-title>
              Table
              <v-spacer></v-spacer>
              <v-btn icon color="green" @click="table_refrest">
                <v-icon>mdi-cached</v-icon>
              </v-btn>
              <v-spacer></v-spacer>
              <v-text-field
                v-model="search"
                append-icon="mdi-magnify"
                label="Search"
                single-line
                hide-details
              ></v-text-field>
            </v-card-title>

            <v-data-table
              :footer-props="{
                'items-per-page-options': [10, 20, 30, 40, 50],
              }"
              loading-text="Loading... Please wait"
              :loading="loading"
              :search="search"
              :headers="headers2"
              :items="desserts2"
              :sort-by="['fen_x', 'want_qty']"
              multi-sort
            >
            </v-data-table>
          </v-card>
        </template>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import { HTTP } from "../../../../plugins/http";

export default {
  data() {
    return {
      valid: false,
      dialog: false,
      snackbar: {
        show: false,
        message: null,
        color: null,
      },
      // search
      searchShop: null,
      searchOrder: null,
      searchCustomer: null,
      searchProduct: null,
      items: [],
      items2: [],
      items3: [],
      items4: [],
      states: [],
      states2: [],
      states3: [],
      states4: [],
      // rules
      rules: {
        required: (value) => !!value || "Required.",
      },
      // form
      form: {
        dataDetails: {
          order: "",
          customer: "",
        },
        shop: "",
        product: "",
        price: "",
        data: [{ fname: "", qty: "" }],
      },

      // selecte option
      shwetheOption: [
        { shwethe: "Shwethe1", shwetheValue: "1" },
        { shwethe: "Shwethe2", shwetheValue: "2" },
      ],

      loading: true,
      search: "",

      headers2: [],
      desserts2: [],
      headers: [
        { text: "Edit", value: "actions", sortable: false },
        { text: "idname", value: "idname" },
        { text: "mm_name", value: "mm_name" },
        { text: "d_name", value: "d_name" },
        { text: "fen_x", value: "fen_x" },
        { text: "qty_x", value: "qty_x" },
        { text: "want_qty", value: "want_qty" },
        { text: "fen_y", value: "fen_y" },
        { text: "qty_y", value: "qty_y" },
        { text: "type", value: "type" },
      ],
      desserts: [],
    };
  },

  watch: {
    searchShop(val) {
      val && val !== this.form.shop && this.queryS_name(val);
    },
    searchOrder(val) {
      val && val !== this.form.dataDetails.order && this.queryJia_yi_name(val);
    },
    searchCustomer(val) {
      val &&
        val !== this.form.dataDetails.customer &&
        this.queryJia_yi_name_2(val);
    },
    searchProduct(val) {
      val && val !== this.form.product && this.queryProduct_name(val);
    },
  },

  created: function() {
    this.table_refrest();
  },

  methods: {
    // submitForm
    async submitForm() {
      this.$refs.form.validate();
      console.log(this.form);
      await HTTP.post(`order_for_order_insert`, this.form)
        .then((response) => {
          console.log(response);
          this.$refs.form.reset();
          this.snackbar = {
            message: "SAVE SUCCESS",
            color: "success",
            show: true,
          };
        })
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {});
    },
    // dynamic
    addInput() {
      this.form.data.push({ fname: "", qty: "" });
    },
    deleteInput(index) {
      if (index >= 1) {
        this.form.data.splice(index, 1);
      }
    },
    // item text value
    itemProduct(item) {
      return `${item.idname} | ${item.mm_name} | ${item.d_name}`;
    },
    itemText(item) {
      return `${item.idname} | ${item.mm_name}`;
    },
    sname_value(item) {
      return `${item.id}`;
    },
    // search
    async queryS_name(v) {
      // console.log(v);
      await HTTP.post(`seach/s_name`, { name: v }).then((response) => {
        console.log(response.data);
        this.states = response.data;
        this.items = this.states.filter((e) => {
          console.log(e.idname);
          return (
            (e.idname || "").toLowerCase().indexOf((v || "").toLowerCase()) > -1
          );
        });
      });
    },
    async queryJia_yi_name(v) {
      // console.log(v);
      await HTTP.post(`seach/jia_yi_name`, { name: v }).then((response) => {
        console.log(response.data);
        this.states2 = response.data;
        this.items2 = this.states2.filter((e) => {
          console.log(e.idname);
          return (
            (e.idname || "").toLowerCase().indexOf((v || "").toLowerCase()) > -1
          );
        });
      });
    },
    async queryJia_yi_name_2(v) {
      // console.log(v);
      await HTTP.post(`seach/jia_yi_name`, { name: v }).then((response) => {
        console.log(response.data);
        this.states3 = response.data;
        this.items3 = this.states3.filter((e) => {
          console.log(e.idname);
          return (
            (e.idname || "").toLowerCase().indexOf((v || "").toLowerCase()) > -1
          );
        });
      });
    },
    async queryProduct_name(v) {
      // console.log(v);
      await HTTP.post(`seach/product_name`, { name: v }).then((response) => {
        console.log(response.data);
        this.states4 = response.data;

        this.items4 = this.states4.filter((e) => {
          console.log(e.idname);
          return (
            (e.idname || "").toLowerCase().indexOf((v || "").toLowerCase()) > -1
          );
        });
      });
      console.log(this.form.product);
      this.table_price_refrdst();
    },
    async table_price_refrdst() {
      this.loading = true;
      await HTTP.post(`seach/s_price`, { id: this.form.product }).then(
        (response) => {
          console.log(response.data[0].qty_fen),
            (this.desserts2 = response.data[0].A1001),
            (this.headers2 = response.data[0].columns);
        }
      );
      this.loading = false;
    },
    async table_refrest() {
      this.loading = true;
      await HTTP.post(`order_for_order_list`, {}).then((response) => {
        console.log(response.data[0].qty_fen),
          (this.desserts = response.data[0].A1001),
          (this.headers = response.data[0].columns);
      });
      this.loading = false;
    },
  },
};
</script>
