<template>
  <v-app class="app">
    <v-dialog v-model="dialog_" max-width="500px">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </v-dialog>
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <v-card id="printMe">
      <v-card-title>
        Order Items G
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>
      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50],
        }"
        :search="search"
        :headers="filteredHeaders"
        :items="desserts"
        multi-sort
      >
        <template v-slot:top>
          <v-dialog
            v-model="dialog"
            fullscreen
            hide-overlay
            transition="dialog-bottom-transition"
          >
            <v-card>
              <v-toolbar dark color="warning">
                <v-btn icon dark @click="dialog = false">
                  <v-icon>mdi-close</v-icon>
                </v-btn>
                <v-toolbar-title>Order Items G</v-toolbar-title>
                <v-spacer></v-spacer>
              </v-toolbar>

              <div class="app_1">
                <v-card>
                  <v-card-title>
                    📒
                    <v-spacer></v-spacer>
                  </v-card-title>
                  <v-data-table :headers="headers" :items="desserts_2"></v-data-table>
                </v-card>
              </div>
              <div class="app_2">
                <b-row>
                  <b-col sm>
                    <v-card>
                      <v-card-title>
                        📒
                        <v-spacer></v-spacer>
                        <v-text-field
                          v-model="search_3"
                          append-icon="mdi-magnify"
                          label="Search"
                          single-line
                          hide-details
                        ></v-text-field>
                      </v-card-title>
                      <v-data-table
                        :headers="headers_3"
                        :items="desserts_3"
                        :search="search_3"
                      ></v-data-table>
                    </v-card>
                  </b-col>
                  <b-col>
                    <v-form ref="form" v-model="valid" @submit.prevent="submitForm">
                      <v-card>
                        <v-card-title> 📒 </v-card-title>
                        <v-row>
                          <v-col cols="8">
                            <v-text-field
                              v-model="min_product_qty"
                              type="number"
                              label="min_product_qty"
                              :counter="6"
                              filled
                              :rules="rules.price"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                        <v-container>
                          <v-btn
                            :disabled="!valid"
                            color="success"
                            class="mr-4"
                            type="submit"
                          >
                            Save
                          </v-btn>
                          <div v-for="(text, index) in answer.data" :key="index">
                            <v-row>
                              <v-col cols="5">
                                <v-select
                                  :items="shwetheOption"
                                  item-text="shwethe"
                                  item-value="shwetheValue"
                                  v-model="text.fname"
                                  filled
                                  label="Shwethe"
                                  :rules="rules.lock"
                                ></v-select>
                              </v-col>
                              <v-col cols="5">
                                <v-text-field
                                  v-model="text.qty"
                                  label="Qty"
                                  :counter="5"
                                  type="number"
                                  filled
                                  :rules="rules.qty"
                                ></v-text-field>
                              </v-col>
                              <v-col cols="2">
                                <v-btn
                                  @click="deleteInput(index)"
                                  class="mx-2"
                                  fab
                                  x-small
                                  dark
                                  color="error"
                                >
                                  <v-icon dark>mdi-minus</v-icon>
                                </v-btn>
                              </v-col>
                            </v-row>
                          </div>
                          <v-divider></v-divider>
                          <v-row>
                            <v-col cols="8">
                              <!-- Value: {{ answer.idname }} -->
                              <v-autocomplete
                                label="Shop"
                                :item-text="itemText"
                                :item-value="sname_value"
                                :items="product_ids"
                                v-model="answer.sname"
                                :rules="rules.lock"
                                filled
                                clearable
                              >
                              </v-autocomplete>
                            </v-col>
                            <v-col cols="2">
                              <v-text-field
                                v-model="answer.price"
                                type="number"
                                label="Price"
                                :counter="6"
                                filled
                                :rules="rules.price"
                              ></v-text-field>
                            </v-col>
                          </v-row>
                        </v-container>
                        <v-container>
                          <v-btn
                            @click="addInput"
                            class="mx-2"
                            fab
                            small
                            dark
                            color="warning"
                          >
                            <v-icon dark>mdi-plus</v-icon>
                          </v-btn>
                        </v-container>
                      </v-card>
                    </v-form>
                    <!-- <pre>{{ answer }}</pre> -->
                  </b-col>
                </b-row>
                <b-row>
                  <b-col cols="6">
                    <v-data-table
                      :headers="headers_4"
                      :items="desserts_4"
                      :items-per-page="5"
                      class="elevation-1"
                    ></v-data-table>
                  </b-col>
                </b-row>
              </div>
            </v-card>
          </v-dialog>
        </template>

        <template v-slot:item.edit="{ item }">
          <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
        </template>
        <template v-slot:item.delete="{ item }">
          <v-btn small color="error" @click="delete_editItem(item)">delete</v-btn>
        </template>
      </v-data-table>
    </v-card>
  </v-app>
</template>

<script>
import { HTTP, shwethe_order_in_fast_api } from "../../../../plugins/http";

export default {
  data: () => ({
    valid: false,
    dialog: false,
    min_product_qty: null,
    product_ids: [
      // { nern: "a", test: "11", test222: "q" },
    ],

    // selecte option
    shwetheOption: [
      { shwethe: "Shwethe1", shwetheValue: "1" },
      { shwethe: "Shwethe2", shwetheValue: "2" },
    ],

    // require
    rules: {
      lock: [(v) => !!v || "Field is required"],
      price: [
        (v) => !!v || "required",
        (v) => (v && v.length <= 6) || "must be less than 6",
      ],
      qty: [
        (v) => !!v || "required",
        (v) => (v && v.length <= 5) || "must be less than 5",
      ],
    },
    dialog_: false,
    // answer
    answer: {
      sname: "",
      idname: "",
      price: "",
      data: [{ fname: "", qty: "" }],
    },

    // Table
    search: "",
    search_3: "",
    headers: [],
    headers_3: [
      { text: "日期", value: "日期" },
      { text: "idname", value: "idname" },
      { text: "mm_name", value: "mm_name" },
      { text: "進價", value: "進價" },
    ],
    headers_4: [
      { text: "fen", value: "fen" },
      { text: "qty", value: "qty" },
    ],
    desserts: [],
    desserts_2: [],
    desserts_3: [],
    // desserts_4: [
    //   {
    //     name: "Frozen Yogurt",
    //     calories: 159,
    //     fat: 6.0,
    //     carbs: 24,
    //     protein: 4.0,
    //     iron: "1%",
    //   },
    //   {
    //     name: "Ice cream sandwich",
    //     calories: 237,
    //     fat: 9.0,
    //     carbs: 37,
    //     protein: 4.3,
    //     iron: "1%",
    //   },
    // ],
    columnsToDrop: [
      "parner_mm_name",
      "parner_id",
      "parner_idname",
      "FK",
      "min qty",
      "max qty ordered",
      "fixed day",
    ], // Specify columns to drop here
    desserts_4: [],

    // Match id
    editedItem: {
      idname: "",
    },
  }),
  computed: {
    filteredHeaders() {
      return this.headers.filter((header) => !this.columnsToDrop.includes(header.value));
    },
    filteredDesserts() {
      return this.desserts.map((item) => {
        let filteredItem = {};
        for (let key in item) {
          if (!this.columnsToDrop.includes(key)) {
            filteredItem[key] = item[key];
          }
        }
        return filteredItem;
      });
    },
  },
  methods: {
    print() {
      this.$htmlToPaper("printMe");
    },
    async delete_editItem(item) {
      console.log(item);
      this.dialog_ = true;
      await HTTP.post(`not_order`, {
        item,
      }).then((response) => {
        console.log(response);
      });

      await HTTP.get(`order_item_list_columns_G`).then((response) => {
        this.headers = response.data;
      });
      // await HTTP.get(`order_item_list_G`).then((response) => {
      //   console.log(response.data), (this.desserts = response.data);
      // });
      shwethe_order_in_fast_api
        .get(`api/v1/orderGoods/orderGoodsIthem`, {
          params: {
            type: "G",
          },
        })
        .then((response) => {
          console.log(response.data);
          this.desserts = response.data;
        });
      this.dialog_ = false;
    },
    editItem(item) {
      this.dialog = true;
      this.editedItem = Object.assign({}, item);

      HTTP.post(`order_item_by_product_id`, {
        idname: this.editedItem.idname,
      }).then((response) => {
        console.log(response.data[0].qty_fen),
          // console.log(response.data.length, "response.data.length");
          // console.log(response.data, "response.data"),
          // console.log(response.data[0].A1001, "response.data[0].A1001"),
          // console.log(response.data[0].A1001[0].id, "response.data[0].A2001"),
          (this.answer.idname = response.data[0].A1001[0].id),
          (this.desserts_2 = response.data[0].A1001),
          (this.desserts_3 = response.data[0].A2001),
          (this.desserts_4 = response.data[0].qty_fen),
          (this.product_ids = response.data[0].s_name);
      });
    },
    addInput() {
      this.answer.data.push({ fname: "", qty: "" });
    },
    deleteInput(index) {
      this.answer.data.splice(index, 1);
    },
    submitForm() {
      this.$refs.form.validate();
      // console.log(this.answer)

      shwethe_order_in_fast_api
        .post("/api/v1/info/info_data", {
          product_id: this.answer.idname,
          data_jsonb_1: { min_product_qty_order: parseFloat(this.min_product_qty) },
        })
        .then(function () {
          alert("SUCCESS!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {});

      HTTP.post("/order_insert", this.answer)
        .then(function () {
          alert("SUCCESS!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.form.reset();
          this.dialog = false;
        });
    },

    itemText(item) {
      return `${item.id} || ${item.idname} || ${item.mm_name}`;
    },
    sname_value(item) {
      return `${item.id}`;
    },
  },
  created() {
    HTTP.get(`order_item_list_columns_G`).then((response) => {
      this.headers = response.data;
    });
    // HTTP.get(`order_item_list_G`).then((response) => {
    //   console.log(response.data), (this.desserts = response.data);
    // });
    shwethe_order_in_fast_api
      .get(`api/v1/orderGoods/orderGoodsIthem`, {
        params: {
          type: "G",
        },
      })
      .then((response) => {
        console.log(response.data);
        this.desserts = response.data;
      });
  },
};
</script>

<style lang="scss" scoped>
// .app {
//   margin: 5% 5%;
// }
// .app_1 {
//   margin: 3% 5%;
// }
// .app_2 {
//   margin: 0 5%;
// }

// @media only screen and (max-width: 1832px) {
//   .app {
//     margin: 5% 1%;
//   }
// }
</style>
