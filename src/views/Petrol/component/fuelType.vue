<template>
  <!-- <v-app class="app"> -->
  <!-- <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn> -->
  <v-card id="printMe">
    <v-card-title>
      Fuel type
      <v-spacer></v-spacer>
      <v-text-field
        v-model="search"
        append-icon="mdi-magnify"
        label="Search"
        single-line
        hide-details
      ></v-text-field>
    </v-card-title>
    <v-data-table
      :search="search"
      :headers="headers"
      :items="desserts"
      disable-pagination
      multi-sort
    >
    </v-data-table>
  </v-card>
  <!-- </v-app> -->
</template>

<script>
import { PetrolProduct } from "../../../plugins/http";

export default {
  data: () => ({
    valid: false,
    dialog: false,

    // Table
    search: "",
    headers: [
      { text: "petrol", value: "petrol" },
      { text: "petrol_price", value: "petrol_price" },
      { text: "price", value: "price" },
      { text: "product_id", value: "product_id" },
      { text: "datetime", value: "datetime" },
      { text: "petrol_id", value: "petrol_id" },
    ],

    desserts: [],
  }),
  methods: {},
  created() {
    PetrolProduct.get(`getFuelPrice`).then((response) => {
      console.log(response.data, "sssssssssssssssss"),
        (this.desserts = response.data);
    });
  },
};
</script>

<style lang="scss" scoped>
// .app {
//   margin: 5% 5%;
// }
// .app_1 {
//   margin: 3% 5%;
// }
// .app_2 {
//   margin: 0 5%;
// }

// @media only screen and (max-width: 1832px) {
//   .app {
//     margin: 5% 1%;
//   }
// }
</style>
