{"name": "vuetify_web", "version": "0.1.9", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"alasql": "^0.6.5", "ant-design-vue": "^1.6.1", "axios": "^0.19.2", "bootstrap": "^4.5.0", "bootstrap-vue": "^2.15.0", "core-js": "^3.6.4", "date-fns": "^2.17.0", "echarts": "^4.8.0", "electron": "^12.0.1", "image-to-base64": "^2.1.0", "jwt-decode": "^3.1.2", "node-thermal-printer": "^4.1.2", "v-charts": "^1.19.0", "vee-validate": "^3.3.2", "vue": "^2.6.11", "vue-html-to-paper": "^1.3.1", "vue-router": "^3.1.6", "vuetify": "^2.2.11"}, "devDependencies": {"@babel/eslint-parser": "^7.17.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-prettier": "^6.0.0", "assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "eslint": "^8.57.1", "eslint-plugin-prettier": "^4.2.5", "eslint-plugin-vue": "^9.33.0", "material-design-icons-iconfont": "^5.0.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "prettier": "^1.19.1", "process": "^0.11.10", "querystring-es3": "^0.2.1", "sass": "^1.32.13", "sass-loader": "^8.0.0", "stream-browserify": "^3.0.0", "util": "^0.12.5", "vm-browserify": "^1.1.2", "vue-cli-plugin-vuetify": "~2.0.5", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/prettier"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false, "allowImportExportEverywhere": true}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}