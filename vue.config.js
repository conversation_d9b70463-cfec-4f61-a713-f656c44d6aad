// module.exports = {
//   transpileDependencies: ["vuetify"]
// };
module.exports = {
  transpileDependencies: ["vuetify"],
  pluginOptions: {
    electronBuilder: {
      nodeIntegration: true,
    }
  },
  configureWebpack: {
    resolve: {
      fallback: {
        // 禁用 Node.js 核心模块
        "fs": false,
        "net": false,
        "tls": false,
        "child_process": false,
        // 提供必要的浏览器 polyfills
        "util": require.resolve("util"),
        "stream": require.resolve("stream-browserify"),
        "zlib": require.resolve("browserify-zlib"),
        "assert": require.resolve("assert"),
        "buffer": require.resolve("buffer"),
        "process": require.resolve("process/browser")
      }
    },
    plugins: [
      new (require('webpack')).ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer'],
      }),
    ],
    externals: {
      // 排除服务器端特定的包
      'node-thermal-printer': 'window',
    }
  }
}