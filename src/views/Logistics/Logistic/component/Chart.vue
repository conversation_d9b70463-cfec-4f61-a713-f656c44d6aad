<template>
  <div class="chart">
    <ve-line :data="chartData" :settings="chartSettings"></ve-line>
  </div>
</template>

<script>
import { HTTP } from "../../../../plugins/http";

const columns = [
  {
    title: "id",
    dataIndex: "id",
    // width: "15%",
    scopedSlots: { customRender: "id" },
  },
  {
    title: "idname",
    dataIndex: "idname",
    // width: "15%",
    scopedSlots: { customRender: "idname" },
  },
  {
    title: "mm_name",
    dataIndex: "mm_name",
    // width: "40%",
    scopedSlots: { customRender: "mm_name" },
  },
  {
    title: "d_name",
    dataIndex: "d_name",
    // width: "40%",
    scopedSlots: { customRender: "d_name" },
  },
  {
    title: "fen",
    dataIndex: "fen_x",
    // width: "15%",
    scopedSlots: { customRender: "fen_x" },
  },
  {
    title: "qty_x",
    dataIndex: "qty_x",
    // width: "40%",
    scopedSlots: { customRender: "qty_x" },
  },
  {
    title: "want_qty",
    dataIndex: "want_qty",
    // width: "40%",
    scopedSlots: { customRender: "want_qty" },
  },
  {
    title: "fen_y",
    dataIndex: "fen_y",
    // width: "40%",
    scopedSlots: { customRender: "fen_y" },
  },
  {
    title: "qty_y",
    dataIndex: "qty_y",
    // width: "40%",
    scopedSlots: { customRender: "qty_y" },
  },
];
const data = [];

export default {
  data() {
    this.chartSettings = {
      stack: { 用户: ["访问用户", "下单用户"] },
      area: true,
    };
    return {
      data,
      columns,

      chartData: {
        columns: [],
        rows: [],
      },
    };
  },
  created: function() {
    let data22 = { orient: "records" };
    HTTP.post("logistics_data_count", data22).then(
      (response) => (
        console.log(response.data),
        (this.chartData.rows = response.data)
        //  this.columns = response.data.columns
      )
    );
  },
  mounted() {
    this.fillData();
  },
  methods: {
    fillData() {
      let data222 = { orient: "split" };
      HTTP
        .post("logistics_data_count", data222)
        .then(
          (response) => (
            console.log(response.data["columns"]),
            (this.chartData.columns = response.data["columns"])
          )
        );
    },
  },
};
</script>

