<template>
  <v-app class="app">
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>

    

    <v-card id="printMe">
      <v-card-title>
        Follow Items
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>

      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50],
        }"
        :search="search"
        :headers="headers"
        :items="desserts"
        multi-sort
      >
        <template v-slot:item.actions="{ item }">
          <div class="my-2">
            <v-icon small @click="deleteItem(item)">delete</v-icon>
          </div>
        </template>
        


        <template v-slot:item.actions2="{ item }">
          <v-btn
                color="red lighten-2"
                dark
                @click="editDatetime(item)"
              >
                edit
                
              </v-btn>
        </template>
        <template v-slot:item.actions3="{ item }">
          <!-- <template v-slot:activator="{ on, attrs }"> -->
              <v-btn
                color="red lighten-2"
                dark
                @click="editDatetime(item)"
              >
                datetime
                
              </v-btn>
          <!-- </template> -->
        </template>
        
      </v-data-table>
      <template>
        <v-dialog
            v-model="dialog"
            width="500"
            >
            <!-- <template v-slot:activator="{ on, attrs }">
              <v-btn
                color="red lighten-2"
                dark
                v-bind="attrs"
                v-on="on"
              >
                Edit
              </v-btn>
            </template> -->
            <v-card>
              <v-card-title class="text-h5 grey lighten-2">
                {{item}}
              </v-card-title>
              <v-container>
                <v-row>
                  <v-col>
                    <v-text-field
                        label="prices"
                        v-model="item.price"
                      ></v-text-field>
                  </v-col>
                </v-row>
              </v-container>
              <v-divider></v-divider>
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                  color="primary"
                  text
                  @click="dialog = false"
                >
                  I accept
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
      </template>
      <template>
        <v-dialog
            v-model="dialogDatetime"
            width="500"
            >
            <v-card>
              <v-card-title class="text-h5 grey lighten-2">
                {{item}}
              </v-card-title>
              <v-container>
                <v-row>
                  <v-col>
                    <v-row justify="center">
                      <v-date-picker
                        v-model="picker"
                        class="mt-4"
                        :min="toDate"
                        flat
                      ></v-date-picker>
                    </v-row>
                  </v-col>
                </v-row>
              </v-container>
              <v-divider></v-divider>
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                  color="primary"
                  text
                  @click="dialogDatetime = false"
                >
                  I accept
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
      </template>
    </v-card>


  </v-app>
</template>

<script>
import { HTTP } from "../../../plugins/http";

export default {
  data() {
    return {
      item:{},
      dialog:false,
      output: null,
      search: "",
      dialogDatetime:false,
      headers: [
        // { text: "id", value: "id" },
        { text: "รหัสสินค้า", value: "actions3" },
        { text: "รหัสสินค้า", value: "actions2" },
        { text: "รหัสสินค้า", value: "idname_x" },
        { text: "รายการสินค้า(M)", value: "mm_name_x" },
        { text: "รายการสินค้า(T)", value: "th_name" },
        { text: "ขนาด/ยี่ห่อ", value: "d_name" },
        { text: "ร้านใน", value: "1" },
        { text: "ร้านนอก", value: "2" },
        { text: "จำนวนรวม", value: "qty_x" },
        { text: "ราคาต่อหน่วย", value: "price" },
        { text: "เวลา/วันที่", value: "time" },
        { text: "เปอร์เซ็นต์", value: "PER" },
        { text: "รหัสประจำร้าน", value: "idname_y" },
        { text: "ชื่อร้าน", value: "mm_name_y" },
        { text: "แก้ไข", value: "actions", sortable: false },
      ],
      desserts: [
        {
          // id: "",
          idname_x: "",
          mm_name_x: "",
          d_name: "",
          th_name: "",
          time: "",
          qty_x: "",
          price: "",
          PER: "",
          idname_y: "",
          mm_name_y: "",
        },
      ],
      toDate:null,
      picker:(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),

      //edit
      delete: {},
    };
  },
  methods: {
    // allowedDates: val => parseInt(val.split('-')[2], 10) % 2 === 0,
    editDatetime(ithem){
      console.log(ithem)
      this.dialogDatetime = true
    },
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },

    // editItem and delete
    deleteItem(item) {
      this.delete = Object.assign({}, item);
      console.log(this.delete.iid, "dd");

      // const index = this.desserts.indexOf(item);
      confirm("Are you sure you want to delete this item?") &&
        HTTP.post(`order_list_delete`, { delete_id: this.delete.iid }).then(
          (response) => {
            // this.desserts = response.data;
            console.log(response);
          }
        );
      HTTP.get(`order_list`).then((response) => {
        this.desserts = response.data;
      });
    },
  },
  created() {
    HTTP.get(`order_list`).then((response) => {
      this.desserts = response.data;
    });

    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
    var yyyy = today.getFullYear();

    today = yyyy + '-' + mm + '-' + dd;


    this.toDate = today
  },
};
</script>
