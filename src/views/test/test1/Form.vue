<template>
  <v-app>
    <div class="myDiv">
      <div class="bg"></div>
      <b-container>
        <!-- <b-row>
          <b-col sm>
            <label
              >အဆင့်မြင့်ပညာရေးအဆင့်:<font color="red"
                >‎‎‏‏‎ ‎‏‏‎ ‎*</font
              ></label
            >
            <b-form-select required v-model="answer.education">
              <option value="" selected disabled>Choose</option>
              <option>အထက်တန်းကျောင်း</option>
              <option>တက္ကသိုလ်</option>
            </b-form-select>
          </b-col>
          <b-col>
            <label
              >ကောလိပ်အမည် / ကျောင်းအမည်:<font color="red"
                >‎‎‏‏‎ ‎‏‏‎ ‎*</font
              ></label
            >
            <b-form-input
              v-model="answer.schoolname"
              required
              autocomplete="off"
            ></b-form-input>
          </b-col>

          <div class="w-100"></div>

          <b-col sm>
            <label>ပညာရေးအဆင့်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label>
            <b-form-input
              v-model="answer.level"
              required
              autocomplete="off"
            ></b-form-input>
          </b-col>
          <b-col>
            <b-form-group label="မေဂျာ :">
              <b-form-input
                v-model="answer.branch"
                autocomplete="off"
              ></b-form-input>
            </b-form-group>
          </b-col>

          <div class="w-100"></div>

          <b-col sm>
            <b-form-group label="အမှတ်စာရင်း :">
              <b-form-input
                v-model="answer.gpa"
                autocomplete="off"
              ></b-form-input>
            </b-form-group>
          </b-col>
        </b-row> -->

        <b-form @submit="onSubmit" id="todo-field" v-if="show" ref="form">
          <b-row>
            <b-col cols="12">
              <label
                >အဆင့်မြင့်ပညာရေးအဆင့်:<font color="red"
                  >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                ></label
              >
              <b-form-select required v-model="answer.education">
                <option value="" selected disabled>Choose</option>
                <option
                  v-for="education in educations"
                  v-bind:value="education.id"
                  v-bind:key="education.value"
                  >{{ education.value }}</option
                >
              </b-form-select>
            </b-col>

            <template v-if="answer.education === 1">
              <b-col md="6">
                <label
                  >ကောလိပ်အမည် / ကျောင်းအမည်:<font color="red"
                    >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                  ></label
                >
                <b-form-input
                  v-model="answer.schoolname"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <label
                  >ပညာရေးအဆင့်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
                >
                <b-form-input
                  v-model="answer.level"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="12">
                <b-form-group label="အမှတ်စာရင်း :">
                  <b-form-input
                    v-model="answer.gpa"
                    autocomplete="off"
                  ></b-form-input>
                </b-form-group>
              </b-col>

              <b-col md="6">
                <label
                  >အနာဂတ်မှာလေ့လာသင်ယူရန်ရှိပါသလား:<font color="red"
                    >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                  ></label
                >
                <b-form-select required v-model="answer.study_future">
                  <option value="" selected disabled>Choose</option>
                  <option
                    v-for="study_future in study_futures"
                    v-bind:value="study_future.id"
                    v-bind:key="study_future.value"
                    >{{ study_future.value }}</option
                  >
                </b-form-select>
              </b-col>
              <template v-if="answer.study_future === 1">
                <b-col>
                  <label
                    >သွားမည့်ရက်ကိုဖြည့်စွပ်ပါ:<font color="red"
                      >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                    ></label
                  >
                  <b-form-input
                    type="date"
                    v-model="answer.study_plan"
                    required
                  ></b-form-input>
                </b-col>
              </template>
            </template>

            <template v-if="answer.education === 2">
              <b-col md="6">
                <label
                  >ကောလိပ်အမည် / ကျောင်းအမည်:<font color="red"
                    >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                  ></label
                >
                <b-form-input
                  v-model="answer.schoolname"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <label
                  >ပညာရေးအဆင့်:<font color="red">‎‎‏‏‎ ‎‏‏‎ ‎*</font></label
                >
                <b-form-input
                  v-model="answer.level"
                  required
                  autocomplete="off"
                ></b-form-input>
              </b-col>

              <b-col md="6">
                <b-form-group label="မေဂျာ :">
                  <b-form-input
                    v-model="answer.branch"
                    autocomplete="off"
                  ></b-form-input>
                </b-form-group>
              </b-col>

              <b-col md="6">
                <b-form-group label="အမှတ်စာရင်း :">
                  <b-form-input
                    v-model="answer.gpa"
                    autocomplete="off"
                  ></b-form-input>
                </b-form-group>
              </b-col>

              <b-col md="6">
                <label
                  >အနာဂတ်မှာလေ့လာသင်ယူရန်ရှိပါသလား:<font color="red"
                    >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                  ></label
                >
                <b-form-select required v-model="answer.study_future">
                  <option value="" selected disabled>Choose</option>
                  <option
                    v-for="study_future in study_futures"
                    v-bind:value="study_future.id"
                    v-bind:key="study_future.value"
                    >{{ study_future.value }}</option
                  >
                </b-form-select>
              </b-col>
              <template v-if="answer.study_future === 1">
                <b-col>
                  <label
                    >သွားမည့်ရက်ကိုဖြည့်စွပ်ပါ:<font color="red"
                      >‎‎‏‏‎ ‎‏‏‎ ‎*</font
                    ></label
                  >
                  <b-form-input
                    type="date"
                    v-model="answer.study_plan"
                    required
                  ></b-form-input>
                </b-col>
              </template>
            </template>
          </b-row>

          <v-btn
            large
            type="submit"
            block
            color="primary"
            :loading="loading"
            :disabled="loading"
            @click="loader = 'loading'"
            >Submit</v-btn
          >
        </b-form>
      </b-container>
    </div>
  </v-app>
</template>

<script>
import { HTTP } from "../../../plugins/http";
export default {
  data: () => ({
    show: true,

    loader: null,
    loading: false,

    // vuetify
    educations: [
      { id: 1, value: "အထက်တန်းကျောင်း" },
      { id: 2, value: "တက္ကသိုလ်" },
    ],
    study_futures: [
      { id: 1, value: "ရှိ" },
      { id: 2, value: "မရှိ" },
    ],
    languages: ["တရုတ်", "ထိုင်း", "ရှမ်း", "အာခါ", "အင်္ဂလိပ်"],
    KnowShwethe: [
      "လက်ကမ်းစာစောင်များ",
      "အသိအကျွမ်း",
      "ရွှေသဲ facebook page",
      "သူငယ်ချင်း",
    ],
    typework: ["စျေးရောင်း", "ကွန်ပျူတာစာရင်းကိုင်"],

    // img preview
    url_font: null,
    url_back: null,
    url_face: null,
    url_paper: null,

    answer: {
      name: "",
      birthday: "",
      sex: "",
      status: "",
      font: null,
      back: null,
      face: null,

      phone: "",
      facebook: "",

      education: null,
      schoolname: "",
      level: "",
      branch: "",
      gpa: "",
      study_future: "",
      study_plan: "",

      experience: "",
      languages: [
        {
          language: "",
          speak: "",
          read: "",
          write: "",
        },
      ],

      typework: "",
      storeplace: "",
      expectedSalary: "",
      KnowShwethe: "",
    },
    img: {},
  }),

  methods: {
    // submit
    onSubmit(evt) {
      evt.preventDefault();

      this.loading = true;
      HTTP.post("/form_job_insert", this.answer)
        .then(() => {
          console.log("SUCCESS!!");
          this.$router.push("/page/success");
        })
        .catch(() => {
          alert("FAILURE!!");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // img preview
    onFileChange_font: function(event) {
      var input = event.target;

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = (e) => {
          this.img.font_base64 = e.target.result;
          this.answer.font = reader.result.split(",")[1];
        };

        reader.readAsDataURL(input.files[0]);
      }
    },
    onFileChange_back: function(event) {
      var input = event.target;

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = (e) => {
          this.img.back_base64 = e.target.result;
          this.answer.back = reader.result.split(",")[1];
        };

        reader.readAsDataURL(input.files[0]);
      }
    },
    onFileChange_face: function(event) {
      var input = event.target;

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = (e) => {
          this.img.face_base64 = e.target.result;
          this.answer.face = reader.result.split(",")[1];
        };

        reader.readAsDataURL(input.files[0]);
      }
    },

    addExperience() {
      this.answer.languages.push({
        language: "",
        speak: "",
        read: "",
        write: "",
      });
    },
  },

  created() {
    window.onbeforeunload = function() {
      return "Are you sure want to leave?";
    };
  },
};
</script>

<style lang="scss" scoped>
/* bg-image */
.myDiv {
  position: relative;
  z-index: 1;
  background-color: white;
}
.myDiv .bg {
  position: absolute;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-image: url("~@/assets/vectorstock_7808629.jpg");
  opacity: 0.015;
  width: 100%;
  height: 100%;
}

.p_title {
  font-size: 30px;
  margin: 3% 0;
}

.img {
  width: 50%;
  height: 15vh;
  cursor: pointer;
  border-radius: 4px;
  border: 3px solid white;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
}

@media only screen and (max-width: 600px) {
  .p_title {
    text-align: center;
    font-size: 25px;
    margin: 3% 0;
    // border: 3px solid green;
  }

  footer {
    width: 640px;
    position: fixed;
    bottom: 0px;
    left: 50%;
    margin-left: -320px;
  }
}
</style>
