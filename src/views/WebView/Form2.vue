<template>
  <v-app>
    <div class="myDiv">
      <div class="bg"></div>
      <v-container>
        <p class="text-uppercase p_title">Employment application</p>

        <v-form
          ref="Form"
          @submit.prevent="submitForm"
          v-model="valid"
          autocomplete="off"
        >
          <v-card outlined>
            <v-card-text>
              <v-row>
                <v-subheader>ကိုယ်ပိုင်သတင်းအချက်အလက်များ</v-subheader>
              </v-row>
              <v-row>
                <v-col>
                  <b-input
                    type="text"
                    placeholder="aaa"
                    v-model="aaa"
                    ref="domainInput"
                  />
                </v-col>
                <v-col cols="12" sm="12" md="6">
                  <v-text-field
                    v-model="answer.name"
                    :rules="required.any"
                    label="အမည်"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" sm="12" md="6">
                  <v-dialog
                    ref="menu"
                    v-model="menu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="answer.birthday"
                        :rules="required.any"
                        label="မွေးသက္ကရာဇ်"
                        readonly
                        v-bind="attrs"
                        v-on="on"
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      ref="picker"
                      v-model="answer.birthday"
                      :max="new Date().toISOString().substr(0, 10)"
                      min="1950-01-01"
                      @change="save"
                    ></v-date-picker>
                  </v-dialog>
                </v-col>

                <v-col cols="12" sm="12" md="6">
                  <v-select
                    :items="sex"
                    label="လိင်"
                    v-model="answer.sex"
                    :rules="required.any"
                  ></v-select>
                </v-col>

                <v-col cols="12" sm="12" md="6">
                  <v-select
                    :items="status"
                    :rules="required.any"
                    label="အိမ်ထောင်ရှိ / မရှိ "
                    v-model="answer.status"
                  ></v-select>
                </v-col>

                <v-col cols="12" sm="12" md="6">
                  <v-text-field
                    v-model="answer.phone"
                    :rules="required.phone"
                    :counter="11"
                    label="ဖုန်းနံပါတ်"
                    type="number"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" sm="12" md="6">
                  <v-text-field
                    v-model="answer.email"
                    :rules="required.email"
                    label="အီးမေးလ်"
                    type="email"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" sm="12" md="12">
                  <v-textarea
                    label="လိပ်စာ"
                    auto-grow
                    rows="1"
                    v-model="answer.address"
                    :rules="required.any"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
          <br />
          <v-card outlined>
            <v-card-text>
              <v-row>
                <v-subheader>နိုင်ငံသားမှတ်ပုံတင်</v-subheader>
              </v-row>
              <v-row>
                <v-col cols="12" sm="12" md="6">
                  <v-text-field
                    v-model="answer.idcard"
                    :rules="required.any"
                    label="နိုင်ငံသားမှတ်ပုံတင်အမှတ်"
                  ></v-text-field>
                </v-col>

                <v-col>
                  <input
                    type="file"
                    @change="previewImage"
                    accept="image/*"
                    capture
                  />
                  <div v-if="answer.imageData.length > 0">
                    <img class="img" :src="answer.imageData" />
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>

          <v-divider></v-divider>

          <!-- <template>
          <div v-for="(language_, index) in answer.languages" :key="index">
            <b-row>
              <b-col cols="12">
                <b-form-group label="ဘာသာစကား :">
                  <b-form-input
                    v-model="language_.language"
                    :name="`languages[${index}][language]`"
                    autocomplete="off"
                  ></b-form-input>
                </b-form-group>
              </b-col>
              <b-col>
                <b-form-group>
                  <b-form-select v-model="language_.speak">
                    <option value="" selected disabled>ပြော</option>
                    <option>ကျွမ်းကျင်သူ</option>
                    <option>ကောင်းသော</option>
                    <option>အနည်းငယ်</option>
                    <option>မကောင်းဘူး</option>
                  </b-form-select>
                </b-form-group>
              </b-col>
              <b-col>
                <b-form-group>
                  <b-form-select v-model="language_.read">
                    <option value="" selected disabled>ဖတ်</option>
                    <option>ကျွမ်းကျင်သူ</option>
                    <option>ကောင်းသော</option>
                    <option>အနည်းငယ်</option>
                    <option>မကောင်းဘူး</option>
                  </b-form-select>
                </b-form-group>
              </b-col>
              <b-col>
                <b-form-group>
                  <b-form-select v-model="language_.write">
                    <option value="" selected disabled>ရေး</option>
                    <option>ကျွမ်းကျင်သူ</option>
                    <option>ကောင်းသော</option>
                    <option>အနည်းငယ်</option>
                    <option>မကောင်းဘူး</option>
                  </b-form-select>
                </b-form-group>
              </b-col>
            </b-row>
          </div>
          <div class="h5 mb-0">
            <b-icon icon="plus-square" @click="addExperience"></b-icon>
          </div>
        </template> -->

          <footer>
            <v-btn
              large
              type="submit"
              block
              color="primary"
              :loading="loading"
              :disabled="loading"
              @click="loader = 'loading'"
              >Submit</v-btn
            >
          </footer>
        </v-form>
        <!-- <pre class="m-0">{{ answer }}</pre> -->
      </v-container>
    </div>
  </v-app>
</template>

<script>
import { HTTP } from "../../plugins/http";

export default {
  data: () => ({
    // alert empty input
    errors: [],
    valid: true,

    // loading submitForm
    loader: null,
    loading: false,

    // date picker
    menu: false,

    // select option
    sex: ["ကျား", "‍မ"],
    status: ["ရှိ", "မရှိ"],

    // required Rules
    required: {
      any: [(v) => !!v || "this fill is required"],
      phone: [
        (v) => !!v || "this fill is required",
        (v) => (v && v.length <= 11) || "must be 11 number",
      ],
      email: [
        (v) => !!v || "E-mail is required",
        (v) => /.+@.+\..+/.test(v) || "E-mail must be valid",
      ],
    },

    // array answer
    answer: {
      name: "",
      birthday: "",
      sex: "",
      status: "",
      phone: "",
      email: "",
      address: "",

      idcard: "",
      imageData: "",
      font: null,
      back: null,
      face: null,

      education: "",
      schoolname: "",
      level: "",
      branch: "",
      gpa: "",
      paper: null,

      languages: [
        {
          language: "",
          speak: "",
          read: "",
          write: "",
        },
      ],

      typework: "",
      storeplace: "",
      expectedSalary: "",
      KnowShwethe: "",
      img: [
        {
          // image: null
        },
      ],
      aaa: "",
    },
  }),

  watch: {
    menu(val) {
      val && setTimeout(() => (this.$refs.picker.activePicker = "YEAR"));
    },
  },

  methods: {
    focusInput: function(inputRef) {
      // $refs is an object that holds the DOM references to your inputs
      this.$refs[inputRef].focus();
    },

    submitForm: function() {
      this.errors = [];
      if (
        (!this.answer.name,
        !this.answer.birthday,
        !this.answer.sex,
        !this.answer.status,
        !this.answer.phone,
        !this.answer.email,
        !this.answer.address)

        // (!this.answer.aaa)
        
      )
              alert("Please fill in the required fields");
      // this.focusInput("domainInput");


      if (this.$refs.Form.validate()) {
        this.loading = true;
        HTTP.post("form_job_insert", this.answer)
          .then(function(response) {
            console.log(response);
          })
          .catch(function(error) {
            console.log(error);
          })
          .finally(() => {
            this.loading = false;
            this.$refs.Form.reset();
            // alert("Success");
          });
      }
    },
    save(birthday) {
      this.$refs.menu.save(birthday);
    },

    addExperience() {
      this.answer.languages.push({
        language: "",
        speak: "",
        read: "",
        write: "",
      });
    },

    previewImage: function(event) {
      var input = event.target;

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = (e) => {
          this.answer.imageData = e.target.result;
        };

        reader.readAsDataURL(input.files[0]);
      }
    },
  },

  created() {
    window.onbeforeunload = function() {
      return "Are you sure want to leave?";
    };
  },
};
</script>

<style lang="scss" scoped>
.p_title {
  font-size: 30px;
  margin: 3% 0;
}

.img {
  width: 50%;
  height: 15vh;
  cursor: pointer;
  border-radius: 4px;
  border: 3px solid white;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
}

/* bg-image */
.myDiv {
  position: relative;
  z-index: 1;
  background-color: white;
}
.myDiv .bg {
  position: absolute;
  z-index: -1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-image: url("~@/assets/vectorstock_7808629.jpg");
  opacity: 0.02;
  width: 100%;
  height: 100%;
}

@media only screen and (max-width: 600px) {
  .p_title {
    font-size: 20px;
    margin: 3% 0;
  }

  footer {
    width: 640px;
    position: fixed;
    bottom: 0px;
    left: 50%;
    margin-left: -320px;
  }
}
</style>
