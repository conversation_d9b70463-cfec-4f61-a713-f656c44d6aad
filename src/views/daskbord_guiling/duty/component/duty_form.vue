<template>
  <v-app>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            duty
            <v-spacer></v-spacer>
          </v-card-title>
          <v-form ref="form" v-model="valid" autocomplete="off">
            <v-container>
              <v-row>
                <v-col cols="6">
                  <v-text-field
                    label="ke bian name"
                    v-model="answer.ke_bian_name"
                    @keyup="_uppercase_5"
                    @keyup.enter="uppercase_5"
                    outlined
                    :rules="rules.lock"
                    ref="ke_bian_name"
                    autofocus
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    label="ke bian name"
                    v-model="ke_bian_mmname"
                    outlined
                    :rules="rules.lock"
                    disabled
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6">
                  <v-text-field
                    label="fee_kyat"
                    v-model.number="answer.data_jsonb.fee_kyat"
                    outlined
                    :rules="rules.lock_2"
                    ref="fee_kyat"
                    type="number"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    label="fee_bath"
                    v-model.number="answer.data_jsonb.fee_bath"
                    outlined
                    :rules="rules.lock_2"
                    ref="fee_bath"
                    type="number"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6">
                  <v-text-field
                    label="kyat"
                    v-model.number="answer.data_jsonb.kyat"
                    outlined
                    :rules="rules.lock_2"
                    ref="kyat"
                    type="number"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    label="dollar"
                    v-model.number="answer.data_jsonb.dollar"
                    outlined
                    :rules="rules.lock_2"
                    ref="dollar"
                    type="number"
                  >
                  </v-text-field>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6">
                  <v-menu
                    ref="menu1"
                    v-model="menu1"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    max-width="290px"
                    min-width="auto"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="answer.data_jsonb.date"
                        label="Date"
                        hint="MM/DD/YYYY format"
                        persistent-hint
                        v-bind="attrs"
                        @blur="date = parseDate(dateFormatted)"
                        v-on="on"
                        :rules="rules.lock"
                        outlined
                      ></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="answer.data_jsonb.date"
                      no-title
                      @input="menu1 = false"
                      :rules="rules.lock"
                    ></v-date-picker>
                  </v-menu>
                </v-col>
                <v-col cols="6">
                  <v-select
                    label="company_name"
                    :items="items"
                    item-text="company_name"
                    item-value="company_id"
                    v-model="answer.items_2"
                    :rules="rules.lock"
                    @change="onChange"
                    return-object
                    outlined
                  ></v-select>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <v-btn
                    @click="submitCar"
                    :disabled="!valid"
                    color="success"
                    class="mr-4"
                  >
                    confirm
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
        </v-card>
      </b-col>
    </b-row>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
    <b-row>
      <b-col md="12" sm="12">
        <v-data-table
          :footer-props="{
            'items-per-page-options': [10, 20, 30, 40, 50],
          }"
          :headers="headers"
          :items="table_list"
          :sort-by="['', '']"
          multi-sort
        >
          <template v-slot:item.edit="{ item }">
            <v-btn small color="primary" @click="editItem(item)">View</v-btn>
          </template>
        </v-data-table>
      </b-col>
    </b-row>
    <!-- <pre>{{ this.answer }}</pre>
    <pre>{{ this.items_2 }}</pre> -->
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Oillllllllllllllllllll
            <v-spacer></v-spacer>
            <!-- <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
            ></v-text-field> -->
          </v-card-title>
          <v-form ref="form_2" v-model="valid_2" autocomplete="off">
            <v-container>
              <v-row>
                <v-col cols="5">
                  <v-text-field
                    v-model="item_bill_id"
                    outlined
                    ref="che_liang"
                    disabled
                    :rules="rules.lock"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="5">
                  <v-text-field
                    label="duty id"
                    v-model="duty_id"
                    outlined
                    :rules="rules.lock"
                    ref="che_liang"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="2">
                  <v-btn
                    @click="summit_duty_id"
                    :disabled="!valid_2"
                    color="success"
                    class="mr-4"
                  >
                    confirm
                  </v-btn>
                </v-col>
              </v-row>
            </v-container>
          </v-form>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers_3"
            :items="desserts_3"
            :sort-by="['', '']"
            multi-sort
          >
            <!-- <template v-slot:item.edit="{ item }">
              <v-btn small color="primary" @click="editItem(item)">View</v-btn>
            </template> -->
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
  </v-app>
</template>

<script>
import { HTTP_2, HTTP } from "../../../../plugins/http";

export default {
  data: (vm) => ({
    checK_idname: false,
    bill_id___: null,
    data_api_post: {},
    valid: false,
    valid_2: false,
    duty_id: null,
    overlay: false,

    delete: {},
    headers_3: [{ text: "duty_id", value: "duty_id" }],
    headers: [
      // { text: "id", value: "id" },

      { text: "bill_id", value: "bill_id" },
      { text: "data.company_name", value: "data.company_name" },
      { text: "bill_id", value: "edit" },
    ],
    table_list: [],
    rules: {
      lock: [(v) => !!v || "required"],
      lock_2: [
        (v) => !!v || "required",
        (v) => v >= 0 || "required",
        (v) => !!v == Number.isFinite(v) || "Must be integer",
      ],
    },

    date: new Date().toISOString().substr(0, 10),
    dateFormatted: vm.formatDate(new Date().toISOString().substr(0, 10)),
    menu1: false,
    menu2: false,
    ke_bian_mmname: null,

    answer: {
      ke_bian: "",
      uid: 0,
      bill_id: 0,
      data_jsonb: {
        kyat: 0.01,
        dollar: 0.01,
        fee_kyat: null,
        fee_bath: null,
        date: new Date().toISOString().substr(0, 10),
        company_id: null,
        company_name: "",
      },
    },

    items: [],
    items_2: [],
    item_bill_id: null,
    // headers_3: [],
    desserts_3: [],
  }),

  computed: {
    computedDateFormatted() {
      return this.formatDate(this.date);
    },
  },
  watch: {
    date() {
      this.dateFormatted = this.formatDate(this.date);
    },
  },
  methods: {
    async editItem(item) {
      console.log(item);
      // console.log(item);
      this.item_bill_id = item.bill_id;
      console.log(this.item_bill_id);
      await HTTP_2.get("shwethe_duty/api/v1/duty_by", {
        params: {
          ID: this.item_bill_id,
        },
      }).then((response) => (this.desserts_3 = JSON.parse(response.data)));
    },

    onChange(a) {
      this.answer.data_jsonb.company_id = a.company_id;
      this.answer.data_jsonb.company_name = a.company_name;
      console.log(a.company_id);
    },
    formatDate(date) {
      if (!date) return null;

      const [year, month, day] = date.split("-");
      return `${month}/${day}/${year}`;
    },
    parseDate(date) {
      if (!date) return null;

      const [month, day, year] = date.split("/");
      return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    },

    async submitCar() {
      this.overlay = true;
      await HTTP_2.post(`shwethe_duty/api/v1/duty`, this.answer).then(
        (response) => {
          console.log(response);
        }
      );
      location.reload();
    },
    async uppercase_5() {
      this.checK_idname = true;
      console.log("this.answer.ke_bian");
      this.answer.ke_bian_name = this.answer.ke_bian_name.toUpperCase();
      console.log("AAAAAAAAAAAAA");
      await HTTP.get("shwethe_n/api/v1/jia_yi_name", {
        params: {
          ID: this.answer.ke_bian_name,
        },
      }).then(
        (response) => (this.che_liang_mmname = JSON.parse(response.data))
      );
      if (this.che_liang_mmname == false) {
        console.log(1);
        this.ke_bian_mmname = null;
      } else {
        this.$refs.fee_kyat.focus();
        console.log(2);
        this.answer.ke_bian = this.che_liang_mmname[0].id;
        this.ke_bian_mmname = this.che_liang_mmname[0].mm_name;
        console.log(this.che_liang_mmname);
      }
    },
    _uppercase_5() {
      console.log("1");
      if (this.checK_idname != false) {
        this.$refs.form.reset();
        this.checK_idname = false;
      } else {
        console.log("1");
      }
    },

    async summit_duty_id() {
      var id_random = Date.now().toString();
      console.log("summit_duty_id");
      console.log(this.duty_id);
      console.log(this.item_bill_id);
      console.log(id_random);

      await HTTP_2.put("shwethe_duty/api/v1/duty_by", {
        bill_id: this.item_bill_id,
        data_jsonb: [
          {
            duty_random_id: id_random,
            duty_id: this.duty_id,
          },
        ],
      })
        .then(function() {})
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
        });
      await HTTP_2.get("shwethe_duty/api/v1/duty_by", {
        params: {
          ID: this.item_bill_id,
        },
      }).then((response) => (this.desserts_3 = JSON.parse(response.data)));
    },
  },
  async created() {
    await HTTP_2.get("shwethe_duty/api/v1/compapy").then(
      (response) => (
        console.log(response.data), (this.items = JSON.parse(response.data))
      )
    );
    //  console.log(localStorage.getItem("uid"))
    var _id_ = Date.now().toString();
    // this._id = _id_;
    this.answer.bill_id = localStorage.getItem("uid") + _id_;
    this.answer.uid = localStorage.getItem("uid");

    await HTTP_2.get("shwethe_duty/api/v1/duty").then(
      (response) => (
        console.log(response.data),
        (this.table_list = JSON.parse(response.data))
      )
    );
  },
};
</script>
