<template>
  <v-app class="app">
    <v-btn @click="print" icon color="primary">
      <v-icon>print</v-icon>
    </v-btn>
    <v-card id="printMe">
      <v-card-title>
        Order Items
        {{ headers }}
        <v-spacer></v-spacer>
        <v-text-field
          v-model="search"
          append-icon="mdi-magnify"
          label="Search"
          single-line
          hide-details
        ></v-text-field>
      </v-card-title>
      <v-data-table
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50],
        }"
        :search="search"
        :headers="filteredHeaders"
        :items="filteredDesserts"
        multi-sort
      ></v-data-table>
    </v-card>
  </v-app>
</template>

<script>
import { HTTP } from "../../../plugins/http";

export default {
  data() {
    return {
      output: null,
      search: "",
      headers: [],
      desserts: [],
      columnsToDrop: [
        "parner_mm_name",
        "parner_id",
        "parner_idname",
        "FK",
        "min qty",
        "max qty ordered",
        "fixed day",
      ], // Specify columns to drop here
    };
  },
  computed: {
    filteredHeaders() {
      return this.headers.filter((header) => !this.columnsToDrop.includes(header.value));
    },
    filteredDesserts() {
      return this.desserts.map((item) => {
        let filteredItem = {};
        for (let key in item) {
          if (!this.columnsToDrop.includes(key)) {
            filteredItem[key] = item[key];
          }
        }
        return filteredItem;
      });
    },
  },
  methods: {
    print() {
      // Pass the element id here
      this.$htmlToPaper("printMe");
    },
  },
  created() {
    HTTP.get(`order_item_list_columns`).then((response) => {
      this.headers = response.data;
    });
    HTTP.get(`order_item_list`).then((response) => {
      this.desserts = response.data;
    });
  },
};
</script>

<style lang="scss" scoped>
// .app {
//   margin: 5% 10%;
// }
// @media only screen and (max-width: 1832px) {
//   .app {
//     margin: 5% 1%;
//   }
// }
</style>
