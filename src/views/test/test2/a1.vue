<template>
  <v-app>
    <v-container>
      <div id="app">
        <v-app id="inspire">
          <v-form ref="form" @submit.prevent="submitForm" v-model="valid">
            <v-container>
              <v-row>
                <v-col>
                  <v-text-field value="aa"></v-text-field>
                </v-col>
                <v-col>
                  Value: {{ answer }}
                  <v-autocomplete
                    label="Shop"
                    :item-text="itemText"
                    :item-value="value"
                    :items="items"
                    :v-model="vmodel"
                    :rules="rules.numberRules"
                    filled
                    clearable
                  >
                  </v-autocomplete>
              
                </v-col>
              </v-row>
              <v-row>
                <v-btn type="submit" :disabled="!valid" color="success"
                  >Submit</v-btn
                >
              </v-row>
            </v-container>
          </v-form>
        </v-app>
      </div>
    </v-container>
  </v-app>
</template>

<script>
export default {
  data: () => ({
    // required: {
    //   type: Boolean,
    //   default: false
    // },
    itemsaaa: ["foo", "bar", "fizz", "buzz"],
    valuesaaa: ["foo", "bar"],

    valid: false,

    items: [{ nern: "a", test: "11", test222: "q" }],

    vmodel: [],

    answer: {
      value: "",
      aaa: "",
      input_2: "",
      texts: [{ input_3: "", input_4: "" }]
    },

    user: {
      number: ""
    },

    rules: {
      numberRules: [
        v => !!v || "Field is required"
        // v => /^\d+$/.test(v) || "Must be a number"
      ]
    }
  }),

  methods: {
    itemText(item) {
      return `${item.nern} | ${item.test} | ${item.test222}`;
      // console.log(`${item.nern} | ${item.test} | ${item.test222}`);
    },
    value(item) {
      return `${item.nern}`;
      // console.log(item.nern);
    },
    // submitForm() {
    //   let self = this;
    //   setTimeout(function() {
    //     if (self.$refs.form.validate()) {
    //       //other codes
    //       alert("submitted");
    //     }
    //   });
    // }
    // submitForm() {
    //   if (this.$refs.form.validate()) {

    //     alert("submitted");
    //   }32
    // }
    submitForm() {
      this.$refs.form.validate();

      alert("submitted");
    }
  }
};
</script>
