<template>
  <v-container>
    <v-card>
      <v-card-title>
        Category
        <v-spacer></v-spacer>
        <v-text-field v-model="search" append-icon="search" label="Search" single-line hide-details></v-text-field>
      </v-card-title>
      <v-data-table
        :headers="headers"
        :items="desserts"
        :search="search"
        :footer-props="{
          'items-per-page-options': [10, 20, 30, 40, 50],
        }"
        :sort-by="[]"
        multi-sort
      >
        <template v-slot:top>
          <v-dialog v-model="dialog" max-width="30%">
            <v-card>
              <v-card-title class="headline"></v-card-title>

              <v-card-text>
                <p>
                  <b>{{ editedItem.idname }} | {{ editedItem.mm_name }}</b>
                </p>

                <v-form ref="form" v-model="valid" @submit.prevent="submitForm">
                  <v-select
                    :items="shwetheOption"
                    item-text="shwethe"
                    item-value="shwetheValue"
                    v-model="answer.located"
                    :rules="rules.lock"
                    filled
                    label="Shwethe"
                  ></v-select>

                  <v-btn :disabled="!valid" color="success" class="mr-4" type="submit">Save</v-btn>
                </v-form>

                <!-- <pre>{{ answer }}</pre> -->
              </v-card-text>
            </v-card>
          </v-dialog>
        </template>

        <template v-slot:item.edit="{ item }">
          <v-btn small color="primary" @click="editItem(item)">Open</v-btn>
        </template>
      </v-data-table>
    </v-card>
  </v-container>
</template>

<script>
import { HTTP } from "../../../plugins/http";

export default {
  data: () => ({
    valid: false,
    dialog: false,

    // table
    search: "",
    headers: [
      { text: "id", value: "id" },
      { text: "idname", value: "idname" },
      { text: "mm_name", value: "mm_name" },
       { text: "d_name", value: "d_name" },
      { text: "Edit", value: "edit" }
    ],
    desserts: [],

    // require
    rules: {
      lock: [v => !!v || "Field is required"]
    },

    // selectOption
    shwetheOption: [
      { shwethe: "အကွေး(ပြာ)", shwetheValue: "အကွေး(ပြာ)" },
      { shwethe: "အကွေး(စိမ်း)", shwetheValue: "အကွေး(စိမ်း)" },
      { shwethe: "ကြွေပြာ", shwetheValue: "ကြွေပြာ" }
    ],

    // array answer
    answer: {
      product_id: "",
      located: ""
    },

    // Match id
    editedItem: {
      id: "",
      idname: "",
      mm_name: ""
    }
  }),

  created: function() {
    HTTP.get("/product_detial/category").then(
      response => (console.log(response.data), (this.desserts = response.data))
    );
  },

  methods: {
    editItem(item) {
      this.dialog = true;
      this.editedItem = Object.assign({}, item);

      HTTP.post(`/product_detial/category`, {}).then(response => {
        response;
        this.answer.product_id = this.editedItem.id;
      });
    },

    submitForm() {
      this.$refs.form.validate();

      HTTP.post("/product_detial_insert_category", this.answer)
        .then(function() {
          alert("SUCCESS!!");
        })
        .catch(function() {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.form.reset();
          this.dialog = false;
          HTTP.get("/product_detial/category").then(
            response => (
              console.log(response.data), (this.desserts = response.data)
            )
          );
        });
    }
  }
};
</script>
