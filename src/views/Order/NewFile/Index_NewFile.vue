<template>
  <v-app>
    <!-- <v-container> -->
    <div class="app">
      <v-row>
        <v-col cols="12"> <Form /></v-col>
        <v-col cols="12"> <Table /></v-col>
        <!-- <v-col cols="12"> <Table2 /></v-col> -->
      </v-row>
    </div>
    <!-- </v-container> -->
  </v-app>
</template>

<script>
import Form from "../NewFile/component/Form";
import Table from "../NewFile/component/Table";
// import Table2 from "../NewFile/component/Table2";
export default {
  components: {
    Form,
    Table,
    // Table2,
  },
};
</script>
