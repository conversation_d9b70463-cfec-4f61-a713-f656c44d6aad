<template>
  <div>
    <b-row>
      <b-col md="4" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Customers
            <v-spacer></v-spacer>
            <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
            ></v-text-field>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :search="search"
            :headers="headers_1"
            :items="table_list_1"
            multi-sort
          >
            <template v-slot:item.edit="{ item }">
              <v-btn small color="primary" @click="editItem(item)">View</v-btn>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
      <b-col md="8" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Items
            <v-spacer></v-spacer>
            <v-select
              :items="items_select"
              @change="select_click()"
              :disabled="select_disabled"
              v-model="module_select"
              label="Standard"
            ></v-select>
            <v-spacer></v-spacer>
            <v-text-field
              v-model="bill_id_value"
              label="bill id"
              solo
              :disabled="text_fild"
              @keyup="bill_id_enter()"
            ></v-text-field>
          </v-card-title>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers"
            :items="table_list"
            multi-sort
          >
            <template v-slot:top>
              <v-dialog v-model="dialog" max-width="500px">
                <v-card>
                  <v-card-text>
                    <v-container>
                      <h4>{{ editedItem.idname }}</h4>

                      <v-form ref="form" v-model="valid" autocomplete="off">
                        <v-row>
                          <v-col cols="12">
                            <v-text-field
                              v-model.number="editedItem.product_qty"
                              label="product_qty"
                              disabled
                            ></v-text-field>
                          </v-col>
                        </v-row>
                        <v-row>
                          <v-col cols="12">
                            <v-text-field
                              v-model.number="conframe_price"
                              label="conframe price"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-form>
                    </v-container>
                    <div v-for="(text, index) in price_datable" :key="index">
                      <v-row>
                        <v-col cols="3">
                          <v-text-field
                            v-model="text.type"
                            label="Qty"
                            filled
                            disabled
                          ></v-text-field>
                        </v-col>
                        <v-col cols="3">
                          <v-text-field
                            v-model="text.fen"
                            label="Qty"
                            type="number"
                            filled
                            disabled
                          ></v-text-field>
                        </v-col>
                        <v-col cols="3">
                          <v-text-field
                            v-model="text.qty"
                            label="Qty"
                            type="number"
                            filled
                            disabled
                          ></v-text-field>
                        </v-col>
                        <v-col cols="3">
                          <v-text-field
                            v-model="text.qty_1"
                            label="Qty"
                            type="number"
                            filled
                            @click="click_number"
                            @keyup="click_number"
                          ></v-text-field>
                        </v-col>
                        <!-- <v-col cols="2">
                                <v-btn
                                  @click="deleteInput(index)"
                                  class="mx-2"
                                  fab
                                  x-small
                                  dark
                                  color="error"
                                >
                                  <v-icon dark>mdi-minus</v-icon>
                                </v-btn>
                              </v-col> -->
                      </v-row>
                    </div>
                  </v-card-text>

                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="blue darken-1" text @click="close">
                      Cancel
                    </v-btn>
                    <v-btn
                      color="blue darken-1"
                      text
                      :disabled="valid"
                      @click="save"
                    >
                      Save
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-dialog>
            </template>
            <template v-slot:item.edit_2="{ item }">
              <v-icon class="mr-2" :disabled="mdi" @click="editItem_2(item)">
                mdi-pencil
              </v-icon>
            </template>
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>
    <b-row>
      <b-col md="12" sm="12">
        <v-card id="printMe">
          <v-card-title>
            Confirm product QTY
            <v-spacer></v-spacer>
          </v-card-title>
          <v-form ref="form" v-model="valid_2" autocomplete="off">
            <v-container>
              <v-row>
                <v-col cols="5">
                  <v-text-field
                    label="kebian_idname"
                    v-model="kebian_idname"
                    @keyup="_kebian_5"
                    @keyup.enter="kebian_5"
                    outlined
                    ref="kebian_idname"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="5">
                  <v-text-field
                    label="name"
                    v-model="kebian_mmname"
                    outlined
                    :rules="rules.lock"
                    disabled
                    ref="kebian_mmname"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="2">
                  <v-btn
                    @click="submitCar"
                    :disabled="!valid_2"
                    color="success"
                    class="mr-4"
                  >
                    confirm
                  </v-btn>
                </v-col>
              </v-row>
              <!-- <v-row>
                <v-col cols="5">
                  <v-text-field
                    label="idname"
                    v-model="che_liang"
                    @keyup="_uppercase_5"
                    @keyup.enter="uppercase_5"
                    outlined
                    :rules="rules.lock"
                    ref="che_liang"
                  >
                  </v-text-field>
                </v-col>
                <v-col cols="5">
                  <v-text-field
                    label="name"
                    v-model="che_liang_mmname"
                    outlined
                    :rules="rules.lock"
                    disabled
                     ref="che_liang_mmname"
                  >
                  </v-text-field>
                </v-col>
                
              </v-row> -->
            </v-container>
          </v-form>
          <v-data-table
            :footer-props="{
              'items-per-page-options': [10, 20, 30, 40, 50],
            }"
            :headers="headers_2"
            :items="table_list_2"
            :sort-by="['', '']"
            multi-sort
          >
          </v-data-table>
        </v-card>
      </b-col>
    </b-row>

    <!-- OVERLAY -->
    <template>
      <div class="text-center">
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
      </div>
    </template>
  </div>
</template>

<script>
import { HTTP_shwethe_n } from "../../../../plugins/http";
const alasql = require("alasql");

export default {
  data: () => ({
    conframe_price:'',
    text_fild: true,
    mdi: true,
    checK_kebian: false,
    kebian: null,
    kebian_idname: null,
    kebian_id: null,
    kebian_mmname: null,
    table_list_ori: [],
    module_select: null,
    select_disabled: true,
    items_select: ["NO", "N", "L"],
    search: "",
    checK_idname: false,
    overlay: false,
    che_liang: null,
    che_liang_id: null,
    che_liang_mmname: null,
    ke_bian: null,
    ke_bian_mmname: null,
    price_datable: null,
    headers_1: [
      { text: "idname", value: "idname" },
      { text: "mm_name", value: "mm_name" },
      { text: "views", value: "edit" },
    ],
    table_list_1: [],
    headers_2: [
      { text: "product_idname", value: "product_idname" },
      { text: "product_mm_name", value: "product_mm_name" },
      { text: "product_d_name", value: "product_d_name" },
      { text: "product_qty", value: "product_qty" },
      { text: "type_line", value: "type_line" },
      { text: "product_price", value: "product_price" },
    ],
    table_list_2: [],
    data_customer_valid: false,
    valid: true,
    valid_2: true,
    dialog: false,
    // Get api
    items_partner: [],
    items_car: [],
    items_oil_type: [],
    headers: [
      // { text: "id", value: "id" },
      { text: "datetime", value: "datetime" },
      { text: "product_idname", value: "product_idname" },
      { text: "product_mm_name", value: "product_mm_name" },
      { text: "product_d_name", value: "product_d_name" },
      { text: "product_qty", value: "product_qty" },
      { text: "product_price", value: "product_price" },
      { text: "type", value: "type" },
      { text: "lei_A", value: "lei_A" },
      { text: "lei_B", value: "lei_B" },
      { text: "1", value: "1" },
      { text: "2", value: "2" },
      { text: "id", value: "edit_2" },
    ],
    // require
    rules: {
      lock: [(v) => !!v || "required"],
      price: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      qty: [
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ],
      distant: [(v) => !!v || "required"],
    },
    _id: null,
    table_list: [],
    bill_id_value: "",
    // answer
    data_product: [{ mm_name: null }],
    data_customer: null,
    data_customer_mmname: null,
    answer_2: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    answer: {
      sname: "",
      car: "",
      oil: "",
      qty: "",
      price: "",
      distant: "",
    },
    recoud_table: null,
    alert: false,
    editedIndex: -1,
    editedItem: {
      idname: "",
      product_qty: "",
      qty_select: "",
    },
  }),

  computed: {
    emailConfirmationRules() {
      return [
        () =>
          this.editedItem.product_qty >= this.editedItem.qty_select ||
          "more than product_qty",
        (v) => !!v || "required",
        (v) => v >= 0.1 || "required",
        (v) => !!v == Number.isInteger(v) || "Must be integer",
      ];
    },
  },

  methods: {
    select_click() {
      console.log("AAAAAAAAAAAAA");

      console.log(this.module_select.toString);
      this.select_disabled = true;
      this.table_list_2 = [];
      this.che_liang = 0;
      this.kebian_idname = 0;
      this.mdi = false;
      if (this.module_select.toString() == "N") {
        this.valid_2 = true;
        this.kebian_mmname = 0;
      } else if (this.module_select.toString() == "NO") {
        this.text_fild = false;
        // this.module_select.toString() != "NO"
      } else {
        this.valid_2 = false;
        console.log(1);
      }
      var res4 = alasql(
        'SELECT * FROM ? where lei_B = "' +
          this.module_select.toString() +
          '"  and lei_B != "NO" ',
        [this.table_list_ori]
      );
      console.log(res4);

      this.table_list = res4;
    },
    bill_id_enter() {
      console.log(this.bill_id_value);
      var res4 = alasql(
        'SELECT * FROM ? where bill_id = "' +
          this.bill_id_value.toString() +
          '"  and lei_B == "NO" ',
        [this.table_list_ori]
      );
      console.log(res4);

      this.table_list = res4;
    },
    async submitCar() {
      if (this.module_select.toString() == "L") {
        // this.kebian_id = 0
      } else {
        this.kebian_id = 0;
      }
      if (this.table_list_2 == false) {
        console.log("log");
        alert("no data");
      } else {
        this.overlay = true;
        console.log("go");
        if (this.module_select.toString() == "L") {
          await HTTP_shwethe_n.put(
            "/api/v2/view_preproduct/view_preproduct_compls",
            {
              bill_id: this._id,
              che_liang: 0,
              ke_bian: this.kebian_id,
              lei_b: 24,
              data_: {
                status: "wait_sucess",
              },
            }
          )
            .then(function () {
              location.reload();
            })
            .catch(function () {
              alert("FAILURE!!");
            })
            .finally(() => {
              // location.reload();
            });
          // this.kebian_id = 0
        } else if (this.module_select.toString() == "N") {
          await HTTP_shwethe_n.put(
            "/api/v2/view_preproduct/view_preproduct_compls",
            {
              bill_id: this._id,
              che_liang: 0,
              ke_bian: this.kebian_id,
              lei_b: 41,
              data_: {
                status: "wait_sucess",
              },
            }
          )
            .then(function () {
              location.reload();
            })
            .catch(function () {
              alert("FAILURE!!");
            })
            .finally(() => {
              // location.reload();
            });
        } else {
          await HTTP_shwethe_n.put(
            "/api/v2/view_preproduct/view_preproduct_compls",
            {
              bill_id: this._id,
              che_liang: 0,
              ke_bian: this.kebian_id,
              lei_b: 49,
              data_: {
                status: "wait_sucess",
              },
            }
          )
            .then(function () {
              location.reload();
            })
            .catch(function () {
              alert("FAILURE!!");
            })
            .finally(() => {
              // location.reload();
            });

          // location.reload();
        }
      }
    },
    async kebian_5() {
      this.checK_idname = true;
      this.kebian_idname = this.kebian_idname.toUpperCase();
      console.log("AAAAAAAAAAAAA");
      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.kebian_idname,
        },
      }).then(
        (response) => (this.kebian = JSON.parse(response.data)),
        (this.checK_kebian = false)
      );
      if (this.che_liang_mmname == false) {
        console.log(1);
        this.kebian_id = null;
        this.kebian_mmname = null;
      } else {
        console.log(2);
        this.kebian_id = this.kebian[0].id;
        this.kebian_mmname = this.kebian[0].mm_name;
      }
    },
    async uppercase_5() {
      this.checK_idname = true;
      this.che_liang = this.che_liang.toUpperCase();
      console.log("AAAAAAAAAAAAA");
      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.che_liang,
        },
      }).then(
        (response) => (this.che_liang_mmname = JSON.parse(response.data))
      );
      if (this.che_liang_mmname == false) {
        console.log(1);
        this.che_liang_id = null;
        this.che_liang_mmname = null;
      } else {
        console.log(2);
        this.che_liang_id = this.che_liang_mmname[0].id;
        this.che_liang_mmname = this.che_liang_mmname[0].mm_name;
      }
    },
    _uppercase_5() {
      console.log("1");
      this.$refs.che_liang_mmname.reset();
    },
    _kebian_5() {
      console.log("1");
      this.$refs.kebian_mmname.reset();
    },
    async editItem_2(item) {
      this.conframe_price = ''
      // this.table_list = []
      this.price_datable = []
      this.text_fild =  true,
      console.log(Object.assign({}, item));
      this.editedIndex = this.table_list.indexOf(item);
      this.editedItem = Object.assign({}, item);
      //  this.editedItem = item
      this.dialog = true;
      console.log(item);
      console.log(item.product_id);

      await HTTP_shwethe_n.get(
        "/api/v1/view_preproduct/view_preproduct_qty",
        {
          params: {
            id: item.product_id,
          },
        }
      ).then((response) => (this.price_datable = JSON.parse(response.data)));
    },
    close() {
      this.dialog = false;
    },
    click_number() {
      console.log(this.price_datable);
      console.log(this.editedItem);
      this.valid = true;
      var res4 = alasql(
        "SELECT Idname,SUM(Convert(Float,qty_1)) AS qty FROM ? GROUP BY Idname",
        [this.price_datable]
      );
      console.log(this.editedItem.product_qty);
      if (res4[0].qty > -10000) {
        if (this.editedItem.product_qty < res4[0].qty) {
          this.valid = true;
          console.log(33);
        } else {
          this.valid = false;
          console.log(34);
        }
        console.log(1);
      } else {
        console.log(2);

        this.valid = true;
      }
      var res9 = alasql(
        "SELECT Idname as product_id,qty_1 AS product_qty,type as type_line,fen FROM ?  where qty_1 <> 0 ",
        [this.price_datable]
      );
      var item_2 = [this.editedItem];
      var res4444 = alasql(
        "SELECT yi_fang,product_d_name,product_id,product_idname,product_mm_name,product_price,bill_id,line FROM ? ",
        [item_2]
      );
      console.log(res9);
      console.log(res4444);
      console.log(res4444[0].product_price);
      console.log(this.valid);

      if(res4444[0].product_price == this.conframe_price && this.valid != true){
        console.log("Sucess")

      }else{
        console.log("un Sucess")
        this.valid = true;
      }


      var res = alasql(
        "SELECT  *  FROM ? res_1 inner JOIN ? res_2 ON res_1.product_id = res_2.product_id  ",
        [res9, res4444]
      );

      // const array3 = res9.concat(res4444);

      this.recoud_table = res;
      console.log(res);
    },
    async editItem_(item) {
      console.log(item);
      // var item_2 = item;
      // var res4 = alasql(
      //   "SELECT fen,type_line,product_d_name,product_id,product_idname,product_mm_name,product_price,qty_select as product_qty,bill_id,line FROM ? ",
      //   [item_2]
      // );
      // console.log(res4);

      await HTTP_shwethe_n.put(`/api/v4/view_preproduct/view_preproduct`, {
        bill_id: this._id,
        uid: localStorage.getItem("uid"),
        yi_fang: item[0].yi_fang,
        data_jsonb: item,
      }).then((response) => {
        console.log(response);
      });

      await HTTP_shwethe_n.get(
        `/api/v1/view_preproduct/view_preproduct_insert`,
        {
          params: {
            ID: this._id,
          },
        }
      ).then((response) => {
        console.log(response);
        this.table_list_2 = JSON.parse(response.data);
      });
      this.table_list.splice(this.editedIndex, 1);
    },
    save() {
      // this.table_list.splice(this.editedIndex, 1);
      this.close();
      this.valid = true;
      this.editItem_(this.recoud_table);
    },
    async editItem(item) {
      // this.conframe_price = ''
      this.bill_id_value= "",
      this.text_fild = true;
      this.mdi = true;
      this.module_select = null;
      this.table_list_2 = [];
      this.overlay = true;
      var _id_ = Date.now().toString();
      this._id = _id_;
      // this._id = 0;

      console.log(item.id);
      // this.table_list = []
      // this.table_list_ori = []
      await HTTP_shwethe_n.get(
        "/api/v5/view_preproduct/view_preproduct_detail",
        {
          params: {
            cus_id: item.id,
          },
        }
      ).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          (this.table_list_ori = JSON.parse(response.data)),
          (this.select_disabled = false),
          console.log(this.table_list)
        )
      );
      await HTTP_shwethe_n.post(`/api/v1/view_preproduct/view_preproduct`, {
        bill_id: this._id,
        uid: localStorage.getItem("uid"),
        yi_fang: item.id,
      }).then((response) => {
        console.log(response);
      });
      this.overlay = false;
    },
    async api_process() {
      console.log(this.data_customer);
      console.log("test");
      await HTTP_shwethe_n.put("/api/v1/pre_order_n_compls", {
        bill_id: this._id,
        yi_fang: this.data_customer[0].id,
        data_: {
          status: "sucess",
        },
      })
        .then(function () {
          location.reload();
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          // location.reload();
        });
    },
    submitForm_2() {
      this.alert = true;
      this.dialog = false;
      console.log("BBBBBB");
      console.log(this.data_customer[0].yi_fang);
      if (this.data_customer) {
        this.alert = false;
        this.api_process();
      } else {
        this.alert = true;
      }
    },
    async submitForm() {
      const alasql = require("alasql");
      console.log(this.data_product[0]);
      var res4 = alasql(
        "SELECT id as product_id,qty as product_qty,price as product_price ,mm_name as product_mm_name,d_name as product_d_name ,idname as product_idname FROM ?",
        [this.data_product]
      );
      console.log(res4[0]);
      this.$refs.form.validate();
      console.log("test");
      console.log("test");
      await HTTP_shwethe_n.put("/api/v1/pre_order_n", {
        uid: 0,
        bill_id: this._id,
        yi_fang: 0,
        lei_b: 0,
        data_jsonb: [res4[0]],
      })
        .then(function () {
          // alert("SUCCESS!!");
        })
        .catch(function () {
          alert("FAILURE!!");
        })
        .finally(() => {
          console.log(this.answer);
          this.$refs.form.reset();
        });
      await HTTP_shwethe_n.get("/api/v1/pre_order_n", {
        params: {
          ID: this._id,
        },
      }).then(
        (response) => (
          (this.table_list = JSON.parse(response.data)),
          console.log(this.table_list)
        )
      );
    },
    async uppercase() {
      console.log(this.answer.sname);
      await HTTP_shwethe_n.get("/api/v1/product", {
        params: {
          ID: this.answer.sname,
        },
      }).then(
        (response) => (
          (this.data_product = JSON.parse(response.data)),
          console.log(this.data_product)
        )
      );
    },
    async uppercase_2() {
      await HTTP_shwethe_n.get("/api/v1/jia_yi_name", {
        params: {
          ID: this.answer_2.sname,
        },
      }).then(
        (response) => (this.data_customer = JSON.parse(response.data))
        // (console.log(this.data_customer[0].mm_name))
      );
      this.data_customer_mmname = null;
      if (this.data_customer == false) {
        console.log(1);
        this.data_customer_mmname = null;
      } else {
        console.log(2);
        this.data_customer_mmname = this.data_customer[0].mm_name;
      }
    },
    itemText(item) {
      return `${item.id} | ${item.idname} | ${item.mm_name}`;
    },
    sname_value(item) {
      console.log(this.item);
      return `${item.id}`;
    },
  },
  async created() {
    console.log(localStorage.getItem("uid"));
    var _id_ = Date.now().toString();
    this._id = _id_;
    console.log(this._id);
    await HTTP_shwethe_n.get(`/api/v3/view_preproduct/view_preproduct`).then(
      (response) => {
        this.table_list_1 = JSON.parse(response.data);
      }
    );
  },
  // /api/v1/view_preproduct/view_preproduct_detail
};
</script>
