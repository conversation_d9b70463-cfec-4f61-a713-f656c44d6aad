<template>
  <v-container>
    <v-card>
      <v-tabs v-model="tab" background-color="primary" dark>
        <v-tab v-for="item in items" :key="item.tab">
          {{ item.tab }}
        </v-tab>
      </v-tabs>
      <v-tabs-items v-model="tab">
        <v-tab-item v-for="item in items" :key="item.tab">
          <v-card flat>
            <v-card-text>
              <component v-bind:is="item.content"></component>
            </v-card-text>
          </v-card>
        </v-tab-item>
      </v-tabs-items>
    </v-card>
  </v-container>
</template>

<script>
import ProductDetail from "./component/ProductDetails";
import Category from "./component/Category";

export default {
  components: {
    ProductDetail,
    Category,
  },
  data() {
    return {
      tab: null,

      items: [
        { tab: "ProductDetail", content: "ProductDetail" },
        { tab: "Category", content: "Category" },
      ],
    };
  },
};
</script>
